<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.cox.cms</groupId>
	<artifactId>cox-core-ui8</artifactId>
	<version>1.0-SNAPSHOT</version>

	<packaging>pom</packaging>
	<name>base</name>
	<repositories>
		<repository>
			<id>repo.corp.cox.com-jcenter</id>
			<name>repo.corp.cox.com-releases</name>
			<url>https://repo.corp.cox.com/artifactory/jcenter</url>
		</repository>
		<repository>
			<id>repo.corp.cox.com-mcentral</id>
			<name>repo.corp.cox.com-releases</name>
			<url>https://repo.corp.cox.com/artifactory/repo1</url>
		</repository>
		<repository>
			<id>central</id>
			<url>https://repo1.maven.org/maven2</url>
			<releases>
				<enabled>false</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
			<id>repo-plugins-release</id>
			<name>plugins-release</name>
			<url>https://repo.corp.cox.com/artifactory/plugins-release</url>
		</pluginRepository>
		<pluginRepository>
			<snapshots />
			<id>repo-plugins-snapshots</id>
			<name>plugins-snapshot</name>
			<url>https://repo.corp.cox.com/artifactory/plugins-snapshot</url>
		</pluginRepository>
		<pluginRepository>
			<id>central</id>
			<url>https://repo1.maven.org/maven2</url>
			<releases>
				<enabled>false</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

	<properties>
		<build.version>${env.MAJOR_VERSION}_${env.MINOR_VERSION}</build.version>
		<jenkins.version>${env.MAJOR_VERSION}.${env.MINOR_VERSION}.${env.BUILD_NUMBER}</jenkins.version>
		<assembly.skipAssembly></assembly.skipAssembly>
		<maven.install.skip></maven.install.skip>
		<env.cucumber_feature></env.cucumber_feature>
		<grunt.task></grunt.task>
	</properties>

	<build>
		<plugins>
			<plugin>
				<groupId>com.github.eirslett</groupId>
				<artifactId>frontend-maven-plugin</artifactId>
				<!-- NB! Set <version> to the latest released version of frontend-maven-plugin, like
				in README.md -->
				<version>1.15.0</version>
				<configuration>
					<nodeVersion>v20.14.0</nodeVersion>
					<npmVersion>10.7.0</npmVersion>
					<pnpmVersion>8.15.4</pnpmVersion>
				</configuration>
				<executions>
					<execution>
						<id>install node and ppm</id>
						<goals>
							<goal>install-node-and-pnpm</goal>
						</goals>
					</execution>
					<execution>
						<id>pnpm version</id>
						<goals>
							<goal>pnpm</goal>
						</goals>
						<!-- Optional configuration which provides for running any npm command -->
						<configuration>
							<arguments>version --new-version ${jenkins.version} --no-git-tag-version</arguments>
							<!-- <skip>true</skip> -->
						</configuration>
					</execution>
					<execution>
						<id>pnpm install</id>
						<goals>
							<goal>pnpm</goal>
						</goals>
						<!-- Optional configuration which provides for running any npm command -->
						<configuration>
							<arguments>install</arguments>
						</configuration>
					</execution>
					<execution>
						<id>pnpm run build</id>
						<goals>
							<goal>pnpm</goal>
						</goals>
						<configuration>
							<arguments>run build</arguments>
						</configuration>
					</execution>
					<execution>
						<id>pnpm run build-storybook</id>
						<goals>
							<goal>pnpm</goal>
						</goals>
						<configuration>
							<arguments>run build-storybook</arguments>
						</configuration>
					</execution>
				</executions>
			</plugin>
				<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.5.1</version>
				<executions>
				<execution>
						<id>make-bundles</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>${project.artifactId}</finalName>
							<descriptors>
								<descriptor>src/main/assembly.xml</descriptor>
							</descriptors>
							<attach>false</attach>
						</configuration>
					</execution>
					<execution>
						<id>make-prototype</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>${project.artifactId}.prototype</finalName>
							<descriptors>
								<descriptor>src/main/prototype.xml</descriptor>
							</descriptors>
							<attach>false</attach>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>