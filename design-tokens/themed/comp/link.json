{"link": {"text": {"breakpoint": {"xxl-xl": {"value": "{cox.text.interactive1}", "type": "typography"}, "lg-md": {"value": "{cox.text.interactive2}", "type": "typography"}, "sm-xs": {"value": "{cox.text.interactive3}", "type": "typography"}}}, "strong-background": {"default": {"value": "{interactive-accent-link-text.on-strong.foreground}", "type": "color"}, "disabled": {"value": "{interactive-accent-link-text.on-strong.disabled}", "type": "color"}, "hover": {"value": "{interactive-accent-link-text.on-strong.hover}", "type": "color"}}, "color": {"text": {"default": {"value": "{interactive-accent-link-text.on-default.foreground}", "type": "color"}, "hover": {"value": "{interactive-accent-link-text.on-default.hover}", "type": "color"}, "disabled": {"value": "{interactive-accent-link-text.on-default.disabled}", "type": "color"}}, "icon": {"default": {"value": "{interactive-accent-link-text.on-default.foreground}", "type": "color"}, "hover": {"value": "{interactive-accent-link-text.on-default.hover}", "type": "color"}, "disabled": {"value": "{interactive-accent-link-text.on-default.disabled}", "type": "color"}}}}}