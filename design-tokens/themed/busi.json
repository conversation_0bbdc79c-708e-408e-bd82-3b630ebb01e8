{"breadcrumb": {"previous": {"value": "{interactive-accent-link-text.on-default.foreground}", "type": "color", "description": "business previous item breadcrumb"}}, "progress-bar": {"default": {"loading-bar": {"value": "{color.blue.750}", "type": "color"}}}, "quantity-selector": {"color": {"number-of-lines": {"value": "{selector.color.business}", "type": "color"}, "list-item": {"text": {"value": "{color.neutral.600}", "type": "color"}, "text-hover": {"value": "{color.blue.750}", "type": "color"}, "background-active": {"value": "{color.blue.750}", "type": "color"}, "background-default": {"value": "{background.muted-2}", "type": "color", "description": "business active dropdown background"}}}}, "pagination-dots": {"active": {"business-border": {"value": "{selector.color.business}", "type": "color"}}, "inactive": {"business": {"value": "{selector.color.business}", "type": "color"}}}, "price": {"color": {"on-default": {"value": "{accent-pricing.business}", "type": "color"}}}, "pagination-arrows": {"background": {"default": {"value": "{background.muted-2}", "type": "color"}}, "border": {"default": {"value": "{foreground.on-default.4}", "type": "color"}}}, "interactive-accent-button": {"default": {"surface": {"value": "{color.blue.750}", "type": "color"}, "border": {"value": "{color.blue.750}", "type": "color"}}, "secondary": {"surface": {"value": "{color.neutral.100}", "type": "color"}}, "hover-active": {"surface": {"value": "{color.blue.900}", "type": "color"}, "border": {"value": "{color.blue.900}", "type": "color"}}, "disabled": {"surface": {"value": "{color.neutral.400}", "type": "color"}, "border": {"value": "{color.neutral.400}", "type": "color"}}, "text": {"default": {"value": "{color.blue.750}", "type": "color"}, "disabled": {"value": "{color.neutral.400}", "type": "color"}, "hover-active": {"value": "{color.blue.900}", "type": "color"}, "on-color-background": {"default": {"value": "{color.neutral.100}", "type": "color"}, "disabled": {"value": "{color.neutral.300}", "type": "color"}, "hover-active": {"value": "{color.blue.100}", "type": "color"}}}}, "button": {"primary": {"color": {"surface": {"default": {"value": "{interactive-accent-button.default.surface}", "type": "color"}, "disabled": {"value": "{interactive-accent-button.disabled.surface}", "type": "color"}, "hover-active": {"value": "{interactive-accent-button.hover-active.surface}", "type": "color"}}}}, "tertiary": {"color": {"text": {"default": {"value": "{interactive-accent-button.text.default}", "type": "color"}, "disabled": {"value": "{interactive-accent-button.text.disabled}", "type": "color"}, "hover-active": {"value": "{interactive-accent-button.text.hover-active}", "type": "color"}, "on-color-background": {"default": {"value": "{interactive-accent-button.text.on-color-background.default}", "type": "color"}, "disabled": {"value": "{interactive-accent-button.text.on-color-background.disabled}", "type": "color"}, "hover-active": {"value": "{interactive-accent-button.text.on-color-background.hover-active}", "type": "color"}}}}}, "secondary": {"color": {"border": {"default": {"value": "{interactive-accent-button.default.surface}", "type": "color"}, "disabled": {"value": "{interactive-accent-button.disabled.surface}", "type": "color"}, "hover-active": {"value": "{interactive-accent-button.hover-active.surface}", "type": "color"}, "on-color-background": {"default": {"value": "{interactive-accent-button.text.on-color-background.default}", "type": "color"}, "disabled": {"value": "{interactive-accent-button.text.on-color-background.disabled}", "type": "color"}, "hover-active": {"value": "{interactive-accent-button.text.on-color-background.hover-active}", "type": "color"}}}, "surface": {"value": "{interactive-accent-button.secondary.surface}", "type": "color"}}}}, "oliver": {"color": {"icon": {"value": "{background.gradient-busi}", "type": "color"}}}, "dropdown-comparison-card": {"default": {"price": {"value": "{accent-pricing.business}", "type": "color"}}, "hover-active": {"background": {"value": "{background.active-focus}", "type": "color"}}}, "background": {"active-focus": {"value": "{color.neutral.250}", "type": "color"}, "muted-1": {"value": "{color.neutral.200}", "type": "color"}, "gradient": {"value": "{color.gradient.busi}", "type": "color"}}, "interactive-tabs": {"default": {"active": {"value": "{color.blue.900}", "type": "color"}, "line": {"value": "{color.neutral.250}", "type": "color"}, "focused-background": {"value": "{color.neutral.250}", "type": "color"}, "inactive": {"value": "{color.neutral.500}", "type": "color"}}}, "tab": {"color": {"label": {"hover-on-default": {"value": "{interactive-tabs.default.active}", "type": "color"}, "deselected-on-default": {"value": "{interactive-tabs.default.inactive}", "type": "color"}, "hover-on-muted-1": {"value": "{interactive-tabs.default.active}", "type": "color"}, "deselected-on-muted-1": {"value": "{interactive-tabs.default.inactive}", "type": "color"}, "hover-on-muted-2": {"value": "{interactive-tabs.default.active}", "type": "color"}, "deselected-on-muted-2": {"value": "{interactive-tabs.default.inactive}", "type": "color"}, "hover-on-strong": {"value": "{interactive-tabs.on-strong.hover}", "type": "color"}, "deselected-on-strong": {"value": "{interactive-tabs.on-strong.deselected}", "type": "color"}, "hover-on-bold": {"value": "{interactive-tabs.on-bold.hover}", "type": "color"}, "deselected-on-bold": {"value": "{interactive-tabs.on-bold.deselected}", "type": "color"}, "hover-on-gradient": {"value": "{interactive-tabs.on-gradient.hover}", "type": "color"}, "deselected-on-gradient": {"value": "{interactive-tabs.on-gradient.deselected}", "type": "color"}}, "selected-on-default": {"value": "{interactive-tabs.default.active}", "type": "color"}, "selected-on-muted-1": {"value": "{interactive-tabs.default.active}", "type": "color"}, "selected-on-muted-2": {"value": "{interactive-tabs.default.active}", "type": "color"}, "selected-on-bold": {"value": "{interactive-tabs.on-bold.selected}", "type": "color"}, "selected-on-strong": {"value": "{interactive-tabs.on-strong.selected}", "type": "color"}, "selected-on-gradient": {"value": "{interactive-tabs.on-gradient.selected}", "type": "color"}, "focused-background": {"value": "{interactive-tabs.default.focused-background}", "type": "color"}}}, "tab-set": {"color": {"underline-on-default": {"value": "{interactive-tabs.default.line}", "type": "color"}, "underline-on-muted-1": {"value": "{utility.on-muted-1.2}", "type": "color"}, "underline-on-muted-2": {"value": "{utility.on-muted-2.2}", "type": "color"}, "underline-on-bold": {"value": "{interactive-tabs.default.line}", "type": "color"}, "underline-on-strong": {"value": "{interactive-tabs.default.line}", "type": "color"}, "underline-on-gradient": {"value": "{interactive-tabs.default.line}", "type": "color"}}}, "foreground": {"business": {"default": {"value": "{color.neutral.600}", "type": "color"}, "link-default": {"value": "{color.blue.750}", "type": "color"}, "text-default": {"value": "{color.blue.750}", "type": "color"}, "link-disabled": {"value": "{color.neutral.400}", "type": "color"}}}, "list-item": {"type": {"value": "{foreground.business.default}", "type": "color"}}, "link": {"default": {"value": "{foreground.business.link-default}", "type": "color"}, "disabled": {"value": "{foreground.business.link-disabled}", "type": "color"}}, "carousel-image": {"list-item": {"vertical-line": {"color": {"active": {"value": "{selector.color.business}", "type": "color"}}}}}, "accordion-content": {"color": {"default": {"chevron": {"value": "{interactive-accent-link-text.on-default.foreground}", "type": "color"}, "link": {"value": "{interactive-accent-link-text.on-default.foreground}", "type": "color"}}, "expanded": {"chevron": {"value": "{interactive-accent-link-text.on-default.foreground}", "type": "color"}, "link": {"value": "{interactive-accent-link-text.on-default.foreground}", "type": "color"}}}}, "form-text-field-numbers": {"border-radius": {"input": {"xxl-xs": {"value": "{comp.border-radius.130}", "type": "borderRadius"}}}, "color": {"default": {"help-icon-default": {"value": "{foreground.business.link-default}", "type": "color"}}}}, "teaser-content": {"border-radius": {"teaser-with-image": {"xxl-l": {"value": "{comp.border-radius.100} {comp.border-radius.130} {comp.border-radius.130} {comp.border-radius.100}", "type": "borderRadius"}, "m-xs": {"value": "{comp.border-radius.100} {comp.border-radius.100} {comp.border-radius.130} {comp.border-radius.130}", "type": "borderRadius"}}, "teaser": {"xxl-xs": {"value": "{comp.border-radius.130}", "type": "borderRadius"}}, "image": {"xxl-l": {"value": "{comp.border-radius.100} {comp.border-radius.130} {comp.border-radius.130} {comp.border-radius.100}", "type": "borderRadius"}, "m-xs": {"value": "{comp.border-radius.130} {comp.border-radius.130} {comp.border-radius.100} {comp.border-radius.100}", "type": "borderRadius"}}}}, "product-teaser-card": {"border-radius": {"teaser-with-image": {"xxl-l": {"value": "{comp.border-radius.100} {comp.border-radius.130} {comp.border-radius.130} {comp.border-radius.100}", "type": "borderRadius"}, "m-xs": {"value": "{comp.border-radius.100} {comp.border-radius.100} {comp.border-radius.130} {comp.border-radius.130}", "type": "borderRadius"}}, "teaser": {"xxl-xs": {"value": "{comp.border-radius.130}", "type": "borderRadius"}}, "image": {"xxl-l": {"value": "{comp.border-radius.100} {comp.border-radius.130} {comp.border-radius.130} {comp.border-radius.100}", "type": "borderRadius"}, "m-xs": {"value": "{comp.border-radius.130} {comp.border-radius.130} {comp.border-radius.100} {comp.border-radius.100}", "type": "borderRadius"}}}}, "info": {"link": {"value": "{color.blue.750}", "type": "color"}, "background": {"value": "{color.neutral.250}", "type": "color", "description": "busi white smoke background"}}, "banner": {"color": {"default": {"link": {"value": "{info.link}", "type": "color"}}}}, "chips": {"selected": {"icon-color": {"value": "{background.business}", "type": "color"}, "border-color": {"value": "{background.business}", "type": "color"}, "background-color": {"value": "{info.background}", "type": "color"}}, "border-radius": {"value": "{comp.border-radius.139}", "type": "borderRadius"}}, "dropdown-plan-switcher-item": {"border-radius": {"value": "{comp.border-radius.130}", "type": "borderRadius"}}, "dropdown-plan-switcher": {"compare-plans-text-color": {"value": "{foreground.business.text-default}", "type": "color"}, "hover-background": {"background-color": {"value": "{background.active-focus}", "type": "color"}}, "price-color": {"value": "{foreground.business.text-default}", "type": "color"}}, "dropdown-plan-switcher-set": {"border-radius": {"value": "{comp.border-radius.139}", "type": "borderRadius"}}}