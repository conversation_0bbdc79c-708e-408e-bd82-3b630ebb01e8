{"name": "@cox/ui-aem", "version": "0.0.1", "description": "AEM integration for Cox UI8 components", "main": "dist/cjs/bundle.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "publishConfig": {"registry": "https://repo.corp.cox.com/artifactory/api/npm/cox-coxcom-npm"}, "license": "UNLICENSED", "scripts": {"format": "prettier --write ."}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "dependencies": {"@cox/ui-core": "workspace:*", "@cox/ui-styles": "workspace:*"}}