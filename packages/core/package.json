{"name": "@cox/ui-core", "version": "8.0.0", "description": "Core UI components for creating intuitive experiences", "main": "dist/cjs/bundle.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "publishConfig": {"registry": "https://repo.corp.cox.com/artifactory/api/npm/cox-coxcom-npm"}, "license": "UNLICENSED", "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c -w", "test": "jest", "test:watch": "jest --watchAll=true", "test:coverage": "jest --watchAll=false --coverage", "test:snapshots": "jest --watchAll=false -u", "format": "prettier --write .", "lint": "eslint --fix --ext .js,.jsx,.ts,.tsx ./src"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "dependencies": {"@cox/ui-styles": "workspace:*", "@cox/ui-utils": "workspace:*"}}