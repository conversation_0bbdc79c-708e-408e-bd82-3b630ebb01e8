{"name": "@cox/ui-types", "version": "0.0.1", "description": "TypeScript types for Cox UI8 components", "main": "dist/cjs/bundle.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "publishConfig": {"registry": "https://repo.corp.cox.com/artifactory/api/npm/cox-coxcom-npm"}, "license": "UNLICENSED", "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c -w", "format": "prettier --write ."}}