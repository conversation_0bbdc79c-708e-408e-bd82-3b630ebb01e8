{"compilerOptions": {"target": "ES2015", "lib": ["dom", "ES2021.String", "es2016"], "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "declaration": true, "emitDeclarationOnly": true, "sourceMap": true, "outDir": "./dist/esm", "declarationDir": "./dist/esm/types", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "isolatedModules": true}, "include": ["src/**/*"], "exclude": ["**/*.test.ts", "**/*.test.tsx", "**/*.stories.ts", "**/*.stories.tsx", "**/dist/**", "**/node_modules/**"]}