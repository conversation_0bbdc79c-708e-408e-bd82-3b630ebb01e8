import { useCallback, useEffect, useRef, useState } from 'react'

import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios'

interface UseAxiosConfig<T> extends AxiosRequestConfig {
  autoFetch?: boolean
  onCompleted?: (data: T) => void
  onError?: (error: AxiosError) => void
}

interface UseAxiosReturn<T> {
  response: T | null
  error: AxiosError | null
  loading: boolean
  axiosAPI: (config?: AxiosRequestConfig) => Promise<void>
}

export function useAxios<T = any>(config: UseAxiosConfig<T>): UseAxiosReturn<T> {
  const { url, method = 'GET', autoFetch = true, onCompleted, onError } = config

  const [response, setResponse] = useState<T | null>(null)
  const [error, setError] = useState<AxiosError | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  const isMounted = useRef<boolean>(false)

  const axiosAPI = useCallback(async (overrideConfig: AxiosRequestConfig = {}) => {
    const csrfRequired =
      overrideConfig?.method?.toLowerCase() === 'post' ||
      overrideConfig?.method?.toLowerCase() === 'delete' ||
      overrideConfig?.method?.toLowerCase() === 'put'
    setLoading(true)
    try {
      if (csrfRequired) {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = window?.RequestJson?.page?.reserved?.csrfToken
      }
      axios.defaults.withCredentials = true
      const response: AxiosResponse<T> = await axios({
        url,
        method,
        ...overrideConfig
      })
      const requestUrl = url || overrideConfig.url
      const redirectUrl = response?.request?.responseURL
      // The requestUrl and redirectUrl are not the same; consider it as a redirected URL.
      if (redirectUrl && requestUrl !== redirectUrl) {
        window.location.href = redirectUrl
        return
      }
      setResponse(response.data)
      if (onCompleted) onCompleted(response.data)
    } catch (err) {
      const axiosError = err as AxiosError
      console.error('🚀 ~ axiosError:', axiosError)
      setError(axiosError)
      if (onError) onError(axiosError)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    if (!isMounted.current && autoFetch) {
      axiosAPI(config)
      isMounted.current = true
    }
    if (isMounted.current) return
  }, [])

  return { response, error, loading, axiosAPI }
}
