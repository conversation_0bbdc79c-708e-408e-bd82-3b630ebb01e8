/**
 * Do not edit directly, this file was auto-generated.
 */

.cox-resi {
  --color-blue-100: #edfaff;
  --color-blue-200: #cff0ff;
  --color-blue-300: #80d5f9;
  --color-blue-400: #00aeef;
  --color-blue-500: #009ae0;
  --color-blue-600: #285a93;
  --color-blue-700: #00aaf4;
  --color-blue-750: #002f87; /** business primary 1 */
  --color-blue-800: #161e7e;
  --color-blue-900: #0f155b;
  --color-teal-300: #8ce0d8;
  --color-teal-400: #00bfa4; /** used in gradients */
  --color-teal-500: #027f94;
  --color-green-100: #eef8f5;
  --color-green-200: #f2ffe2;
  --color-green-250: #dff9ea;
  --color-green-300: #b1f6ad;
  --color-green-400: #80e9ab;
  --color-green-500: #00d258;
  --color-green-600: #00a846;
  --color-green-700: #017f58;
  --color-green-800: #054547;
  --color-yellow-100: #fff8ed;
  --color-yellow-150: #ffe7c3;
  --color-yellow-200: #ffbe00;
  --color-yellow-300: #fcb034;
  --color-yellow-400: #ff9e25;
  --color-yellow-500: #cd7f2e;
  --color-red-100: #fcf4f3;
  --color-red-500: #c63c34;
  --color-purple-500: #6d1f7e;
  --color-pink-500: #db2786;
  --color-neutral-100: #ffffff;
  --color-neutral-200: #f4f8f9;
  --color-neutral-250: #ebeff0;
  --color-neutral-300: #b9c9d2;
  --color-neutral-400: #6c7880;
  --color-neutral-500: #455051;
  --color-neutral-600: #202020;
  --color-gradient-resi: linear-gradient(135deg, #00aaf4 0%, #00bfa4 57%, #00d258 100%);
  --color-gradient-fibr-1: linear-gradient(211deg, #EBFEDF 0%, #93D8DA 100%);
  --color-gradient-fibr-2: linear-gradient(195deg, #6FD8DA 0%, #01A1EA 70%, #076CB4 100%);
  --color-gradient-busi: linear-gradient(135deg, #00aaf4 0%, #002f87 100%);
  --color-gradient-radial: radial-gradient(circle at top left, #00aaf4 0%, #00bfa4 57%, #00d258 100%); /** radial gradient */
  --color-gradient-busi-radial: radial-gradient(circle at top left, #00aaf4 0%, #002f87 100%); /** business radial gradient */
  --color-overlay-500: rgba(69, 80, 81, 0.7);
  --transparent: rgba(255, 255, 255, 0);
  --radius-25: 2px;
  --radius-30: 3px; /** standard for Business */
  --radius-50: 5px;
  --radius-75: 10px;
  --radius-85: 15px;
  --radius-90: 9px;
  --radius-100: 20px;
  --radius-600: 60px;
  --radius-800: 110px;
  --radius-round: 999px;
  --radius-none: 0;
  --borderwidth-25: 1px;
  --borderwidth-50: 2px;
  --borderwidth-100: 3px;
  --borderwidth-none: 0;
  --elevation-1: 0 4px 15px 0 #00000026;
  --elevation-basic: 0 4px 15px 0 #00000026;
  --spacer-15: -24px;
  --spacer-25: 1px;
  --spacer-50: 2px;
  --spacer-100: 4px;
  --spacer-200: 8px;
  --spacer-300: 12px;
  --spacer-350: 14px;
  --spacer-400: 16px;
  --spacer-450: 18px;
  --spacer-500: 20px;
  --spacer-600: 24px;
  --spacer-700: 28px;
  --spacer-800: 32px;
  --spacer-900: 36px;
  --spacer-1000: 40px;
  --spacer-1100: 44px;
  --spacer-1200: 48px;
  --spacer-1300: 56px;
  --spacer-1400: 64px;
  --spacer-1500: 72px;
  --spacer-1600: 80px;
  --spacer-1700: 88px;
  --spacer-1800: 96px;
  --spacer-1900: 125px;
  --spacer-2000: 162px;
  --spacer-2100: 199px;
  --spacer-none: 0;
  --opacity-0: 0;
  --opacity-70: 0.7;
  --opacity-100: 1;
  --opacity-none: 0;
  --opacity-half: 0.5;
  --opacity-full: 1;
  --sizing-100: 2px;
  --sizing-200: 3px;
  --sizing-300: 4px;
  --sizing-400: 8px;
  --sizing-500: 12px;
  --sizing-600: 16px;
  --sizing-650: 18px;
  --sizing-700: 24px;
  --sizing-800: 32px;
  --sizing-900: 38px;
  --sizing-1000: 40px;
  --sizing-1050: 47px;
  --sizing-1100: 48px;
  --sizing-1150: 50px;
  --sizing-1200: 54px;
  --sizing-1300: 56px;
  --sizing-1350: 60px;
  --sizing-1400: 64px;
  --sizing-1500: 86px;
  --sizing-1600: 120px;
  --sizing-icon-functional-100: 16px;
  --sizing-icon-functional-200: 24px;
  --sizing-icon-functional-300: 32px;
  --sizing-icon-decorative-100: 54px;
  --sizing-icon-decorative-200: 86px;
  --sizing-icon-decorative-300: 120px;
  --sizing-icon-oliver-100: 54px;
  --sizing-pattern-650: 18px;
  --sizing-pattern-1050: 47px;
  --sizing-pattern-1150: 50px;
  --sizing-pattern-1350: 60px;
  --sizing-pattern-1500: 86px;
  --sizing-comp-100: 3px;
  --sizing-comp-125: 4px;
  --sizing-comp-150: 8px;
  --sizing-comp-200: 12px;
  --sizing-comp-600: 16px;
  --sizing-comp-650: 18px;
  --sizing-comp-700: 24px;
  --sizing-comp-1000: 40px;
  --sizing-comp-1050: 47px;
  --sizing-comp-1100: 48px;
  --sizing-comp-1150: 50px;
  --sizing-comp-1300: 56px;
  --sizing-comp-1350: 60px;
  --sizing-comp-1400: 64px;
  --sizing-comp-1500: 86px;
  --sizing-comp-1600: 120px;
  --background-default: #ffffff;
  --background-muted-1: #f4f8f9;
  --background-muted-2: #ebeff0;
  --background-bold: #027f94;
  --background-strong: #285a93;
  --background-gradient: linear-gradient(135deg, #00aaf4 0%, #00bfa4 57%, #00d258 100%);
  --background-default-resi: #285a93;
  --background-business: #002f87;
  --background-gradient-busi: linear-gradient(135deg, #00aaf4 0%, #002f87 100%);
  --background-active-focus: #edfaff;
  --accent-decorative-default-1: #009ae0;
  --accent-decorative-default-2: #80d5f9;
  --accent-decorative-on-muted-1-1: #009ae0;
  --accent-decorative-on-muted-1-2: #80d5f9;
  --accent-decorative-on-muted-2-1: #009ae0;
  --accent-decorative-on-muted-2-2: #80d5f9;
  --accent-decorative-on-strong-1: #009ae0;
  --accent-decorative-on-strong-2: #80d5f9;
  --accent-decorative-on-bold-1: #009ae0;
  --accent-decorative-on-bold-2: #80d5f9;
  --accent-decorative-on-gradient-1: #009ae0;
  --accent-decorative-on-gradient-2: #80d5f9;
  --accent-pricing-default: #0f155b;
  --accent-pricing-muted: #ffffff;
  --accent-pricing-disabled: #b9c9d2;
  --accent-pricing-emphasis: #009ae0;
  --accent-pricing-business: #002f87;
  --error-default: #c63c34;
  --error-subtle: #fcf4f3;
  --error-on-error: #202020;
  --warning-default: #ffbe00;
  --warning-subtle: #ffe7c3;
  --warning-on-error: #202020;
  --success-default: #017f58;
  --success-subtle: #eef8f5;
  --success-on-error: #202020;
  --badge-special-offer: #ffbe00;
  --badge-special-content: #ffbe00;
  --badge-standard-product: #cff0ff;
  --badge-standard-content: #cff0ff;
  --badge-community-content: #80d5f9;
  --badge-label: #202020;
  --badge-color-background-special: #ffbe00;
  --badge-color-background-standard: #cff0ff;
  --badge-color-background-community: #80d5f9;
  --badge-color-background-mobile: #00d258;
  --badge-color-background-fiber: #8ce0d8;
  --badge-color-text-default: #202020;
  --icon-functional-default: #202020;
  --icon-functional-muted: #ffffff;
  --icon-functional-bright: #009ae0;
  --icon-functional-bold: #285a93;
  --icon-speed-100: #00d258;
  --icon-speed-200: #00a846;
  --icon-speed-300: #009ae0;
  --icon-decorative-on-default-background-background: #ffbe00; /** icon background for on-default */
  --icon-decorative-on-default-background-stroke: #285a93; /** icon stroke/outline for on-default */
  --icon-decorative-on-default-background-fill: #80d5f9; /** icon fill for on-default */
  --icon-decorative-on-default-background-fill-accent: #ffffff; /** optional white accent fill color */
  --icon-decorative-on-muted-2-background-background: #ffbe00; /** icon background for on-muted2 */
  --icon-decorative-on-muted-2-background-stroke: #285a93; /** icon stroke/outline for on-muted2 */
  --icon-decorative-on-muted-2-background-fill: #80d5f9; /** icon fill for on-muted2 */
  --icon-decorative-on-muted-2-background-fill-accent: #ffffff; /** optional white accent fill color */
  --icon-decorative-on-muted-1-background-background: #ffbe00; /** icon background for on-muted1 */
  --icon-decorative-on-muted-1-background-stroke: #285a93; /** icon stroke/outline for on-muted1 */
  --icon-decorative-on-muted-1-background-fill: #80d5f9; /** icon fill for on-muted1 */
  --icon-decorative-on-muted-1-background-fill-accent: #ffffff; /** optional white accent fill color */
  --icon-decorative-on-strong-background-background: #cff0ff; /** icon background for on-strong */
  --icon-decorative-on-strong-background-stroke: #285a93; /** icon stroke/outline for on-strong */
  --icon-decorative-on-strong-background-fill: #80d5f9; /** icon fill for on-strong */
  --icon-decorative-on-strong-background-fill-accent: #ffffff; /** optional white accent fill color */
  --icon-decorative-on-bold-background-background: #cff0ff; /** icon background for on-bold */
  --icon-decorative-on-bold-background-stroke: #285a93; /** icon stroke/outline for on-bold */
  --icon-decorative-on-bold-background-fill: #80d5f9; /** icon fill for on-bold */
  --icon-decorative-on-bold-background-fill-accent: #ffffff; /** optional white accent fill color */
  --icon-decorative-on-gradient-background-background: #cff0ff; /** icon background for on-gradient */
  --icon-decorative-on-gradient-background-stroke: #285a93; /** icon stroke/outline for on-gradient */
  --icon-decorative-on-gradient-background-fill: #80d5f9; /** icon fill for on-gradient */
  --icon-decorative-on-gradient-background-fill-accent: #ffffff; /** optional white accent fill color */
  --icon-decorative-on-default-background: #ffbe00; /** alias to semantic icon background token */
  --icon-decorative-on-default-stroke: #285a93; /** icon stroke/outline for on-default */
  --icon-decorative-on-default-fill: #80d5f9; /** icon fill for on-default */
  --icon-decorative-on-default-fill-accent: #ffffff; /** optional white accent fill color */
  --icon-decorative-on-muted-2-background: #ffbe00; /** icon background for on-muted2 */
  --icon-decorative-on-muted-2-stroke: #285a93; /** icon stroke/outline for on-muted2 */
  --icon-decorative-on-muted-2-fill: #80d5f9; /** icon fill for on-muted2 */
  --icon-decorative-on-muted-2-fill-accent: #ffffff; /** optional white accent fill color */
  --icon-decorative-on-muted-1-background: #ffbe00; /** icon background for on-muted1 */
  --icon-decorative-on-muted-1-stroke: #285a93; /** icon stroke/outline for on-muted-1 */
  --icon-decorative-on-muted-1-fill: #80d5f9; /** icon fill for on-muted-1 */
  --icon-decorative-on-muted-1-fill-accent: #ffffff; /** optional white accent fill color */
  --icon-decorative-on-strong-background: #cff0ff; /** icon background for on-strong */
  --icon-decorative-on-strong-stroke: #285a93; /** icon stroke/outline for on-strong */
  --icon-decorative-on-strong-fill: #80d5f9; /** icon fill for on-strong */
  --icon-decorative-on-strong-fill-accent: #ffffff; /** optional white accent fill color */
  --icon-decorative-on-bold-background: #cff0ff; /** icon background for on-bold */
  --icon-decorative-on-bold-stroke: #285a93; /** icon stroke/outline for on-bold */
  --icon-decorative-on-bold-fill: #80d5f9; /** icon fill for on-bold */
  --icon-decorative-on-bold-fill-accent: #ffffff; /** optional white accent fill color */
  --icon-decorative-on-gradient-background: #cff0ff; /** icon background for on-gradient */
  --icon-decorative-on-gradient-stroke: #285a93; /** icon stroke/outline for on-gradient */
  --icon-decorative-on-gradient-fill: #80d5f9; /** icon fill for on-gradient */
  --icon-decorative-on-gradient-fill-accent: #ffffff; /** optional white accent fill color */
  --icon-oliver-100: linear-gradient(135deg, #00aaf4 0%, #00bfa4 57%, #00d258 100%);
  --icon-oliver-default: linear-gradient(135deg, #00aaf4 0%, #00bfa4 57%, #00d258 100%);
  --info-default: #009ae0;
  --info-subtle: #cff0ff;
  --info-background: #cff0ff;
  --info-foreground: #202020;
  --info-link: #285a93;
  --foreground-on-default-1: #202020;
  --foreground-on-default-2: #202020;
  --foreground-on-default-3: #455051;
  --foreground-on-default-4: #6c7880;
  --foreground-on-default-disabled: #b9c9d2;
  --foreground-on-default-utility-1: #ebeff0;
  --foreground-on-default-utility-2: #b9c9d2;
  --foreground-on-muted-1-1: #202020;
  --foreground-on-muted-1-2: #202020;
  --foreground-on-muted-1-3: #455051;
  --foreground-on-muted-1-4: #6c7880;
  --foreground-on-muted-1-disabled: #6c7880;
  --foreground-on-muted-1-utility-1: #ebeff0;
  --foreground-on-muted-1-utility-2: #b9c9d2;
  --foreground-on-bold-1: #ffffff;
  --foreground-on-bold-2: #ffffff;
  --foreground-on-bold-3: #ffffff;
  --foreground-on-bold-4: #ffffff;
  --foreground-on-bold-disabled: #b9c9d2;
  --foreground-on-bold-utility-1: #ebeff0;
  --foreground-on-bold-utility-2: #b9c9d2;
  --foreground-on-muted-2-1: #202020;
  --foreground-on-muted-2-2: #202020;
  --foreground-on-muted-2-3: #455051;
  --foreground-on-muted-2-4: #6c7880;
  --foreground-on-muted-2-disabled: #6c7880;
  --foreground-on-muted-2-utility-1: #f4f8f9;
  --foreground-on-muted-2-utility-2: #b9c9d2;
  --foreground-on-strong-1: #ffffff;
  --foreground-on-strong-2: #ffffff;
  --foreground-on-strong-3: #ffffff;
  --foreground-on-strong-4: #ffffff;
  --foreground-on-strong-disabled: #b9c9d2;
  --foreground-on-strong-utility-1: #ebeff0;
  --foreground-on-strong-utility-2: #b9c9d2;
  --foreground-on-gradient-1: #ffffff;
  --foreground-on-gradient-2: #ffffff;
  --foreground-on-gradient-3: #ffffff;
  --foreground-on-gradient-4: #ffffff;
  --foreground-on-gradient-disabled: #b9c9d2;
  --foreground-on-gradient-utility-1: #ebeff0;
  --foreground-on-gradient-utility-2: #b9c9d2;
  --utility-on-default-1: #ebeff0;
  --utility-on-default-2: #b9c9d2;
  --utility-on-default-3: #6c7880;
  --utility-on-muted-1-1: #ebeff0;
  --utility-on-muted-1-2: #b9c9d2;
  --utility-on-muted-1-3: #6c7880;
  --utility-on-bold-1: #ebeff0;
  --utility-on-bold-2: #b9c9d2;
  --utility-on-bold-3: #f4f8f9;
  --utility-on-muted-2-1: #f4f8f9;
  --utility-on-muted-2-2: #b9c9d2;
  --utility-on-muted-2-3: #6c7880;
  --utility-on-strong-1: #ebeff0;
  --utility-on-strong-2: #b9c9d2;
  --utility-on-strong-3: #f4f8f9;
  --utility-on-gradient-1: #ebeff0;
  --utility-on-gradient-2: #b9c9d2;
  --utility-on-gradient-3: #f4f8f9;
  --cox-fontfamilies-main: 'Cera Pro';
  --cox-fontweights-cera-pro-bold: 700;
  --cox-fontweights-cera-pro-medium: 500;
  --cox-fontweights-cera-pro-regular: 400;
  --cox-fontweights-cera-pro-light: 300;
  --cox-fontweights-cera-pro-thin: 100;
  --cox-letterspacing-100: -0.04em;
  --cox-letterspacing-200: -0.03em;
  --cox-letterspacing-300: -0.02em;
  --cox-letterspacing-400: -0.01em;
  --cox-letterspacing-500: 0em;
  --cox-letterspacing-600: 0.01em;
  --cox-letterspacing-700: 0.04em;
  --cox-letterspacing-800: 0.16em;
  --cox-letterspacing-900: 0.2em;
  --cox-text-eyebrow1-font-family: 'Cera Pro';
  --cox-text-eyebrow1-font-weight: 700;
  --cox-text-eyebrow1-font-size: 16px;
  --cox-text-eyebrow1-line-height: 16px;
  --cox-text-eyebrow1-letter-spacing: 0.04em;
  --cox-text-eyebrow1-text-case: uppercase;
  --cox-text-eyebrow2-font-family: 'Cera Pro';
  --cox-text-eyebrow2-font-weight: 700;
  --cox-text-eyebrow2-font-size: 14px;
  --cox-text-eyebrow2-line-height: 14px;
  --cox-text-eyebrow2-letter-spacing: 0.04em;
  --cox-text-eyebrow2-text-case: uppercase;
  --cox-text-eyebrow3-font-family: 'Cera Pro';
  --cox-text-eyebrow3-font-weight: 700;
  --cox-text-eyebrow3-font-size: 12px;
  --cox-text-eyebrow3-line-height: 12px;
  --cox-text-eyebrow3-letter-spacing: 0.04em;
  --cox-text-eyebrow3-text-case: uppercase;
  --cox-text-interactive1-font-family: 'Cera Pro';
  --cox-text-interactive1-font-weight: 700;
  --cox-text-interactive1-line-height: 20px;
  --cox-text-interactive1-font-size: 20px;
  --cox-text-interactive1-letter-spacing: 0.01em;
  --cox-text-interactive2-font-family: 'Cera Pro';
  --cox-text-interactive2-font-weight: 700;
  --cox-text-interactive2-line-height: 18px;
  --cox-text-interactive2-font-size: 18px;
  --cox-text-interactive2-letter-spacing: 0.01em;
  --cox-text-interactive3-font-family: 'Cera Pro';
  --cox-text-interactive3-font-weight: 700;
  --cox-text-interactive3-line-height: 16px;
  --cox-text-interactive3-font-size: 16px;
  --cox-text-interactive3-letter-spacing: 0.01em;
  --cox-text-paragraph1-regular-xxl-m-font-family: 'Cera Pro';
  --cox-text-paragraph1-regular-xxl-m-font-weight: 400;
  --cox-text-paragraph1-regular-xxl-m-font-size: 24px;
  --cox-text-paragraph1-regular-xxl-m-line-height: 34px;
  --cox-text-paragraph1-regular-xxl-m-letter-spacing: 0em;
  --cox-text-paragraph1-regular-xxl-m-text-decoration: none;
  --cox-text-paragraph1-regular-xxl-m-text-case: none;
  --cox-text-paragraph1-regular-xxl-m-paragraph-indent: none;
  --cox-text-paragraph1-regular-xxl-m-paragraph-spacing: none;
  --cox-text-paragraph1-regular-s-xs-font-family: 'Cera Pro';
  --cox-text-paragraph1-regular-s-xs-font-weight: 400;
  --cox-text-paragraph1-regular-s-xs-font-size: 18px;
  --cox-text-paragraph1-regular-s-xs-line-height: 24px;
  --cox-text-paragraph1-regular-s-xs-letter-spacing: 0em;
  --cox-text-paragraph1-regular-s-xs-text-decoration: none;
  --cox-text-paragraph1-regular-s-xs-text-case: none;
  --cox-text-paragraph1-regular-s-xs-paragraph-spacing: none;
  --cox-text-paragraph1-regular-s-xs-paragraph-indent: none;
  --cox-text-paragraph1-medium-xxl-m-font-family: 'Cera Pro';
  --cox-text-paragraph1-medium-xxl-m-font-weight: 500;
  --cox-text-paragraph1-medium-xxl-m-font-size: 24px;
  --cox-text-paragraph1-medium-xxl-m-line-height: 34px;
  --cox-text-paragraph1-medium-xxl-m-letter-spacing: 0em;
  --cox-text-paragraph1-medium-s-xs-font-family: 'Cera Pro';
  --cox-text-paragraph1-medium-s-xs-font-weight: 500;
  --cox-text-paragraph1-medium-s-xs-font-size: 18px;
  --cox-text-paragraph1-medium-s-xs-line-height: 24px;
  --cox-text-paragraph1-medium-s-xs-letter-spacing: 0em;
  --cox-text-paragraph1-bold-xxl-m-font-family: 'Cera Pro';
  --cox-text-paragraph1-bold-xxl-m-font-weight: 700;
  --cox-text-paragraph1-bold-xxl-m-font-size: 24px;
  --cox-text-paragraph1-bold-xxl-m-line-height: 34px;
  --cox-text-paragraph1-bold-xxl-m-letter-spacing: 0em;
  --cox-text-paragraph1-bold-s-xs-font-family: 'Cera Pro';
  --cox-text-paragraph1-bold-s-xs-font-weight: 700;
  --cox-text-paragraph1-bold-s-xs-font-size: 18px;
  --cox-text-paragraph1-bold-s-xs-line-height: 24px;
  --cox-text-paragraph1-bold-s-xs-letter-spacing: 0em;
  --cox-text-paragraph2-regular-xxl-m-font-family: 'Cera Pro';
  --cox-text-paragraph2-regular-xxl-m-font-weight: 400;
  --cox-text-paragraph2-regular-xxl-m-font-size: 18px;
  --cox-text-paragraph2-regular-xxl-m-line-height: 24px;
  --cox-text-paragraph2-regular-xxl-m-letter-spacing: 0em;
  --cox-text-paragraph2-regular-s-xs-font-family: 'Cera Pro';
  --cox-text-paragraph2-regular-s-xs-font-weight: 400;
  --cox-text-paragraph2-regular-s-xs-font-size: 16px;
  --cox-text-paragraph2-regular-s-xs-line-height: 23px;
  --cox-text-paragraph2-regular-s-xs-letter-spacing: 0em;
  --cox-text-paragraph2-medium-xxl-m-font-family: 'Cera Pro';
  --cox-text-paragraph2-medium-xxl-m-font-weight: 500;
  --cox-text-paragraph2-medium-xxl-m-font-size: 18px;
  --cox-text-paragraph2-medium-xxl-m-line-height: 24px;
  --cox-text-paragraph2-medium-xxl-m-letter-spacing: 0em;
  --cox-text-paragraph2-medium-s-xs-font-family: 'Cera Pro';
  --cox-text-paragraph2-medium-s-xs-font-weight: 500;
  --cox-text-paragraph2-medium-s-xs-font-size: 16px;
  --cox-text-paragraph2-medium-s-xs-line-height: 23px;
  --cox-text-paragraph2-medium-s-xs-letter-spacing: 0em;
  --cox-text-paragraph2-bold-xxl-m-font-family: 'Cera Pro';
  --cox-text-paragraph2-bold-xxl-m-font-weight: 700;
  --cox-text-paragraph2-bold-xxl-m-font-size: 18px;
  --cox-text-paragraph2-bold-xxl-m-line-height: 24px;
  --cox-text-paragraph2-bold-xxl-m-letter-spacing: 0em;
  --cox-text-paragraph2-bold-s-xs-font-family: 'Cera Pro';
  --cox-text-paragraph2-bold-s-xs-font-weight: 700;
  --cox-text-paragraph2-bold-s-xs-font-size: 16px;
  --cox-text-paragraph2-bold-s-xs-line-height: 23px;
  --cox-text-paragraph2-bold-s-xs-letter-spacing: 0em;
  --cox-text-paragraph3-regular-font-family: 'Cera Pro';
  --cox-text-paragraph3-regular-font-weight: 400;
  --cox-text-paragraph3-regular-font-size: 16px;
  --cox-text-paragraph3-regular-line-height: 23px;
  --cox-text-paragraph3-regular-letter-spacing: 0em;
  --cox-text-paragraph3-medium-font-family: 'Cera Pro';
  --cox-text-paragraph3-medium-font-weight: 500;
  --cox-text-paragraph3-medium-font-size: 16px;
  --cox-text-paragraph3-medium-line-height: 23px;
  --cox-text-paragraph3-medium-letter-spacing: 0em;
  --cox-text-paragraph3-bold-font-family: 'Cera Pro';
  --cox-text-paragraph3-bold-font-weight: 700;
  --cox-text-paragraph3-bold-font-size: 16px;
  --cox-text-paragraph3-bold-line-height: 23px;
  --cox-text-paragraph3-bold-letter-spacing: 0em;
  --cox-text-paragraph4-regular-font-family: 'Cera Pro';
  --cox-text-paragraph4-regular-font-weight: 400;
  --cox-text-paragraph4-regular-font-size: 14px;
  --cox-text-paragraph4-regular-line-height: 21px;
  --cox-text-paragraph4-regular-letter-spacing: 0em;
  --cox-text-paragraph4-medium-font-family: 'Cera Pro';
  --cox-text-paragraph4-medium-font-weight: 500;
  --cox-text-paragraph4-medium-font-size: 14px;
  --cox-text-paragraph4-medium-line-height: 21px;
  --cox-text-paragraph4-medium-letter-spacing: 0em;
  --cox-text-paragraph4-bold-font-family: 'Cera Pro';
  --cox-text-paragraph4-bold-font-weight: 700;
  --cox-text-paragraph4-bold-font-size: 14px;
  --cox-text-paragraph4-bold-line-height: 21px;
  --cox-text-paragraph4-bold-letter-spacing: 0em;
  --cox-text-paragraph5-regular-font-family: 'Cera Pro';
  --cox-text-paragraph5-regular-font-weight: 400;
  --cox-text-paragraph5-regular-font-size: 13px;
  --cox-text-paragraph5-regular-line-height: 20px;
  --cox-text-paragraph5-regular-letter-spacing: 0em;
  --cox-text-paragraph5-medium-font-family: 'Cera Pro';
  --cox-text-paragraph5-medium-font-weight: 500;
  --cox-text-paragraph5-medium-font-size: 13px;
  --cox-text-paragraph5-medium-line-height: 20px;
  --cox-text-paragraph5-medium-letter-spacing: 0em;
  --cox-text-paragraph5-bold-font-family: 'Cera Pro';
  --cox-text-paragraph5-bold-font-weight: 700;
  --cox-text-paragraph5-bold-font-size: 13px;
  --cox-text-paragraph5-bold-line-height: 20px;
  --cox-text-paragraph5-bold-letter-spacing: 0em;
  --cox-text-title1-medium-font-family: 'Cera Pro';
  --cox-text-title1-medium-font-weight: 500;
  --cox-text-title1-medium-line-height: 30px;
  --cox-text-title1-medium-font-size: 22px;
  --cox-text-title1-medium-letter-spacing: -0.02em;
  --cox-text-title1-bold-font-family: 'Cera Pro';
  --cox-text-title1-bold-font-weight: 700;
  --cox-text-title1-bold-line-height: 30px;
  --cox-text-title1-bold-font-size: 22px;
  --cox-text-title1-bold-letter-spacing: -0.02em;
  --cox-text-title2-medium-font-family: 'Cera Pro';
  --cox-text-title2-medium-font-weight: 500;
  --cox-text-title2-medium-line-height: 28px;
  --cox-text-title2-medium-font-size: 20px;
  --cox-text-title2-medium-letter-spacing: -0.02em;
  --cox-text-title2-bold-font-family: 'Cera Pro';
  --cox-text-title2-bold-font-weight: 700;
  --cox-text-title2-bold-line-height: 28px;
  --cox-text-title2-bold-font-size: 20px;
  --cox-text-title2-bold-letter-spacing: -0.02em;
  --cox-text-title3-medium-font-family: 'Cera Pro';
  --cox-text-title3-medium-font-weight: 500;
  --cox-text-title3-medium-line-height: 26px;
  --cox-text-title3-medium-font-size: 18px;
  --cox-text-title3-medium-letter-spacing: -0.02em;
  --cox-text-title3-bold-font-family: 'Cera Pro';
  --cox-text-title3-bold-font-weight: 700;
  --cox-text-title3-bold-line-height: 26px;
  --cox-text-title3-bold-font-size: 18px;
  --cox-text-title3-bold-letter-spacing: -0.02em;
  --cox-text-title4-medium-font-family: 'Cera Pro';
  --cox-text-title4-medium-font-weight: 500;
  --cox-text-title4-medium-line-height: 22px;
  --cox-text-title4-medium-font-size: 16px;
  --cox-text-title4-medium-letter-spacing: -0.02em;
  --cox-text-title4-bold-font-family: 'Cera Pro';
  --cox-text-title4-bold-font-weight: 700;
  --cox-text-title4-bold-line-height: 22px;
  --cox-text-title4-bold-font-size: 16px;
  --cox-text-title4-bold-letter-spacing: -0.02em;
  --cox-text-display1-xxl-xl-font-family: 'Cera Pro';
  --cox-text-display1-xxl-xl-font-weight: 700;
  --cox-text-display1-xxl-xl-letter-spacing: -0.04em;
  --cox-text-display1-xxl-xl-font-size: 128px;
  --cox-text-display1-xxl-xl-line-height: 132px;
  --cox-text-display1-xxl-xl-paragraph-spacing: 0;
  --cox-text-display1-l-m-font-family: 'Cera Pro';
  --cox-text-display1-l-m-font-weight: 700;
  --cox-text-display1-l-m-font-size: 72px;
  --cox-text-display1-l-m-line-height: 80px;
  --cox-text-display1-l-m-letter-spacing: -0.03em;
  --cox-text-display1-l-m-paragraph-spacing: 0;
  --cox-text-display1-s-xs-font-family: 'Cera Pro';
  --cox-text-display1-s-xs-font-weight: 700;
  --cox-text-display1-s-xs-font-size: 64px;
  --cox-text-display1-s-xs-line-height: 70px;
  --cox-text-display1-s-xs-letter-spacing: -0.03em;
  --cox-text-display1-s-xs-paragraph-spacing: 0;
  --cox-text-display2-xxl-xl-font-family: 'Cera Pro';
  --cox-text-display2-xxl-xl-font-weight: 700;
  --cox-text-display2-xxl-xl-letter-spacing: -0.04em;
  --cox-text-display2-xxl-xl-font-size: 96px;
  --cox-text-display2-xxl-xl-line-height: 100px;
  --cox-text-display2-xxl-xl-paragraph-spacing: 0;
  --cox-text-display2-l-m-font-family: 'Cera Pro';
  --cox-text-display2-l-m-font-weight: 700;
  --cox-text-display2-l-m-font-size: 56px;
  --cox-text-display2-l-m-line-height: 64px;
  --cox-text-display2-l-m-letter-spacing: -0.04em;
  --cox-text-display2-l-m-paragraph-spacing: 0;
  --cox-text-display2-s-xs-font-family: 'Cera Pro';
  --cox-text-display2-s-xs-font-weight: 700;
  --cox-text-display2-s-xs-font-size: 48px;
  --cox-text-display2-s-xs-line-height: 58px;
  --cox-text-display2-s-xs-letter-spacing: -0.03em;
  --cox-text-display2-s-xs-paragraph-spacing: 0;
  --cox-text-display3-xxl-xl-font-family: 'Cera Pro';
  --cox-text-display3-xxl-xl-font-weight: 700;
  --cox-text-display3-xxl-xl-letter-spacing: -0.04em;
  --cox-text-display3-xxl-xl-font-size: 80px;
  --cox-text-display3-xxl-xl-line-height: 88px;
  --cox-text-display3-l-m-font-family: 'Cera Pro';
  --cox-text-display3-l-m-font-weight: 700;
  --cox-text-display3-l-m-font-size: 48px;
  --cox-text-display3-l-m-line-height: 54px;
  --cox-text-display3-l-m-letter-spacing: -0.04em;
  --cox-text-display3-s-xs-font-family: 'Cera Pro';
  --cox-text-display3-s-xs-font-weight: 700;
  --cox-text-display3-s-xs-font-size: 36px;
  --cox-text-display3-s-xs-line-height: 44px;
  --cox-text-display3-s-xs-letter-spacing: -0.02em;
  --cox-text-display3-s-xs-text-decoration: bold;
  --cox-text-heading1-xxl-xl-font-family: 'Cera Pro';
  --cox-text-heading1-xxl-xl-font-weight: 700;
  --cox-text-heading1-xxl-xl-letter-spacing: -0.03em;
  --cox-text-heading1-xxl-xl-font-size: 48px;
  --cox-text-heading1-xxl-xl-line-height: 58px;
  --cox-text-heading1-l-m-font-family: 'Cera Pro';
  --cox-text-heading1-l-m-font-weight: 700;
  --cox-text-heading1-l-m-line-height: 52px;
  --cox-text-heading1-l-m-letter-spacing: -0.03em;
  --cox-text-heading1-l-m-font-size: 44px;
  --cox-text-heading1-s-xs-font-family: 'Cera Pro';
  --cox-text-heading1-s-xs-font-weight: 700;
  --cox-text-heading1-s-xs-font-size: 36px;
  --cox-text-heading1-s-xs-line-height: 44px;
  --cox-text-heading1-s-xs-letter-spacing: -0.03em;
  --cox-text-heading2-xxl-xl-font-family: 'Cera Pro';
  --cox-text-heading2-xxl-xl-font-weight: 700;
  --cox-text-heading2-xxl-xl-letter-spacing: -0.03em;
  --cox-text-heading2-xxl-xl-font-size: 40px;
  --cox-text-heading2-xxl-xl-text-case: none;
  --cox-text-heading2-xxl-xl-line-height: 50px;
  --cox-text-heading2-l-m-font-family: 'Cera Pro';
  --cox-text-heading2-l-m-font-weight: 700;
  --cox-text-heading2-l-m-letter-spacing: -0.02em;
  --cox-text-heading2-l-m-font-size: 36px;
  --cox-text-heading2-l-m-text-case: none;
  --cox-text-heading2-l-m-line-height: 44px;
  --cox-text-heading2-s-xs-font-family: 'Cera Pro';
  --cox-text-heading2-s-xs-font-weight: 700;
  --cox-text-heading2-s-xs-font-size: 28px;
  --cox-text-heading2-s-xs-line-height: 36px;
  --cox-text-heading2-s-xs-letter-spacing: -0.02em;
  --cox-text-heading2-s-xs-text-case: none;
  --cox-text-heading4-xxl-xl-font-family: 'Cera Pro';
  --cox-text-heading4-xxl-xl-font-weight: 700;
  --cox-text-heading4-xxl-xl-letter-spacing: -0.03em;
  --cox-text-heading4-xxl-xl-font-size: 32px;
  --cox-text-heading4-xxl-xl-line-height: 40px;
  --cox-text-heading4-l-m-font-family: 'Cera Pro';
  --cox-text-heading4-l-m-font-weight: 700;
  --cox-text-heading4-l-m-font-size: 24px;
  --cox-text-heading4-l-m-line-height: 30px;
  --cox-text-heading4-l-m-letter-spacing: -0.02em;
  --cox-text-heading4-s-xs-font-family: 'Cera Pro';
  --cox-text-heading4-s-xs-font-weight: 700;
  --cox-text-heading4-s-xs-font-size: 20px;
  --cox-text-heading4-s-xs-line-height: 28px;
  --cox-text-heading4-s-xs-letter-spacing: -0.02em;
  --cox-text-heading3-xxl-xl-font-family: 'Cera Pro';
  --cox-text-heading3-xxl-xl-font-weight: 700;
  --cox-text-heading3-xxl-xl-letter-spacing: -0.03em;
  --cox-text-heading3-xxl-xl-font-size: 36px;
  --cox-text-heading3-xxl-xl-text-case: none;
  --cox-text-heading3-xxl-xl-line-height: 44px;
  --cox-text-heading3-l-m-font-family: 'Cera Pro';
  --cox-text-heading3-l-m-font-weight: 700;
  --cox-text-heading3-l-m-letter-spacing: -0.02em;
  --cox-text-heading3-l-m-font-size: 30px;
  --cox-text-heading3-l-m-text-case: none;
  --cox-text-heading3-l-m-line-height: 36px;
  --cox-text-heading3-s-xs-font-family: 'Cera Pro';
  --cox-text-heading3-s-xs-font-weight: 700;
  --cox-text-heading3-s-xs-font-size: 24px;
  --cox-text-heading3-s-xs-line-height: 32px;
  --cox-text-heading3-s-xs-letter-spacing: -0.02em;
  --cox-text-heading3-s-xs-text-case: none;
  --cox-text-heading5-xxl-xl-font-family: 'Cera Pro';
  --cox-text-heading5-xxl-xl-font-weight: 700;
  --cox-text-heading5-xxl-xl-letter-spacing: -0.02em;
  --cox-text-heading5-xxl-xl-font-size: 28px;
  --cox-text-heading5-xxl-xl-text-case: none;
  --cox-text-heading5-xxl-xl-line-height: 36px;
  --cox-text-heading5-l-m-font-family: 'Cera Pro';
  --cox-text-heading5-l-m-font-weight: 700;
  --cox-text-heading5-l-m-letter-spacing: -0.02em;
  --cox-text-heading5-l-m-font-size: 20px;
  --cox-text-heading5-l-m-text-case: none;
  --cox-text-heading5-l-m-line-height: 28px;
  --cox-text-heading5-s-xs-font-family: 'Cera Pro';
  --cox-text-heading5-s-xs-font-weight: 700;
  --cox-text-heading5-s-xs-font-size: 18px;
  --cox-text-heading5-s-xs-line-height: 26px;
  --cox-text-heading5-s-xs-letter-spacing: -0.02em;
  --cox-text-heading5-s-xs-text-case: none;
  --cox-text-heading6-xxl-xl-font-family: 'Cera Pro';
  --cox-text-heading6-xxl-xl-font-weight: 700;
  --cox-text-heading6-xxl-xl-letter-spacing: -0.02em;
  --cox-text-heading6-xxl-xl-font-size: 24px;
  --cox-text-heading6-xxl-xl-text-case: none;
  --cox-text-heading6-xxl-xl-line-height: 32px;
  --cox-text-heading6-l-xs-font-family: 'Cera Pro';
  --cox-text-heading6-l-xs-font-weight: 700;
  --cox-text-heading6-l-xs-letter-spacing: -0.02em;
  --cox-text-heading6-l-xs-font-size: 16px;
  --cox-text-heading6-l-xs-text-case: none;
  --cox-text-heading6-l-xs-line-height: 22px;
  --cox-textcase-uppercase: uppercase;
  --cox-textcase-none: none;
  --cox-lineheight-100: 12px;
  --cox-lineheight-200: 14px;
  --cox-lineheight-300: 16px;
  --cox-lineheight-400: 18px;
  --cox-lineheight-500: 20px;
  --cox-lineheight-600: 21px;
  --cox-lineheight-700: 22px;
  --cox-lineheight-800: 23px;
  --cox-lineheight-900: 24px;
  --cox-lineheight-1000: 26px;
  --cox-lineheight-1100: 28px;
  --cox-lineheight-1200: 30px;
  --cox-lineheight-1250: 32px;
  --cox-lineheight-1300: 34px;
  --cox-fontsize-100: 12px;
  --cox-fontsize-200: 13px;
  --cox-fontsize-300: 14px;
  --cox-fontsize-400: 16px;
  --cox-fontsize-500: 18px;
  --cox-fontsize-600: 20px;
  --cox-fontsize-700: 22px;
  --cox-fontsize-800: 24px;
  --cox-resi-elevation-1: 0 4px 15px 0 #00000026;
  --cox-resi-lineheight-25: 16px;
  --cox-resi-lineheight-50: 18px;
  --cox-resi-lineheight-75: 20px;
  --cox-resi-lineheight-100: 22px;
  --cox-resi-lineheight-200: 26px;
  --cox-resi-lineheight-250: 28px;
  --cox-resi-lineheight-300: 30px;
  --cox-resi-lineheight-350: 32px;
  --cox-resi-lineheight-400: 36px;
  --cox-resi-lineheight-500: 40px;
  --cox-resi-lineheight-600: 44px;
  --cox-resi-lineheight-700: 50px;
  --cox-resi-lineheight-750: 52px;
  --cox-resi-lineheight-800: 54px;
  --cox-resi-lineheight-900: 58px;
  --cox-resi-lineheight-1000: 64px;
  --cox-resi-lineheight-1100: 70px;
  --cox-resi-lineheight-1200: 80px;
  --cox-resi-lineheight-1300: 88px;
  --cox-resi-lineheight-1400: 100px;
  --cox-resi-lineheight-1700: 132px;
  --cox-resi-borderwidth-1: 2px;
  --cox-resi-fontsize-100: 16px;
  --cox-resi-fontsize-150: 18px;
  --cox-resi-fontsize-200: 20px;
  --cox-resi-fontsize-250: 24px;
  --cox-resi-fontsize-300: 28px;
  --cox-resi-fontsize-350: 30px;
  --cox-resi-fontsize-400: 32px;
  --cox-resi-fontsize-450: 36px;
  --cox-resi-fontsize-500: 40px;
  --cox-resi-fontsize-550: 44px;
  --cox-resi-fontsize-600: 48px;
  --cox-resi-fontsize-650: 56px;
  --cox-resi-fontsize-700: 64px;
  --cox-resi-fontsize-800: 72px;
  --cox-resi-fontsize-850: 80px;
  --cox-resi-fontsize-900: 96px;
  --cox-resi-fontsize-1000: 128px;
  --cox-resi-opacity-70: 0.7;
  --cox-resi-letterspacing-100: -0.04em;
  --cox-resi-letterspacing-200: -0.03em;
  --cox-resi-letterspacing-300: -0.02em;
  --cox-resi-letterspacing-400: 0.01em;
  --cox-opacity-0: 0;
  --cox-opacity-200: 1;
  --spacing-pattern-100: 4px;
  --spacing-pattern-200: 8px;
  --spacing-pattern-300: 12px;
  --spacing-pattern-400: 14px;
  --spacing-pattern-500: 16px;
  --spacing-pattern-600: 20px;
  --spacing-pattern-700: 24px;
  --spacing-pattern-800: 28px;
  --spacing-pattern-900: 32px;
  --spacing-pattern-1000: 36px;
  --spacing-pattern-1100: 40px;
  --spacing-pattern-1200: 44px;
  --spacing-pattern-1300: 48px;
  --spacing-pattern-1400: 56px;
  --spacing-pattern-1600: 72px;
  --spacing-pattern-1700: 88px;
  --spacing-pattern-1900: 96px;
  --spacing-pattern-none: 0;
  --spacing-comp-100: 4px;
  --spacing-comp-200: 8px;
  --spacing-comp-300: 12px;
  --spacing-comp-400: 14px;
  --spacing-comp-500: 16px;
  --spacing-comp-600: 20px;
  --spacing-comp-700: 24px;
  --spacing-comp-800: 28px;
  --spacing-comp-900: 32px;
  --spacing-comp-1000: 36px;
  --spacing-comp-1200: 44px;
  --spacing-section-80: -24px;
  --spacing-section-100: 16px;
  --spacing-section-125: 20px;
  --spacing-section-150: 28px;
  --spacing-section-200: 32px;
  --spacing-section-300: 56px;
  --spacing-section-400: 96px;
  --spacing-section-500: 125px;
  --spacing-section-600: 162px;
  --spacing-section-700: 199px;
  --pattern-border-radius-ui-100: 0;
  --pattern-border-radius-ui-150: 15px;
  --pattern-border-radius-ui-200: 20px;
  --pattern-border-radius-ui-300: 60px;
  --pattern-border-radius-photography-50: 20px;
  --pattern-border-radius-photography-100: 20px;
  --pattern-border-radius-photography-500: 999px;
  --comp-border-radius-100: 0;
  --comp-border-radius-125: 2px;
  --comp-border-radius-130: 3px; /** standard for Business */
  --comp-border-radius-135: 5px;
  --comp-border-radius-139: 9px;
  --comp-border-radius-150: 10px;
  --comp-border-radius-200: 10px;
  --comp-border-radius-300: 20px;
  --comp-border-radius-400: 999px;
  --comp-border-radius-600: 60px;
  --comp-border-radius-button-pill: 110px;
  --border-width-100: 1px;
  --border-width-200: 2px;
  --border-width-300: 3px;
  --border-width-none: 0;
  --section-border-radius-ui-100: 0;
  --section-border-radius-ui-150: 15px;
  --section-border-radius-ui-200: 20px;
  --section-border-radius-ui-300: 20px;
  --section-border-radius-photography-100: 60px;
  --section-border-radius-photography-500: 999px;
  --interactive-accent-link-text-on-muted-1-foreground: #285a93;
  --interactive-accent-link-text-on-muted-1-disabled: #b9c9d2;
  --interactive-accent-link-text-on-muted-1-hover: #0f155b;
  --interactive-accent-link-text-on-default-foreground: #285a93;
  --interactive-accent-link-text-on-default-disabled: #b9c9d2;
  --interactive-accent-link-text-on-default-hover: #0f155b;
  --interactive-accent-link-text-on-muted-2-foreground: #ffffff;
  --interactive-accent-link-text-on-muted-2-disabled: #b9c9d2;
  --interactive-accent-link-text-on-muted-2-hover: #0f155b;
  --interactive-accent-link-text-on-strong-foreground: #ffffff;
  --interactive-accent-link-text-on-strong-disabled: #b9c9d2;
  --interactive-accent-link-text-on-strong-hover: #ebeff0;
  --interactive-accent-link-text-on-bold-foreground: #ffffff;
  --interactive-accent-link-text-on-bold-disabled: #b9c9d2;
  --interactive-accent-link-text-on-bold-hover: #ebeff0;
  --interactive-accent-link-text-on-gradient-foreground: #ffffff;
  --interactive-accent-link-text-on-gradient-disabled: #b9c9d2;
  --interactive-accent-link-text-on-gradient-hover: #ebeff0;
  --pagination-dots-border-active-on-default: #00aeef;
  --pagination-dots-border-active-on-muted-1: #00aeef;
  --pagination-dots-border-active-on-muted-2: #00aeef;
  --pagination-dots-border-active-on-bold: #ffffff;
  --pagination-dots-border-active-on-strong: #ffffff;
  --pagination-dots-border-active-on-gradient: #ffffff;
  --pagination-dots-border-hover-on-default: #00aeef;
  --pagination-dots-border-hover-on-muted-1: #00aeef;
  --pagination-dots-border-hover-on-muted-2: #00aeef;
  --pagination-dots-border-hover-on-bold: #ffffff;
  --pagination-dots-border-hover-on-strong: #ffffff;
  --pagination-dots-border-hover-on-gradient: #ffffff;
  --pagination-dots-surface-inactive-on-default: #0f155b;
  --pagination-dots-surface-inactive-on-muted-1: #0f155b;
  --pagination-dots-surface-inactive-on-muted-2: #0f155b;
  --pagination-dots-surface-inactive-on-bold: #ffffff;
  --pagination-dots-surface-inactive-on-strong: #ffffff;
  --pagination-dots-surface-inactive-on-gradient: #ffffff;
  --interactive-accent-button-primary-on-default-foreground: #ffffff;
  --interactive-accent-button-primary-on-default-surface: #285a93;
  --interactive-accent-button-primary-on-default-disabled: #b9c9d2;
  --interactive-accent-button-primary-on-default-hover: #0f155b;
  --interactive-accent-button-primary-on-default-active: #0f155b;
  --interactive-accent-button-primary-on-muted-1-foreground: #ffffff;
  --interactive-accent-button-primary-on-muted-1-surface: #285a93;
  --interactive-accent-button-primary-on-muted-1-disabled: #b9c9d2;
  --interactive-accent-button-primary-on-muted-1-active: #0f155b;
  --interactive-accent-button-primary-on-muted-1-hover: #0f155b;
  --interactive-accent-button-primary-on-muted-2-foreground: #ffffff;
  --interactive-accent-button-primary-on-muted-2-surface: #285a93;
  --interactive-accent-button-primary-on-muted-2-disabled: #b9c9d2;
  --interactive-accent-button-primary-on-muted-2-hover: #0f155b;
  --interactive-accent-button-primary-on-muted-2-active: #0f155b;
  --interactive-accent-button-primary-on-strong-foreground: #285a93;
  --interactive-accent-button-primary-on-strong-surface: #ffffff;
  --interactive-accent-button-primary-on-strong-disabled: #b9c9d2;
  --interactive-accent-button-primary-on-strong-foreground-disabled: #ffffff;
  --interactive-accent-button-primary-on-strong-hover: #edfaff;
  --interactive-accent-button-primary-on-strong-active: #edfaff;
  --interactive-accent-button-primary-on-bold-foreground: #285a93;
  --interactive-accent-button-primary-on-bold-surface: #ffffff;
  --interactive-accent-button-primary-on-bold-disabled: #b9c9d2;
  --interactive-accent-button-primary-on-bold-foreground-disabled: #ffffff;
  --interactive-accent-button-primary-on-bold-hover: #edfaff;
  --interactive-accent-button-primary-on-bold-active: #edfaff;
  --interactive-accent-button-primary-on-gradient-foreground: #285a93;
  --interactive-accent-button-primary-on-gradient-surface: #ffffff;
  --interactive-accent-button-primary-on-gradient-disabled: #b9c9d2;
  --interactive-accent-button-primary-on-gradient-foreground-disabled: #ffffff;
  --interactive-accent-button-primary-on-gradient-hover: #edfaff;
  --interactive-accent-button-primary-on-gradient-active: #edfaff;
  --interactive-accent-button-secondary-on-default-foreground: #285a93;
  --interactive-accent-button-secondary-on-default-foreground-hover: #0f155b;
  --interactive-accent-button-secondary-on-default-foreground-disabled: #b9c9d2;
  --interactive-accent-button-secondary-on-default-foreground-active: #0f155b;
  --interactive-accent-button-secondary-on-default-surface-border: #285a93;
  --interactive-accent-button-secondary-on-default-hover: #0f155b;
  --interactive-accent-button-secondary-on-default-disabled: #b9c9d2;
  --interactive-accent-button-secondary-on-default-active: #0f155b;
  --interactive-accent-button-secondary-on-muted-1-foreground: #285a93;
  --interactive-accent-button-secondary-on-muted-1-foreground-hover: #0f155b;
  --interactive-accent-button-secondary-on-muted-1-foreground-disabled: #b9c9d2;
  --interactive-accent-button-secondary-on-muted-1-foreground-active: #0f155b;
  --interactive-accent-button-secondary-on-muted-1-surface-border: #285a93;
  --interactive-accent-button-secondary-on-muted-1-hover: #0f155b;
  --interactive-accent-button-secondary-on-muted-1-disabled: #b9c9d2;
  --interactive-accent-button-secondary-on-muted-1-active: #0f155b;
  --interactive-accent-button-secondary-on-muted-2-foreground: #285a93;
  --interactive-accent-button-secondary-on-muted-2-foreground-hover: #0f155b;
  --interactive-accent-button-secondary-on-muted-2-foreground-disabled: #b9c9d2;
  --interactive-accent-button-secondary-on-muted-2-foreground-active: #0f155b;
  --interactive-accent-button-secondary-on-muted-2-surface-border: #285a93;
  --interactive-accent-button-secondary-on-muted-2-hover: #0f155b;
  --interactive-accent-button-secondary-on-muted-2-disabled: #b9c9d2;
  --interactive-accent-button-secondary-on-muted-2-active: #0f155b;
  --interactive-accent-button-secondary-on-strong-foreground: #ffffff;
  --interactive-accent-button-secondary-on-strong-foreground-hover: #285a93;
  --interactive-accent-button-secondary-on-strong-foreground-disabled: #b9c9d2;
  --interactive-accent-button-secondary-on-strong-foreground-active: #285a93;
  --interactive-accent-button-secondary-on-strong-surface-border: #ffffff;
  --interactive-accent-button-secondary-on-strong-hover: #ffffff;
  --interactive-accent-button-secondary-on-strong-disabled: #b9c9d2;
  --interactive-accent-button-secondary-on-strong-active: #ffffff;
  --interactive-accent-button-secondary-on-strong-surface-hover: #ffffff;
  --interactive-accent-button-secondary-on-bold-foreground: #ffffff;
  --interactive-accent-button-secondary-on-bold-foreground-hover: #285a93;
  --interactive-accent-button-secondary-on-bold-foreground-disabled: #b9c9d2;
  --interactive-accent-button-secondary-on-bold-foreground-active: #285a93;
  --interactive-accent-button-secondary-on-bold-surface-border: #ffffff;
  --interactive-accent-button-secondary-on-bold-hover: #ffffff;
  --interactive-accent-button-secondary-on-bold-disabled: #b9c9d2;
  --interactive-accent-button-secondary-on-bold-active: #ffffff;
  --interactive-accent-button-secondary-on-bold-surface-hover: #ffffff;
  --interactive-accent-button-secondary-on-gradient-foreground: #ffffff;
  --interactive-accent-button-secondary-on-gradient-foreground-hover: #285a93;
  --interactive-accent-button-secondary-on-gradient-foreground-disabled: #b9c9d2;
  --interactive-accent-button-secondary-on-gradient-foreground-active: #285a93;
  --interactive-accent-button-secondary-on-gradient-surface-border: #ffffff;
  --interactive-accent-button-secondary-on-gradient-hover: #ffffff;
  --interactive-accent-button-secondary-on-gradient-disabled: #b9c9d2;
  --interactive-accent-button-secondary-on-gradient-active: #ffffff;
  --interactive-accent-button-secondary-on-gradient-surface-hover: #ffffff;
  --interactive-accent-button-tertiary-on-default-foreground: #285a93;
  --interactive-accent-button-tertiary-on-default-foreground-hover: #0f155b;
  --interactive-accent-button-tertiary-on-default-foreground-disabled: #b9c9d2;
  --interactive-accent-button-tertiary-on-default-foreground-active: #0f155b;
  --interactive-accent-button-tertiary-on-muted-1-foreground: #285a93;
  --interactive-accent-button-tertiary-on-muted-1-foreground-hover: #0f155b;
  --interactive-accent-button-tertiary-on-muted-1-foreground-disabled: #b9c9d2;
  --interactive-accent-button-tertiary-on-muted-1-foreground-active: #0f155b;
  --interactive-accent-button-tertiary-on-muted-2-foreground: #285a93;
  --interactive-accent-button-tertiary-on-muted-2-foreground-hover: #0f155b;
  --interactive-accent-button-tertiary-on-muted-2-foreground-disabled: #b9c9d2;
  --interactive-accent-button-tertiary-on-muted-2-foreground-active: #0f155b;
  --interactive-accent-button-tertiary-on-strong-foreground: #ffffff;
  --interactive-accent-button-tertiary-on-strong-foreground-hover: #edfaff;
  --interactive-accent-button-tertiary-on-strong-foreground-disabled: #b9c9d2;
  --interactive-accent-button-tertiary-on-strong-foreground-active: #edfaff;
  --interactive-accent-button-tertiary-on-bold-foreground: #ffffff;
  --interactive-accent-button-tertiary-on-bold-foreground-hover: #edfaff;
  --interactive-accent-button-tertiary-on-bold-foreground-disabled: #b9c9d2;
  --interactive-accent-button-tertiary-on-bold-foreground-active: #edfaff;
  --interactive-accent-button-tertiary-on-gradient-foreground: #ffffff;
  --interactive-accent-button-tertiary-on-gradient-foreground-hover: #edfaff;
  --interactive-accent-button-tertiary-on-gradient-foreground-disabled: #b9c9d2;
  --interactive-accent-button-tertiary-on-gradient-foreground-active: #edfaff;
  --interactive-tabs-on-muted-1-selected: #0f155b;
  --interactive-tabs-on-muted-1-deselected: #6c7880;
  --interactive-tabs-on-muted-1-hover: #0f155b;
  --interactive-tabs-on-default-selected: #0f155b;
  --interactive-tabs-on-default-deselected: #6c7880;
  --interactive-tabs-on-default-hover: #0f155b;
  --interactive-tabs-on-muted-2-selected: #0f155b;
  --interactive-tabs-on-muted-2-deselected: #6c7880;
  --interactive-tabs-on-muted-2-hover: #0f155b;
  --interactive-tabs-on-strong-selected: #ffffff;
  --interactive-tabs-on-strong-deselected: #b9c9d2;
  --interactive-tabs-on-strong-hover: #ffffff;
  --interactive-tabs-on-bold-selected: #ffffff;
  --interactive-tabs-on-bold-deselected: #b9c9d2;
  --interactive-tabs-on-bold-hover: #ffffff;
  --interactive-tabs-on-gradient-selected: #ffffff;
  --interactive-tabs-on-gradient-deselected: #f4f8f9;
  --interactive-tabs-on-gradient-hover: #ffffff;
  --selector-color-default-resi: #009ae0; /** selector default-resi color */
  --selector-color-business: #002f87; /** business selector color */
  --selector-color-active: #455051;
  --selector-color-disabled: #b9c9d2;
  --selector-color-text: #202020;
  --border-radius-busi: 10px;
  --focus-ring: 2px solid #009ae0;
  --focus-ring-offset: 2px;
  --button-primary-color-text-foreground-default-on-default: #ffffff;
  --button-primary-color-text-foreground-hover-on-default: #ffffff;
  --button-primary-color-text-foreground-active-on-default: #ffffff;
  --button-primary-color-text-foreground-disabled-on-default: #ffffff;
  --button-primary-color-text-foreground-default-on-muted-1: #ffffff;
  --button-primary-color-text-foreground-hover-on-muted-1: #ffffff;
  --button-primary-color-text-foreground-active-on-muted-1: #ffffff;
  --button-primary-color-text-foreground-disabled-on-muted-1: #ffffff;
  --button-primary-color-text-foreground-default-on-muted-2: #ffffff;
  --button-primary-color-text-foreground-hover-on-muted-2: #ffffff;
  --button-primary-color-text-foreground-active-on-muted-2: #ffffff;
  --button-primary-color-text-foreground-disabled-on-muted-2: #ffffff;
  --button-primary-color-text-foreground-default-on-strong: #285a93;
  --button-primary-color-text-foreground-hover-on-strong: #285a93;
  --button-primary-color-text-foreground-active-on-strong: #285a93;
  --button-primary-color-text-foreground-disabled-on-strong: #ffffff;
  --button-primary-color-text-foreground-default-on-bold: #285a93;
  --button-primary-color-text-foreground-hover-on-bold: #285a93;
  --button-primary-color-text-foreground-active-on-bold: #285a93;
  --button-primary-color-text-foreground-disabled-on-bold: #ffffff;
  --button-primary-color-text-foreground-default-on-gradient: #285a93;
  --button-primary-color-text-foreground-hover-on-gradient: #285a93;
  --button-primary-color-text-foreground-active-on-gradient: #285a93;
  --button-primary-color-text-foreground-disabled-on-gradient: #ffffff;
  --button-primary-color-surface-default-on-default: #285a93;
  --button-primary-color-surface-hover-on-default: #0f155b;
  --button-primary-color-surface-active-on-default: #0f155b;
  --button-primary-color-surface-disabled-on-default: #b9c9d2;
  --button-primary-color-surface-default-on-muted-1: #285a93;
  --button-primary-color-surface-hover-on-muted-1: #0f155b;
  --button-primary-color-surface-active-on-muted-1: #0f155b;
  --button-primary-color-surface-disabled-on-muted-1: #b9c9d2;
  --button-primary-color-surface-default-on-muted-2: #285a93;
  --button-primary-color-surface-hover-on-muted-2: #0f155b;
  --button-primary-color-surface-active-on-muted-2: #0f155b;
  --button-primary-color-surface-disabled-on-muted-2: #b9c9d2;
  --button-primary-color-surface-default-on-strong: #ffffff;
  --button-primary-color-surface-hover-on-strong: #edfaff;
  --button-primary-color-surface-active-on-strong: #edfaff;
  --button-primary-color-surface-disabled-on-strong: #b9c9d2;
  --button-primary-color-surface-default-on-bold: #ffffff;
  --button-primary-color-surface-hover-on-bold: #edfaff;
  --button-primary-color-surface-active-on-bold: #edfaff;
  --button-primary-color-surface-disabled-on-bold: #b9c9d2;
  --button-primary-color-surface-default-on-gradient: #ffffff;
  --button-primary-color-surface-hover-on-on-gradient: #edfaff;
  --button-primary-color-surface-active-on-gradient: #edfaff;
  --button-primary-color-surface-disabled-on-gradient: #b9c9d2;
  --button-primary-color-border-default-on-default: #285a93;
  --button-primary-color-border-hover-on-default: #0f155b;
  --button-primary-color-border-active-on-default: #0f155b;
  --button-primary-color-border-disabled-on-default: #b9c9d2;
  --button-primary-color-border-default-on-muted-1: #285a93;
  --button-primary-color-border-hover-on-muted-1: #0f155b;
  --button-primary-color-border-active-on-muted-1: #0f155b;
  --button-primary-color-border-disabled-on-muted-1: #b9c9d2;
  --button-primary-color-border-default-on-muted-2: #285a93;
  --button-primary-color-border-hover-on-muted-2: #0f155b;
  --button-primary-color-border-active-on-muted-2: #0f155b;
  --button-primary-color-border-disabled-on-muted-2: #b9c9d2;
  --button-primary-color-border-default-on-strong: #ffffff;
  --button-primary-color-border-hover-on-strong: #80d5f9;
  --button-primary-color-border-active-on-strong: #edfaff;
  --button-primary-color-border-disabled-on-strong: #b9c9d2;
  --button-primary-color-border-default-on-bold: #ffffff;
  --button-primary-color-border-hover-on-bold: #edfaff;
  --button-primary-color-border-active-on-bold: #edfaff;
  --button-primary-color-border-disabled-on-bold: #b9c9d2;
  --button-primary-color-border-default-on-gradient: #ffffff;
  --button-primary-color-border-hover-on-on-gradient: #edfaff;
  --button-primary-color-border-active-on-gradient: #edfaff;
  --button-primary-color-border-disabled-on-gradient: #b9c9d2;
  --button-primary-color-icon-foreground-default-on-default: #ffffff;
  --button-primary-color-icon-foreground-hover-on-default: #ffffff;
  --button-primary-color-icon-foreground-active-on-default: #ffffff;
  --button-primary-color-icon-foreground-disabled-on-default: #ffffff;
  --button-primary-color-icon-foreground-default-on-muted-1: #ffffff;
  --button-primary-color-icon-foreground-hover-on-muted-1: #ffffff;
  --button-primary-color-icon-foreground-active-on-muted-1: #ffffff;
  --button-primary-color-icon-foreground-disabled-on-muted-1: #ffffff;
  --button-primary-color-icon-foreground-default-on-muted-2: #ffffff;
  --button-primary-color-icon-foreground-hover-on-muted-2: #ffffff;
  --button-primary-color-icon-foreground-active-on-muted-2: #ffffff;
  --button-primary-color-icon-foreground-disabled-on-muted-2: #ffffff;
  --button-primary-color-icon-foreground-default-on-strong: #285a93;
  --button-primary-color-icon-foreground-hover-on-strong: #285a93;
  --button-primary-color-icon-foreground-active-on-strong: #285a93;
  --button-primary-color-icon-foreground-disabled-on-strong: #ffffff;
  --button-primary-color-icon-foreground-default-on-bold: #285a93;
  --button-primary-color-icon-foreground-hover-on-bold: #285a93;
  --button-primary-color-icon-foreground-active-on-bold: #285a93;
  --button-primary-color-icon-foreground-disabled-on-bold: #ffffff;
  --button-primary-color-icon-foreground-default-on-gradient: #285a93;
  --button-primary-color-icon-foreground-hover-on-gradient: #285a93;
  --button-primary-color-icon-foreground-active-on-gradient: #285a93;
  --button-primary-color-icon-foreground-disabled-on-gradient: #ffffff;
  --button-primary-color-icon-surface-default-on-default: #285a93;
  --button-primary-color-icon-surface-hover-on-default: #0f155b;
  --button-primary-color-icon-surface-active-on-default: #0f155b;
  --button-primary-color-icon-surface-disabled-on-default: #b9c9d2;
  --button-primary-color-icon-surface-default-on-muted-1: #285a93;
  --button-primary-color-icon-surface-hover-on-muted-1: #0f155b;
  --button-primary-color-icon-surface-active-on-muted-1: #0f155b;
  --button-primary-color-icon-surface-disabled-on-muted-1: #b9c9d2;
  --button-primary-color-icon-surface-default-on-muted-2: #285a93;
  --button-primary-color-icon-surface-hover-on-muted-2: #0f155b;
  --button-primary-color-icon-surface-active-on-muted-2: #0f155b;
  --button-primary-color-icon-surface-disabled-on-muted-2: #b9c9d2;
  --button-primary-color-icon-surface-default-on-strong: #ffffff;
  --button-primary-color-icon-surface-hover-on-strong: #edfaff;
  --button-primary-color-icon-surface-active-on-strong: #edfaff;
  --button-primary-color-icon-surface-disabled-on-strong: #b9c9d2;
  --button-primary-color-icon-surface-default-on-bold: #ffffff;
  --button-primary-color-icon-surface-hover-on-bold: #edfaff;
  --button-primary-color-icon-surface-active-on-bold: #edfaff;
  --button-primary-color-icon-surface-disabled-on-bold: #b9c9d2;
  --button-primary-color-icon-surface-default-on-gradient: #ffffff;
  --button-primary-color-icon-surface-hover-on-gradient: #edfaff;
  --button-primary-color-icon-surface-active-on-gradient: #edfaff;
  --button-primary-color-icon-surface-disabled-on-gradient: #b9c9d2;
  --button-primary-alt-color-text-foreground-default: #285a93;
  --button-primary-alt-color-text-foreground-hover: #0f155b;
  --button-primary-alt-color-text-foreground-active: #161e7e;
  --button-primary-alt-color-text-foreground-disabled: #ffffff;
  --button-primary-alt-color-surface-default: #ffffff;
  --button-primary-alt-color-surface-hover: #edfaff;
  --button-primary-alt-color-surface-active: #edfaff;
  --button-primary-alt-color-surface-disabled: #b9c9d2;
  --button-primary-alt-color-border-default: #ffffff;
  --button-primary-alt-color-border-hover: #edfaff;
  --button-primary-alt-color-border-active: #edfaff;
  --button-primary-alt-color-border-disabled: #b9c9d2;
  --button-primary-alt-color-icon-foreground-default: #285a93;
  --button-primary-alt-color-icon-foreground-hover: #0f155b;
  --button-primary-alt-color-icon-foreground-active: #ffffff;
  --button-primary-alt-color-icon-foreground-disabled: #ffffff;
  --button-primary-alt-color-icon-surface-default: #ffffff;
  --button-primary-alt-color-icon-surface-hover: #0f155b;
  --button-primary-alt-color-icon-surface-active: #0f155b;
  --button-primary-alt-color-icon-surface-disabled: #b9c9d2;
  --button-secondary-color-text-foreground-default-on-default: #285a93;
  --button-secondary-color-text-foreground-hover-on-default: #0f155b;
  --button-secondary-color-text-foreground-active-on-default: #0f155b;
  --button-secondary-color-text-foreground-disabled-on-default: #b9c9d2;
  --button-secondary-color-text-foreground-default-on-muted-1: #285a93;
  --button-secondary-color-text-foreground-hover-on-muted-1: #0f155b;
  --button-secondary-color-text-foreground-active-on-muted-1: #0f155b;
  --button-secondary-color-text-foreground-disabled-on-muted-1: #b9c9d2;
  --button-secondary-color-text-foreground-default-on-muted-2: #285a93;
  --button-secondary-color-text-foreground-hover-on-muted-2: #0f155b;
  --button-secondary-color-text-foreground-active-on-muted-2: #0f155b;
  --button-secondary-color-text-foreground-disabled-on-muted-2: #b9c9d2;
  --button-secondary-color-text-foreground-default-on-strong: #ffffff;
  --button-secondary-color-text-foreground-hover-on-strong: #285a93;
  --button-secondary-color-text-foreground-active-on-strong: #285a93;
  --button-secondary-color-text-foreground-disabled-on-strong: #b9c9d2;
  --button-secondary-color-text-foreground-default-on-bold: #ffffff;
  --button-secondary-color-text-foreground-hover-on-bold: #285a93;
  --button-secondary-color-text-foreground-active-on-bold: #285a93;
  --button-secondary-color-text-foreground-disabled-on-bold: #b9c9d2;
  --button-secondary-color-text-foreground-default-on-gradient: #ffffff;
  --button-secondary-color-text-foreground-hover-on-gradient: #285a93;
  --button-secondary-color-text-foreground-active-on-gradient: #285a93;
  --button-secondary-color-text-foreground-disabled-on-gradient: #b9c9d2;
  --button-secondary-color-border-default-on-default: #285a93;
  --button-secondary-color-border-hover-on-default: #0f155b;
  --button-secondary-color-border-active-on-default: #0f155b;
  --button-secondary-color-border-disabled-on-default: #b9c9d2;
  --button-secondary-color-border-default-on-muted-1: #285a93;
  --button-secondary-color-border-hover-on-muted-1: #0f155b;
  --button-secondary-color-border-active-on-muted-1: #0f155b;
  --button-secondary-color-border-disabled-on-muted-1: #b9c9d2;
  --button-secondary-color-border-default-on-muted-2: #285a93;
  --button-secondary-color-border-hover-on-muted-2: #0f155b;
  --button-secondary-color-border-active-on-muted-2: #0f155b;
  --button-secondary-color-border-disabled-on-muted-2: #b9c9d2;
  --button-secondary-color-border-default-on-strong: #ffffff;
  --button-secondary-color-border-hover-on-strong: #ffffff;
  --button-secondary-color-border-active-on-strong: #ffffff;
  --button-secondary-color-border-disabled-on-strong: #b9c9d2;
  --button-secondary-color-border-default-on-bold: #ffffff;
  --button-secondary-color-border-hover-on-bold: #ffffff;
  --button-secondary-color-border-active-on-bold: #ffffff;
  --button-secondary-color-border-disabled-on-bold: #b9c9d2;
  --button-secondary-color-border-default-on-gradient: #ffffff;
  --button-secondary-color-border-hover-on-gradient: #ffffff;
  --button-secondary-color-border-active-on-gradient: #ffffff;
  --button-secondary-color-border-disabled-on-gradient: #b9c9d2;
  --button-secondary-color-icon-foreground-default-on-default: #285a93;
  --button-secondary-color-icon-foreground-hover-on-default: #0f155b;
  --button-secondary-color-icon-foreground-active-on-default: #0f155b;
  --button-secondary-color-icon-foreground-disabled-on-default: #b9c9d2;
  --button-secondary-color-icon-foreground-default-on-muted-1: #285a93;
  --button-secondary-color-icon-foreground-hover-on-muted-1: #0f155b;
  --button-secondary-color-icon-foreground-active-on-muted-1: #0f155b;
  --button-secondary-color-icon-foreground-disabled-on-muted-1: #b9c9d2;
  --button-secondary-color-icon-foreground-hover-on-muted-2: #0f155b;
  --button-secondary-color-icon-foreground-default-on-muted-2: #285a93;
  --button-secondary-color-icon-foreground-active-on-muted-2: #0f155b;
  --button-secondary-color-icon-foreground-disabled-on-muted-2: #b9c9d2;
  --button-secondary-color-icon-foreground-default-on-strong: #ffffff;
  --button-secondary-color-icon-foreground-hover-on-strong: #285a93;
  --button-secondary-color-icon-foreground-active-on-strong: #285a93;
  --button-secondary-color-icon-foreground-disabled-on-strong: #b9c9d2;
  --button-secondary-color-icon-foreground-default-on-bold: #ffffff;
  --button-secondary-color-icon-foreground-hover-on-bold: #ffffff;
  --button-secondary-color-icon-foreground-active-on-bold: #285a93;
  --button-secondary-color-icon-foreground-disabled-on-bold: #b9c9d2;
  --button-secondary-color-icon-foreground-default-on-gradient: #ffffff;
  --button-secondary-color-icon-foreground-hover-on-gradient: #ffffff;
  --button-secondary-color-icon-foreground-active-on-gradient: #285a93;
  --button-secondary-color-icon-foreground-disabled-on-gradient: #b9c9d2;
  --button-secondary-color-icon-border-default-on-default: #285a93;
  --button-secondary-color-icon-border-hover-on-default: #0f155b;
  --button-secondary-color-icon-border-active-on-default: #0f155b;
  --button-secondary-color-icon-border-disabled-on-default: #b9c9d2;
  --button-secondary-color-icon-border-default-on-muted-1: #285a93;
  --button-secondary-color-icon-border-hover-on-muted-1: #0f155b;
  --button-secondary-color-icon-border-active-on-muted-1: #0f155b;
  --button-secondary-color-icon-border-disabled-on-muted-1: #b9c9d2;
  --button-secondary-color-icon-border-default-on-muted-2: #285a93;
  --button-secondary-color-icon-border-hover-on-muted-2: #0f155b;
  --button-secondary-color-icon-border-active-on-muted-2: #0f155b;
  --button-secondary-color-icon-border-disabled-on-muted-2: #b9c9d2;
  --button-secondary-color-icon-border-default-on-strong: #ffffff;
  --button-secondary-color-icon-border-hover-on-strong: #ffffff;
  --button-secondary-color-icon-border-active-on-strong: #ffffff;
  --button-secondary-color-icon-border-disabled-on-strong: #b9c9d2;
  --button-secondary-color-icon-border-default-on-bold: #ffffff;
  --button-secondary-color-icon-border-hover-on-bold: #ffffff;
  --button-secondary-color-icon-border-active-on-bold: #ffffff;
  --button-secondary-color-icon-border-disabled-on-bold: #b9c9d2;
  --button-secondary-color-icon-border-default-on-gradient: #ffffff;
  --button-secondary-color-icon-border-hover-on-gradient: #ffffff;
  --button-secondary-color-icon-border-active-on-gradient: #ffffff;
  --button-secondary-color-icon-border-disabled-on-gradient: #b9c9d2;
  --button-secondary-color-surface-default-on-default: rgba(255, 255, 255, 0);
  --button-secondary-color-surface-disabled-on-default: rgba(255, 255, 255, 0);
  --button-secondary-color-surface-hover-on-default: rgba(255, 255, 255, 0);
  --button-secondary-color-surface-hover-on-strong: #ffffff;
  --button-secondary-alt-color-text-foreground-default: #ffffff;
  --button-secondary-alt-color-text-foreground-hover: #0f155b;
  --button-secondary-alt-color-text-foreground-active: #0f155b;
  --button-secondary-alt-color-text-foreground-disabled: #b9c9d2;
  --button-secondary-alt-color-border-default: #ffffff;
  --button-secondary-alt-color-border-disabled: #b9c9d2;
  --button-secondary-alt-color-border-hover: #ffffff;
  --button-secondary-alt-color-border-active: #ffffff;
  --button-secondary-alt-color-icon-foreground-default: #ffffff;
  --button-secondary-alt-color-icon-foreground-hover: #0f155b;
  --button-secondary-alt-color-icon-foreground-active: #0f155b;
  --button-secondary-alt-color-icon-foreground-disabled: #b9c9d2;
  --button-secondary-alt-color-icon-border-default: #285a93;
  --button-secondary-alt-color-icon-border-hover: #0f155b;
  --button-secondary-alt-color-icon-border-active: #0f155b;
  --button-secondary-alt-color-icon-border-disabled: #b9c9d2;
  --button-secondary-alt-color-surface-default: rgba(255, 255, 255, 0);
  --button-secondary-alt-color-surface-disabled: rgba(255, 255, 255, 0);
  --button-secondary-alt-color-surface-hover: #ffffff;
  --button-secondary-alt-color-surface-active: #ffffff;
  --button-tertiary-color-text-foreground-default: #285a93;
  --button-tertiary-color-text-foreground-disabled: #b9c9d2;
  --button-tertiary-color-text-foreground-hover-active: #0f155b;
  --button-tertiary-color-text-foreground-default-on-default: #285a93;
  --button-tertiary-color-text-foreground-hover-on-default: #0f155b;
  --button-tertiary-color-text-foreground-active-on-default: #0f155b;
  --button-tertiary-color-text-foreground-disabled-on-default: #b9c9d2;
  --button-tertiary-color-text-foreground-default-on-muted-1: #285a93;
  --button-tertiary-color-text-foreground-hover-on-muted-1: #0f155b;
  --button-tertiary-color-text-foreground-active-on-muted-1: #0f155b;
  --button-tertiary-color-text-foreground-disabled-on-muted-1: #b9c9d2;
  --button-tertiary-color-text-foreground-default-on-muted-2: #285a93;
  --button-tertiary-color-text-foreground-hover-on-muted-2: #0f155b;
  --button-tertiary-color-text-foreground-active-on-muted-2: #0f155b;
  --button-tertiary-color-text-foreground-disabled-on-muted-2: #b9c9d2;
  --button-tertiary-color-text-foreground-default-on-strong: #ffffff;
  --button-tertiary-color-text-foreground-hover-on-strong: #edfaff;
  --button-tertiary-color-text-foreground-active-on-strong: #edfaff;
  --button-tertiary-color-text-foreground-disabled-on-strong: #b9c9d2;
  --button-tertiary-color-text-foreground-default-on-bold: #ffffff;
  --button-tertiary-color-text-foreground-hover-on-bold: #edfaff;
  --button-tertiary-color-text-foreground-active-on-bold: #edfaff;
  --button-tertiary-color-text-foreground-disabled-on-bold: #b9c9d2;
  --button-tertiary-color-text-foreground-default-on-gradient: #ffffff;
  --button-tertiary-color-text-foreground-hover-on-gradient: #edfaff;
  --button-tertiary-color-text-foreground-active-on-gradient: #edfaff;
  --button-tertiary-color-text-foreground-disabled-on-gradient: #b9c9d2;
  --button-tertiary-alt-color-text-foreground-default: #ffffff;
  --button-tertiary-alt-color-text-foreground-hover: #edfaff;
  --button-tertiary-alt-color-text-foreground-active: #edfaff;
  --button-tertiary-alt-color-text-foreground-disabled: #b9c9d2;
  --button-radius-default: 110px;
  --button-radius-icon: 999px;
  --button-large-spacing-padding-horizontal: 32px;
  --button-large-spacing-padding-vertical: 16px;
  --button-large-typography-text-size-font-family: 'Cera Pro';
  --button-large-typography-text-size-font-weight: 700;
  --button-large-typography-text-size-line-height: 20px;
  --button-large-typography-text-size-font-size: 20px;
  --button-large-typography-text-size-letter-spacing: 0.01em;
  --button-medium-spacing-padding-horizontal: 32px;
  --button-medium-spacing-padding-vertical: 14px;
  --button-medium-typography-text-size-font-family: 'Cera Pro';
  --button-medium-typography-text-size-font-weight: 700;
  --button-medium-typography-text-size-line-height: 18px;
  --button-medium-typography-text-size-font-size: 18px;
  --button-medium-typography-text-size-letter-spacing: 0.01em;
  --button-small-spacing-padding-horizontal: 20px;
  --button-small-spacing-padding-vertical: 12px;
  --button-small-typography-text-size-font-family: 'Cera Pro';
  --button-small-typography-text-size-font-weight: 700;
  --button-small-typography-text-size-line-height: 16px;
  --button-small-typography-text-size-font-size: 16px;
  --button-small-typography-text-size-letter-spacing: 0.01em;
  --default: 2px;
  --link-text-breakpoint-xxl-xl-font-family: 'Cera Pro';
  --link-text-breakpoint-xxl-xl-font-weight: 700;
  --link-text-breakpoint-xxl-xl-line-height: 20px;
  --link-text-breakpoint-xxl-xl-font-size: 20px;
  --link-text-breakpoint-xxl-xl-letter-spacing: 0.01em;
  --link-text-breakpoint-lg-md-font-family: 'Cera Pro';
  --link-text-breakpoint-lg-md-font-weight: 700;
  --link-text-breakpoint-lg-md-line-height: 18px;
  --link-text-breakpoint-lg-md-font-size: 18px;
  --link-text-breakpoint-lg-md-letter-spacing: 0.01em;
  --link-text-breakpoint-sm-xs-font-family: 'Cera Pro';
  --link-text-breakpoint-sm-xs-font-weight: 700;
  --link-text-breakpoint-sm-xs-line-height: 16px;
  --link-text-breakpoint-sm-xs-font-size: 16px;
  --link-text-breakpoint-sm-xs-letter-spacing: 0.01em;
  --link-strong-background-default: #ffffff;
  --link-strong-background-disabled: #b9c9d2;
  --link-strong-background-hover: #ebeff0;
  --link-color-text-default: #285a93;
  --link-color-text-hover: #0f155b;
  --link-color-text-disabled: #b9c9d2;
  --link-color-icon-default: #285a93;
  --link-color-icon-hover: #0f155b;
  --link-color-icon-disabled: #b9c9d2;
  --oliver-input-color-surface: #202020;
  --oliver-input-color-icon: linear-gradient(135deg, #00aaf4 0%, #00bfa4 57%, #00d258 100%);
  --oliver-input-color-placeholder: #6c7880;
  --oliver-input-color-filled: #202020;
  --oliver-input-border-radius-xxl-xs: 999px;
  --oliver-input-spacing-after-icon-xxl-l: 44px;
  --oliver-input-spacing-after-icon-m-xs: 8px;
  --oliver-input-spacing-vertical-xxl-l: 28px;
  --oliver-input-spacing-vertical-m-xs: 28px;
  --oliver-input-spacing-horizontal-xxl-l: 28px;
  --oliver-input-spacing-horizontal-s-xs: 28px;
  --oliver-input-typography-placeholder-xxl-m-font-family: 'Cera Pro';
  --oliver-input-typography-placeholder-xxl-m-font-weight: 500;
  --oliver-input-typography-placeholder-xxl-m-font-size: 18px;
  --oliver-input-typography-placeholder-xxl-m-line-height: 24px;
  --oliver-input-typography-placeholder-xxl-m-letter-spacing: 0em;
  --oliver-input-typography-placeholder-s-xs-font-family: 'Cera Pro';
  --oliver-input-typography-placeholder-s-xs-font-weight: 500;
  --oliver-input-typography-placeholder-s-xs-font-size: 16px;
  --oliver-input-typography-placeholder-s-xs-line-height: 23px;
  --oliver-input-typography-placeholder-s-xs-letter-spacing: 0em;
  --oliver-input-sizing-icon-xxl-l: 54px;
  --oliver-input-sizing-icon-m-xs: 32px;
  --skeleton-border-radius-default: 10px;
  --skeleton-border-radius-circle: 999px;
  --skeleton-color-background-default: #202020;
  --skeleton-color-background-on-dark: #ffffff;
  --spinner-size-sm: 16px;
  --spinner-size-md: 24px;
  --spinner-size-lg: 48px;
  --spinner-size-xl: 72px;
  --spinner-color-solid: #00aeef;
  --spinner-color-gradient-start: #00aaf4;
  --spinner-color-gradient-end: #00d258;
  --toggle-color-deselected-border: #6c7880;
  --toggle-color-deselected-dot: #6c7880;
  --toggle-color-selected-background: #0f155b;
  --toggle-color-selected-dot: #ffffff;
  --toggle-color-disabled-background: #b9c9d2;
  --toggle-color-disabled-dot: #ffffff;
  --toggle-color-label-text: #202020;
  --toggle-color-border: #285a93;
  --toggle-typography-label-body-font-family: 'Cera Pro';
  --toggle-typography-label-body-font-weight: 400;
  --toggle-typography-label-body-font-size: 16px;
  --toggle-typography-label-body-line-height: 23px;
  --toggle-typography-label-body-letter-spacing: 0em;
  --toggle-spacing-after-toggle: 12px;
  --toggle-spacing-inside: 4px;
  --toggle-border-radius: 999px;
  --toggle-border-width: 2px;
  --skeleton-border-radius-default: 10px;
  --skeleton-border-radius-circle: 999px;
  --skeleton-color-background-default: #202020;
  --skeleton-color-background-on-dark: #ffffff;
  --spinner-size-sm: 16px;
  --spinner-size-md: 24px;
  --spinner-size-lg: 48px;
  --spinner-size-xl: 72px;
  --spinner-color-solid: #00aeef;
  --spinner-color-gradient-start: #00aaf4;
  --spinner-color-gradient-end: #00d258;
  --address-capture-input-color-foreground-placeholder-text-on-default: #6c7880;
  --address-capture-input-color-foreground-input-text-on-default: #202020;
  --address-capture-input-color-foreground-icon: #009ae0;
  --address-capture-input-color-foreground-placeholder-text-on-muted-1: #6c7880;
  --address-capture-input-color-foreground-input-text-on-muted-1: #202020;
  --address-capture-input-color-surface-input-on-default: #f4f8f9;
  --address-capture-input-color-surface-input-on-muted-1: #ffffff;
  --address-capture-input-color-surface-container: #ebeff0;
  --address-capture-input-typography-input-body-xxl-m-font-family: 'Cera Pro';
  --address-capture-input-typography-input-body-xxl-m-font-weight: 400;
  --address-capture-input-typography-input-body-xxl-m-font-size: 18px;
  --address-capture-input-typography-input-body-xxl-m-line-height: 24px;
  --address-capture-input-typography-input-body-xxl-m-letter-spacing: 0em;
  --address-capture-input-typography-input-body-s-xs-font-family: 'Cera Pro';
  --address-capture-input-typography-input-body-s-xs-font-weight: 400;
  --address-capture-input-typography-input-body-s-xs-font-size: 16px;
  --address-capture-input-typography-input-body-s-xs-line-height: 23px;
  --address-capture-input-typography-input-body-s-xs-letter-spacing: 0em;
  --address-capture-input-typography-title-xxl-xs-font-family: 'Cera Pro';
  --address-capture-input-typography-title-xxl-xs-font-weight: 700;
  --address-capture-input-typography-title-xxl-xs-line-height: 30px;
  --address-capture-input-typography-title-xxl-xs-font-size: 22px;
  --address-capture-input-typography-title-xxl-xs-letter-spacing: -0.02em;
  --address-capture-input-typography-title-xxl-xl-font-family: 'Cera Pro';
  --address-capture-input-typography-title-xxl-xl-font-weight: 700;
  --address-capture-input-typography-title-xxl-xl-letter-spacing: -0.02em;
  --address-capture-input-typography-title-xxl-xl-font-size: 24px;
  --address-capture-input-typography-title-xxl-xl-text-case: none;
  --address-capture-input-typography-title-xxl-xl-line-height: 32px;
  --address-capture-input-typography-title-l-xs-font-family: 'Cera Pro';
  --address-capture-input-typography-title-l-xs-font-weight: 700;
  --address-capture-input-typography-title-l-xs-letter-spacing: -0.02em;
  --address-capture-input-typography-title-l-xs-font-size: 16px;
  --address-capture-input-typography-title-l-xs-text-case: none;
  --address-capture-input-typography-title-l-xs-line-height: 22px;
  --address-capture-input-typography-body-xxl-xs-font-family: 'Cera Pro';
  --address-capture-input-typography-body-xxl-xs-font-weight: 400;
  --address-capture-input-typography-body-xxl-xs-font-size: 16px;
  --address-capture-input-typography-body-xxl-xs-line-height: 23px;
  --address-capture-input-typography-body-xxl-xs-letter-spacing: 0em;
  --address-capture-input-typography-disclaimer-xxl-xs-font-family: 'Cera Pro';
  --address-capture-input-typography-disclaimer-xxl-xs-font-weight: 400;
  --address-capture-input-typography-disclaimer-xxl-xs-font-size: 13px;
  --address-capture-input-typography-disclaimer-xxl-xs-line-height: 20px;
  --address-capture-input-typography-disclaimer-xxl-xs-letter-spacing: 0em;
  --address-capture-input-border-radius-xxl-xs: 999px;
  --address-capture-input-spacing-vertical-xxl-l: 20px;
  --address-capture-input-spacing-vertical-m-xs: 12px;
  --address-capture-input-spacing-horizontal-xxl-l: 32px;
  --address-capture-input-spacing-horizontal-m-xs: 20px;
  --address-capture-input-spacing-after-icon-xxl-l: 16px;
  --address-capture-input-spacing-after-icon-m-xs: 8px;
  --address-capture-input-spacing-after-input-xxl-l: 20px;
  --address-capture-input-spacing-after-input-m-s: 12px;
  --address-capture-input-spacing-after-input-xs: 8px;
  --address-capture-field-sizing-icon-xxl-l: 32px;
  --address-capture-field-sizing-icon-m-xs: 24px;
  --address-capture-field-sizing-loader-xxl-xs: 50px;
  --banner-promo-color-border: linear-gradient(135deg, #00aaf4 0%, #00bfa4 57%, #00d258 100%);
  --banner-promo-color-body: #202020;
  --banner-promo-color-surface: #ffffff;
  --banner-promo-border-radius: 10px;
  --banner-promo-typography-xxl-m-font-family: 'Cera Pro';
  --banner-promo-typography-xxl-m-font-weight: 500;
  --banner-promo-typography-xxl-m-font-size: 24px;
  --banner-promo-typography-xxl-m-line-height: 34px;
  --banner-promo-typography-xxl-m-letter-spacing: 0em;
  --banner-promo-typography-s-xs-font-family: 'Cera Pro';
  --banner-promo-typography-s-xs-font-weight: 500;
  --banner-promo-typography-s-xs-font-size: 18px;
  --banner-promo-typography-s-xs-line-height: 24px;
  --banner-promo-typography-s-xs-letter-spacing: 0em;
  --banner-promo-spacing-inside: 32px;
  --banner-promo-spacing-icon-right-xxl-l: 20px;
  --banner-promo-spacing-body-right-xxl-l: 32px;
  --banner-promo-spacing-body-bottom-m-xs: 28px;
  --banner-promo-sizing-icon: 54px;
  --banner-promo-border-width: 2px;
  --oliver-color-header: #202020;
  --oliver-color-disclaimer-body: #455051;
  --oliver-color-icon: linear-gradient(135deg, #00aaf4 0%, #00bfa4 57%, #00d258 100%);
  --oliver-typography-title-xxl-xs-font-family: 'Cera Pro';
  --oliver-typography-title-xxl-xs-font-weight: 700;
  --oliver-typography-title-xxl-xs-line-height: 30px;
  --oliver-typography-title-xxl-xs-font-size: 22px;
  --oliver-typography-title-xxl-xs-letter-spacing: -0.02em;
  --oliver-typography-disclaimer-body-xxl-xs-font-family: 'Cera Pro';
  --oliver-typography-disclaimer-body-xxl-xs-font-weight: 400;
  --oliver-typography-disclaimer-body-xxl-xs-font-size: 13px;
  --oliver-typography-disclaimer-body-xxl-xs-line-height: 20px;
  --oliver-typography-disclaimer-body-xxl-xs-letter-spacing: 0em;
  --after-headline-xxl-xs: 32px;
  --before-disclaimer-xxl-xs: 20px;
  --address-capture-color-title: #202020;
  --address-capture-color-body: #202020;
  --address-capture-color-disclaimer: #455051;
  --address-capture-spacing-before-subhead-xxl-l: 24px;
  --address-capture-spacing-before-subhead-m-xs: 16px;
  --address-capture-spacing-before-help-ctas-xxl-l: 24px;
  --address-capture-spacing-before-help-ctas-m-xs: 16px;
  --address-capture-spacing-between-help-ctas-xxl-l: 28px;
  --address-capture-spacing-between-help-ctas-m-xs: 20px;
  --address-capture-spacing-after-header-frame-xxl-l: 40px;
  --address-capture-spacing-after-header-frame-m-xs: 32px;
  --address-capture-spacing-before-disclaimer-xxl-l: 28px;
  --address-capture-spacing-before-disclaimer-m-xs: 20px;
  --address-capture-spacing-after-dropdown-xxl-l: 28px;
  --address-capture-spacing-after-dropdown-m-xs: 20px;
  --address-capture-spacing-section-xxl: 199px;
  --address-capture-spacing-section-xl: 162px;
  --address-capture-spacing-section-l: 20px 96px;
  --address-capture-spacing-section-m: 20px 48px;
  --address-capture-spacing-section-s-xs: 20px 24px 20px 24px;
  --address-capture-sticky-color-title: #0f155b;
  --address-capture-sticky-color-body: #202020;
  --address-capture-sticky-color-surface: #cff0ff;
  --address-capture-sticky-container-xxl: 20px 72px;
  --address-capture-sticky-container-xl: 20px 56px;
  --address-capture-sticky-container-l-m: 20px 48px 28px 48px;
  --address-capture-sticky-container-s-xs: 12px 24px 20px 24px;
  --address-capture-sticky-container-title-after-xxl-xl: 40px;
  --address-capture-sticky-container-title-after-l-m: 20px;
  --address-capture-sticky-container-title-after-s-xs: 16px;
  --address-capture-sticky-container-body-after-xxl-l: 40px;
  --address-capture-sticky-container-body-after-l-m: 20px;
  --address-capture-sticky-container-body-after-s-xs: 16px;
  --address-capture-sticky-border: 1px solid #f4f8f9;
}
