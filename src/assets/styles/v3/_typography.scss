@import '../../theme/v3/css/tokens-Business';
@import '../../theme/v3/css/tokens-Residential';

@mixin getFontStyles($fontWeight, $fontSize, $lineHeight, $letterSpacing: normal, $textCase: none, $fontColor) {
  font-weight: $fontWeight;
  font-size: $fontSize;
  line-height: $lineHeight;
  letter-spacing: $letterSpacing;
  text-transform: $textCase;
  color: $fontColor;
}

// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Header, Global-Nav, Form Input (Custom Mixins) ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ //

@mixin link-description-1-regular {
  @include getFontStyles(
    var(--cox-fontweights-cera-pro-regular),
    var(--cox-fontsize-100),
    var(--cox-lineheight-500),
    var(--cox-letterspacing-500),
    none,
    var(--foreground-on-default-2)
  );
}

@mixin cox-resi-text-label-2-small {
  @include getFontStyles(
    var(--cox-fontweights-cera-pro-medium),
    var(--cox-fontsize-400),
    var(--cox-lineheight-900),
    var(--cox-letterspacing-500),
    none,
    var(--foreground-on-default-2)
  );
}

/********************************** Interactive */

/*********** Interactive 1 */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-interactive1 {
  @include getFontStyles(
    var(--cox-text-interactive1-font-weight),
    var(--cox-text-interactive1-font-size),
    var(--cox-text-interactive1-line-height),
    var(--cox-text-interactive1-letter-spacing),
    none,
    var(--color-blue-600)
  );
}
/* Class */
.cox-text-interactive1 {
  @include cox-text-interactive1;
}

/*********** Interactive 2 */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-interactive2 {
  @include getFontStyles(
    var(--cox-text-interactive2-font-weight),
    var(--cox-text-interactive2-font-size),
    var(--cox-text-interactive2-line-height),
    var(--cox-text-interactive2-letter-spacing),
    none,
    var(--color-blue-600)
  );
}
/* Class */
.cox-text-interactive2 {
  @include cox-text-interactive2;
}

/*********** Interactive 3 */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-interactive3 {
  @include getFontStyles(
    var(--cox-text-interactive3-font-weight),
    var(--cox-text-interactive3-font-size),
    var(--cox-text-interactive3-line-height),
    var(--cox-text-interactive3-letter-spacing),
    none,
    var(--color-blue-600)
  );
}
/* Class */
.cox-text-interactive3 {
  @include cox-text-interactive3;
}

/********************************** Eyebrow */

/*********** Eyebrow 1 */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-eyebrow1 {
  @include getFontStyles(
    var(--cox-text-eyebrow1-font-weight),
    var(--cox-text-eyebrow1-font-size),
    var(--cox-text-eyebrow1-line-height),
    var(--cox-text-eyebrow1-letter-spacing),
    var(--cox-text-eyebrow1-text-case),
    var(--foreground-on-default-4)
  );
}
/* Class */
.cox-text-eyebrow1 {
  @include cox-text-eyebrow1;
}

/*********** Eyebrow 2 */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-eyebrow2 {
  @include getFontStyles(
    var(--cox-text-eyebrow2-font-weight),
    var(--cox-text-eyebrow2-font-size),
    var(--cox-text-eyebrow2-line-height),
    var(--cox-text-eyebrow2-letter-spacing),
    var(--cox-text-eyebrow2-text-case),
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-eyebrow2 {
  @include cox-text-eyebrow2;
}

/*********** Eyebrow 3 */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-eyebrow3 {
  @include getFontStyles(
    var(--cox-text-eyebrow3-font-weight),
    var(--cox-text-eyebrow3-font-size),
    var(--cox-text-eyebrow3-line-height),
    var(--cox-text-eyebrow3-letter-spacing),
    var(--cox-text-eyebrow3-text-case),
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-eyebrow3 {
  @include cox-text-eyebrow3;
}

/********************************** Display */

/*********** Display 1 */
/* Mixin */
@mixin cox-text-display1 {
  @include getFontStyles(
    var(--cox-text-display1-s-xs-font-weight),
    var(--cox-text-display1-s-xs-font-size),
    var(--cox-text-display1-s-xs-line-height),
    var(--cox-text-display1-s-xs-letter-spacing),
    var(--cox-text-display1-s-xs-text-case),
    var(--foreground-on-default-1)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-display1-l-m-font-weight),
      var(--cox-text-display1-l-m-font-size),
      var(--cox-text-display1-l-m-line-height),
      var(--cox-text-display1-l-m-letter-spacing),
      var(--cox-text-display1-l-m-text-case),
      var(--foreground-on-default-1)
    );
  }
  @media (min-width: $xl) {
    @include getFontStyles(
      var(--cox-text-display1-xxl-xl-font-weight),
      var(--cox-text-display1-xxl-xl-font-size),
      var(--cox-text-display1-xxl-xl-line-height),
      var(--cox-text-display1-xxl-xl-letter-spacing),
      var(--cox-text-display1-xxl-xl-text-case),
      var(--foreground-on-default-1)
    );
  }
}
/* Class */
.cox-text-display1 {
  @include cox-text-display1;
}

/*********** Display 2 */
/* Mixin */
@mixin cox-text-display2 {
  @include getFontStyles(
    var(--cox-text-display2-s-xs-font-weight),
    var(--cox-text-display2-s-xs-font-size),
    var(--cox-text-display2-s-xs-line-height),
    var(--cox-text-display2-s-xs-letter-spacing),
    var(--cox-text-display2-s-xs-text-case),
    var(--foreground-on-default-1)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-display2-l-m-font-weight),
      var(--cox-text-display2-l-m-font-size),
      var(--cox-text-display2-l-m-line-height),
      var(--cox-text-display2-l-m-letter-spacing),
      var(--cox-text-display2-l-m-text-case),
      var(--foreground-on-default-1)
    );
  }
  @media (min-width: $xl) {
    @include getFontStyles(
      var(--cox-text-display2-xxl-xl-font-weight),
      var(--cox-text-display2-xxl-xl-font-size),
      var(--cox-text-display2-xxl-xl-line-height),
      var(--cox-text-display2-xxl-xl-letter-spacing),
      var(--cox-text-display2-xxl-xl-text-case),
      var(--foreground-on-default-1)
    );
  }
}
/* Class */
.cox-text-display2 {
  @include cox-text-display2;
}

/*********** Display 3 */
/* Mixin */
@mixin cox-text-display3 {
  @include getFontStyles(
    var(--cox-text-display3-s-xs-font-weight),
    var(--cox-text-display3-s-xs-font-size),
    var(--cox-text-display3-s-xs-line-height),
    var(--cox-text-display3-s-xs-letter-spacing),
    var(--cox-text-display3-s-xs-text-case),
    var(--foreground-on-default-1)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-display3-l-m-font-weight),
      var(--cox-text-display3-l-m-font-size),
      var(--cox-text-display3-l-m-line-height),
      var(--cox-text-display3-l-m-letter-spacing),
      var(--cox-text-display3-l-m-text-case),
      var(--foreground-on-default-1)
    );
  }
  @media (min-width: $xl) {
    @include getFontStyles(
      var(--cox-text-display3-xxl-xl-font-weight),
      var(--cox-text-display3-xxl-xl-font-size),
      var(--cox-text-display3-xxl-xl-line-height),
      var(--cox-text-display3-xxl-xl-letter-spacing),
      var(--cox-text-display3-xxl-xl-text-case),
      var(--foreground-on-default-1)
    );
  }
}
/* Class */
.cox-text-display3 {
  @include cox-text-display3;
}

/********************************** Heading */

/********************* Heading 1 */
/* Mixin */
@mixin cox-text-heading1 {
  @include getFontStyles(
    var(--cox-text-heading1-s-xs-font-weight),
    var(--cox-text-heading1-s-xs-font-size),
    var(--cox-text-heading1-s-xs-line-height),
    var(--cox-text-heading1-s-xs-letter-spacing),
    var(--cox-text-heading1-s-xs-text-case),
    var(--foreground-on-default-1)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-heading1-l-m-font-weight),
      var(--cox-text-heading1-l-m-font-size),
      var(--cox-text-heading1-l-m-line-height),
      var(--cox-text-heading1-l-m-letter-spacing),
      var(--cox-text-heading1-l-m-text-case),
      var(--foreground-on-default-1)
    );
  }
  @media (min-width: $xl) {
    @include getFontStyles(
      var(--cox-text-heading1-xxl-xl-font-weight),
      var(--cox-text-heading1-xxl-xl-font-size),
      var(--cox-text-heading1-xxl-xl-line-height),
      var(--cox-text-heading1-xxl-xl-letter-spacing),
      var(--cox-text-heading1-xxl-xl-text-case),
      var(--foreground-on-default-1)
    );
  }
}
/* Class */
.cox-text-heading1 {
  @include cox-text-heading1;
}

/********************* Heading 2 */
/* Mixin */
@mixin cox-text-heading2 {
  @include getFontStyles(
    var(--cox-text-heading2-s-xs-font-weight),
    var(--cox-text-heading2-s-xs-font-size),
    var(--cox-text-heading2-s-xs-line-height),
    var(--cox-text-heading2-s-xs-letter-spacing),
    var(--cox-text-heading2-s-xs-text-case),
    var(--foreground-on-default-1)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-heading2-l-m-font-weight),
      var(--cox-text-heading2-l-m-font-size),
      var(--cox-text-heading2-l-m-line-height),
      var(--cox-text-heading2-l-m-letter-spacing),
      var(--cox-text-heading2-l-m-text-case),
      var(--foreground-on-default-1)
    );
  }
  @media (min-width: $xl) {
    @include getFontStyles(
      var(--cox-text-heading2-xxl-xl-font-weight),
      var(--cox-text-heading2-xxl-xl-font-size),
      var(--cox-text-heading2-xxl-xl-line-height),
      var(--cox-text-heading2-xxl-xl-letter-spacing),
      var(--cox-text-heading2-xxl-xl-text-case),
      var(--foreground-on-default-1)
    );
  }
}
/* Class */
.cox-text-heading2 {
  @include cox-text-heading2;
}

/********************* Heading 3 */
/* Mixin */
@mixin cox-text-heading3 {
  @include getFontStyles(
    var(--cox-text-heading3-s-xs-font-weight),
    var(--cox-text-heading3-s-xs-font-size),
    var(--cox-text-heading3-s-xs-line-height),
    var(--cox-text-heading3-s-xs-letter-spacing),
    var(--cox-text-heading3-s-xs-text-case),
    var(--foreground-on-default-1)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-heading3-l-m-font-weight),
      var(--cox-text-heading3-l-m-font-size),
      var(--cox-text-heading3-l-m-line-height),
      var(--cox-text-heading3-l-m-letter-spacing),
      var(--cox-text-heading3-l-m-text-case),
      var(--foreground-on-default-1)
    );
  }
  @media (min-width: $xl) {
    @include getFontStyles(
      var(--cox-text-heading3-xxl-xl-font-weight),
      var(--cox-text-heading3-xxl-xl-font-size),
      var(--cox-text-heading3-xxl-xl-line-height),
      var(--cox-text-heading3-xxl-xl-letter-spacing),
      var(--cox-text-heading3-xxl-xl-text-case),
      var(--foreground-on-default-1)
    );
  }
}
/* Class */
.cox-text-heading3 {
  @include cox-text-heading3;
}

/********************* Heading 4 */
/* Mixin */
@mixin cox-text-heading4 {
  @include getFontStyles(
    var(--cox-text-heading4-s-xs-font-weight),
    var(--cox-text-heading4-s-xs-font-size),
    var(--cox-text-heading4-s-xs-line-height),
    var(--cox-text-heading4-s-xs-letter-spacing),
    var(--cox-text-heading4-s-xs-text-case),
    var(--foreground-on-default-1)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-heading4-l-m-font-weight),
      var(--cox-text-heading4-l-m-font-size),
      var(--cox-text-heading4-l-m-line-height),
      var(--cox-text-heading4-l-m-letter-spacing),
      var(--cox-text-heading4-l-m-text-case),
      var(--foreground-on-default-1)
    );
  }
  @media (min-width: $xl) {
    @include getFontStyles(
      var(--cox-text-heading4-xxl-xl-font-weight),
      var(--cox-text-heading4-xxl-xl-font-size),
      var(--cox-text-heading4-xxl-xl-line-height),
      var(--cox-text-heading4-xxl-xl-letter-spacing),
      var(--cox-text-heading4-xxl-xl-text-case),
      var(--foreground-on-default-1)
    );
  }
}
/* Class */
.cox-text-heading4 {
  @include cox-text-heading4;
}

/********************* Heading 5 */
/* Mixin */
@mixin cox-text-heading5 {
  @include getFontStyles(
    var(--cox-text-heading5-s-xs-font-weight),
    var(--cox-text-heading5-s-xs-font-size),
    var(--cox-text-heading5-s-xs-line-height),
    var(--cox-text-heading5-s-xs-letter-spacing),
    var(--cox-text-heading5-s-xs-text-case),
    var(--foreground-on-default-1)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-heading5-l-m-font-weight),
      var(--cox-text-heading5-l-m-font-size),
      var(--cox-text-heading5-l-m-line-height),
      var(--cox-text-heading5-l-m-letter-spacing),
      var(--cox-text-heading5-l-m-text-case),
      var(--foreground-on-default-1)
    );
  }
  @media (min-width: $xl) {
    @include getFontStyles(
      var(--cox-text-heading5-xxl-xl-font-weight),
      var(--cox-text-heading5-xxl-xl-font-size),
      var(--cox-text-heading5-xxl-xl-line-height),
      var(--cox-text-heading5-xxl-xl-letter-spacing),
      var(--cox-text-heading5-xxl-xl-text-case),
      var(--foreground-on-default-1)
    );
  }
}
/* Class */
.cox-text-heading5 {
  @include cox-text-heading5;
}

/********************* Heading 6 */
/* Mixin */
@mixin cox-text-heading6 {
  @include getFontStyles(
    var(--cox-text-heading6-l-xs-font-weight),
    var(--cox-text-heading6-l-xs-font-size),
    var(--cox-text-heading6-l-xs-line-height),
    var(--cox-text-heading6-l-xs-letter-spacing),
    var(--cox-text-heading6-l-xs-text-case),
    var(--foreground-on-default-1)
  );
  @media (min-width: $xl) {
    @include getFontStyles(
      var(--cox-text-heading6-xxl-xl-font-weight),
      var(--cox-text-heading6-xxl-xl-font-size),
      var(--cox-text-heading6-xxl-xl-line-height),
      var(--cox-text-heading6-xxl-xl-letter-spacing),
      var(--cox-text-heading6-xxl-xl-text-case),
      var(--foreground-on-default-1)
    );
  }
}
/* Class */
.cox-text-heading6 {
  @include cox-text-heading6;
}

/********************************** Title */

/********************* Title 1 - Medium */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-title1-medium {
  @include getFontStyles(
    var(--cox-text-title1-medium-font-weight),
    var(--cox-text-title1-medium-font-size),
    var(--cox-text-title1-medium-line-height),
    var(--cox-text-title1-medium-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-title1-medium {
  @include cox-text-title1-medium;
}

/********************* Title 2 - Medium */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-title2-medium {
  @include getFontStyles(
    var(--cox-text-title2-medium-font-weight),
    var(--cox-text-title2-medium-font-size),
    var(--cox-text-title2-medium-line-height),
    var(--cox-text-title2-medium-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-title2-medium {
  @include cox-text-title2-medium;
}

/********************* Title 3 - Medium */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-title3-medium {
  @include getFontStyles(
    var(--cox-text-title3-medium-font-weight),
    var(--cox-text-title3-medium-font-size),
    var(--cox-text-title3-medium-line-height),
    var(--cox-text-title3-medium-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-title3-medium {
  @include cox-text-title3-medium;
}

/********************* Title 4 - Medium */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-title4-medium {
  @include getFontStyles(
    var(--cox-text-title4-medium-font-weight),
    var(--cox-text-title4-medium-font-size),
    var(--cox-text-title4-medium-line-height),
    var(--cox-text-title4-medium-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-title4-medium {
  @include cox-text-title4-medium;
}

/********************* Title 1 - Bold */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-title1-bold {
  @include getFontStyles(
    var(--cox-text-title1-bold-font-weight),
    var(--cox-text-title1-bold-font-size),
    var(--cox-text-title1-bold-line-height),
    var(--cox-text-title1-bold-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-title1-bold {
  @include cox-text-title1-bold;
}

/********************* Title 2 - Bold */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-title2-bold {
  @include getFontStyles(
    var(--cox-text-title2-bold-font-weight),
    var(--cox-text-title2-bold-font-size),
    var(--cox-text-title2-bold-line-height),
    var(--cox-text-title2-bold-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-title2-bold {
  @include cox-text-title2-bold;
}

/********************* Title 3 - Bold */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-title3-bold {
  @include getFontStyles(
    var(--cox-text-title3-bold-font-weight),
    var(--cox-text-title3-bold-font-size),
    var(--cox-text-title3-bold-line-height),
    var(--cox-text-title3-bold-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-title3-bold {
  @include cox-text-title3-bold;
}

/********************* Title 4 - Bold */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-title4-bold {
  @include getFontStyles(
    var(--cox-text-title4-bold-font-weight),
    var(--cox-text-title4-bold-font-size),
    var(--cox-text-title4-bold-line-height),
    var(--cox-text-title4-bold-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-title4-bold {
  @include cox-text-title4-bold;
}

/********************************** Paragraph */

/********************* Paragraph 1 - Regular */
/* Mixin */
@mixin cox-text-paragraph1-regular {
  @include getFontStyles(
    var(--cox-text-paragraph1-regular-s-xs-font-weight),
    var(--cox-text-paragraph1-regular-s-xs-font-size),
    var(--cox-text-paragraph1-regular-s-xs-line-height),
    var(--cox-text-paragraph1-regular-s-xs-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-paragraph1-regular-xxl-m-font-weight),
      var(--cox-text-paragraph1-regular-xxl-m-font-size),
      var(--cox-text-paragraph1-regular-xxl-m-line-height),
      var(--cox-text-paragraph1-regular-xxl-m-letter-spacing),
      none,
      var(--foreground-on-default-2)
    );
  }
}
/* Class */
.cox-text-paragraph1-regular {
  @include cox-text-paragraph1-regular;
}

/********************* Paragraph 1 - Medium */
/* Mixin */
@mixin cox-text-paragraph1-medium {
  @include getFontStyles(
    var(--cox-text-paragraph1-medium-s-xs-font-weight),
    var(--cox-text-paragraph1-medium-s-xs-font-size),
    var(--cox-text-paragraph1-medium-s-xs-line-height),
    var(--cox-text-paragraph1-medium-s-xs-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-paragraph1-medium-xxl-m-font-weight),
      var(--cox-text-paragraph1-medium-xxl-m-font-size),
      var(--cox-text-paragraph1-medium-xxl-m-line-height),
      var(--cox-text-paragraph1-medium-xxl-m-letter-spacing),
      none,
      var(--foreground-on-default-2)
    );
  }
}
/* Class */
.cox-text-paragraph1-medium {
  @include cox-text-paragraph1-medium;
}

/********************* Paragraph 1 - Bold */
/* Mixin */
@mixin cox-text-paragraph1-bold {
  @include getFontStyles(
    var(--cox-text-paragraph1-bold-s-xs-font-weight),
    var(--cox-text-paragraph1-bold-s-xs-font-size),
    var(--cox-text-paragraph1-bold-s-xs-line-height),
    var(--cox-text-paragraph1-bold-s-xs-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-paragraph1-bold-xxl-m-font-weight),
      var(--cox-text-paragraph1-bold-xxl-m-font-size),
      var(--cox-text-paragraph1-bold-xxl-m-line-height),
      var(--cox-text-paragraph1-bold-xxl-m-letter-spacing),
      none,
      var(--foreground-on-default-2)
    );
  }
}
/* Class */
.cox-text-paragraph1-bold {
  @include cox-text-paragraph1-bold;
}

/********************* Paragraph 2 - Regular */
/* Mixin */
@mixin cox-text-paragraph2-regular {
  @include getFontStyles(
    var(--cox-text-paragraph2-regular-s-xs-font-weight),
    var(--cox-text-paragraph2-regular-s-xs-font-size),
    var(--cox-text-paragraph2-regular-s-xs-line-height),
    var(--cox-text-paragraph2-regular-s-xs-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-paragraph2-regular-xxl-m-font-weight),
      var(--cox-text-paragraph2-regular-xxl-m-font-size),
      var(--cox-text-paragraph2-regular-xxl-m-line-height),
      var(--cox-text-paragraph2-regular-xxl-m-letter-spacing),
      none,
      var(--foreground-on-default-2)
    );
  }
}
/* Class */
.cox-text-paragraph2-regular {
  @include cox-text-paragraph2-regular;
}

/********************* Paragraph 2 - Medium */
/* Mixin */
@mixin cox-text-paragraph2-medium {
  @include getFontStyles(
    var(--cox-text-paragraph2-medium-s-xs-font-weight),
    var(--cox-text-paragraph2-medium-s-xs-font-size),
    var(--cox-text-paragraph2-medium-s-xs-line-height),
    var(--cox-text-paragraph2-medium-s-xs-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-paragraph2-medium-xxl-m-font-weight),
      var(--cox-text-paragraph2-medium-xxl-m-font-size),
      var(--cox-text-paragraph2-medium-xxl-m-line-height),
      var(--cox-text-paragraph2-medium-xxl-m-letter-spacing),
      none,
      var(--foreground-on-default-2)
    );
  }
}
/* Class */
.cox-text-paragraph2-medium {
  @include cox-text-paragraph2-medium;
}

/********************* Paragraph 2 - Bold */
/* Mixin */
@mixin cox-text-paragraph2-bold {
  @include getFontStyles(
    var(--cox-text-paragraph2-bold-s-xs-font-weight),
    var(--cox-text-paragraph2-bold-s-xs-font-size),
    var(--cox-text-paragraph2-bold-s-xs-line-height),
    var(--cox-text-paragraph2-bold-s-xs-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--cox-text-paragraph2-bold-xxl-m-font-weight),
      var(--cox-text-paragraph2-bold-xxl-m-font-size),
      var(--cox-text-paragraph2-bold-xxl-m-line-height),
      var(--cox-text-paragraph2-bold-xxl-m-letter-spacing),
      none,
      var(--foreground-on-default-2)
    );
  }
}
/* Class */
.cox-text-paragraph2-bold {
  @include cox-text-paragraph2-bold;
}

/********************* Paragraph 3 - Regular */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-paragraph3-regular {
  @include getFontStyles(
    var(--cox-text-paragraph3-regular-font-weight),
    var(--cox-text-paragraph3-regular-font-size),
    var(--cox-text-paragraph3-regular-line-height),
    var(--cox-text-paragraph3-regular-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-paragraph3-regular {
  @include cox-text-paragraph3-regular;
}

/********************* Paragraph 3 - Medium */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-paragraph3-medium {
  @include getFontStyles(
    var(--cox-text-paragraph3-medium-font-weight),
    var(--cox-text-paragraph3-medium-font-size),
    var(--cox-text-paragraph3-medium-line-height),
    var(--cox-text-paragraph3-medium-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-paragraph3-medium {
  @include cox-text-paragraph3-medium;
}

/********************* Paragraph 3 - Bold */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-paragraph3-bold {
  @include getFontStyles(
    var(--cox-text-paragraph3-bold-font-weight),
    var(--cox-text-paragraph3-bold-font-size),
    var(--cox-text-paragraph3-bold-line-height),
    var(--cox-text-paragraph3-bold-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-paragraph3-bold {
  @include cox-text-paragraph3-bold;
}

/********************* Paragraph 4 - Regular */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-paragraph4-regular {
  @include getFontStyles(
    var(--cox-text-paragraph4-regular-font-weight),
    var(--cox-text-paragraph4-regular-font-size),
    var(--cox-text-paragraph4-regular-line-height),
    var(--cox-text-paragraph4-regular-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-paragraph4-regular {
  @include cox-text-paragraph4-regular;
}

/********************* Paragraph 4 - Medium */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-paragraph4-medium {
  @include getFontStyles(
    var(--cox-text-paragraph4-medium-font-weight),
    var(--cox-text-paragraph4-medium-font-size),
    var(--cox-text-paragraph4-medium-line-height),
    var(--cox-text-paragraph4-medium-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-paragraph4-medium {
  @include cox-text-paragraph4-medium;
}

/********************* Paragraph 4 - Bold */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-paragraph4-bold {
  @include getFontStyles(
    var(--cox-text-paragraph4-bold-font-weight),
    var(--cox-text-paragraph4-bold-font-size),
    var(--cox-text-paragraph4-bold-line-height),
    var(--cox-text-paragraph4-bold-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-paragraph4-bold {
  @include cox-text-paragraph4-bold;
}

/********************* Paragraph 5 - Regular */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-paragraph5-regular {
  @include getFontStyles(
    var(--cox-text-paragraph5-regular-font-weight),
    var(--cox-text-paragraph5-regular-font-size),
    var(--cox-text-paragraph5-regular-line-height),
    var(--cox-text-paragraph5-regular-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-paragraph5-regular {
  @include cox-text-paragraph5-regular;
}

/********************* Paragraph 5 - Medium */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-paragraph5-medium {
  @include getFontStyles(
    var(--cox-text-paragraph5-medium-font-weight),
    var(--cox-text-paragraph5-medium-font-size),
    var(--cox-text-paragraph5-medium-line-height),
    var(--cox-text-paragraph5-medium-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-paragraph5-medium {
  @include cox-text-paragraph5-medium;
}

/********************* Paragraph 5 - Bold */
/* Properties remain consistent across screen widths */
/* Mixin */
@mixin cox-text-paragraph5-bold {
  @include getFontStyles(
    var(--cox-text-paragraph5-bold-font-weight),
    var(--cox-text-paragraph5-bold-font-size),
    var(--cox-text-paragraph5-bold-line-height),
    var(--cox-text-paragraph5-bold-letter-spacing),
    none,
    var(--foreground-on-default-2)
  );
}
/* Class */
.cox-text-paragraph5-bold {
  @include cox-text-paragraph5-bold;
}

/********************* link-text */
/* Mixin */
@mixin link-text {
  @include getFontStyles(
    var(--link-text-breakpoint-sm-xs-font-weight),
    var(--link-text-breakpoint-sm-xs-font-size),
    var(--link-text-breakpoint-sm-xs-line-height),
    var(--link-text-breakpoint-sm-xs-letter-spacing),
    none,
    var(--link-color-text-default)
  );
  @media (min-width: $md) {
    @include getFontStyles(
      var(--link-text-breakpoint-lg-md-font-weight),
      var(--link-text-breakpoint-lg-md-font-size),
      var(--link-text-breakpoint-lg-md-line-height),
      var(--link-text-breakpoint-lg-md-letter-spacing),
      none,
      var(--link-color-text-default)
    );
  }
  @media (min-width: $xl) {
    @include getFontStyles(
      var(--link-text-breakpoint-xxl-xl-font-weight),
      var(--link-text-breakpoint-xxl-xl-font-size),
      var(--link-text-breakpoint-xxl-xl-line-height),
      var(--link-text-breakpoint-xxl-xl-letter-spacing),
      none,
      var(--link-color-text-default)
    );
  }
}
/* Class */
.link-text {
  @include link-text;
}

// Colors for PSU Accordion secondary title:
.red-500 {
  color: var(--color-red-500);
}

.green-600 {
  color: var(--color-green-600);
}

.neutral-300 {
  color: var(--color-neutral-400);
}
