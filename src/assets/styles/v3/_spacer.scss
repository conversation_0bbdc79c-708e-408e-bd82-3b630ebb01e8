/* Property: Spacing is implemented with gaps, padding, and margins. */
$space-none: 0px;
$space-25: 1px;
$space-50: 2px;
$space-100: 4px;
$space-200: 8px;
$space-300: 12px;
$space-350: 14px;
$space-400: 16px;
$space-450: 18px;
$space-500: 20px;
$space-600: 24px;
$space-700: 28px;
$space-800: 32px;
$space-900: 36px;
$space-1000: 40px;
$space-1100: 44px;
$space-1200: 48px;
$space-1300: 56px;
$space-1400: 64px;
$space-1500: 72px;
$space-1600: 80px;
$space-1700: 88px;
$space-1800: 96px;

// variables from sass
// creates map of spacers according to HUGE figma.
// naming based on pixel
// example: mb-96 = margin bottom 96px aka 6rem according to the figma file
$spacer: 1rem !default;
$spacer-max: 150;

@use 'sass:map';

$spacers: (
  0: 0
);

@for $i from 1 through $spacer-max {
  $spacers: map.merge(
    $spacers,
    (
      $i: (
          calc($i/4) * $spacer
        )
    )
  );
}

$sectionSpacers: (
  $xs: 80px,
  $xl: 96px
);

.space-between-sections {
  @each $size, $spacer in $sectionSpacers {
    @media (min-width: $size) {
      margin-top: calc($spacer/ 2);
      margin-bottom: calc($spacer/ 2);
    }
  }
}

.atom-space-between-sections {
  margin-top: 16px;
  margin-bottom: 16px;
}

.padding-inside-container {
  @each $size, $spacer in $sectionSpacers {
    @media (min-width: $size) {
      padding-top: calc($spacer/ 2);
      padding-bottom: calc($spacer/ 2);
      padding-left: calc($spacer/4);
      padding-right: calc($spacer/4);
    }
  }
}

.container {
  padding-right: calc(var(--bs-gutter-x) * 1) !important;
  padding-left: calc(var(--bs-gutter-x) * 1) !important;
}

.top-spacing {
  @each $size, $spacer in $sectionSpacers {
    @media (min-width: $size) {
      margin-top: calc($spacer/ 2);
    }
  }
}
.bottom-spacing {
  @each $size, $spacer in $sectionSpacers {
    @media (min-width: $size) {
      margin-bottom: calc($spacer/ 2);
    }
  }
}
.left-spacing {
  padding-left: calc(var(--bs-gutter-x) * 1) !important;
}
.right-spacing {
  padding-right: calc(var(--bs-gutter-x) * 1) !important;
}
.atom-top-spacing {
  margin-top: 16px;
}
.atom-bottom-spacing {
  margin-bottom: 16px;
}
