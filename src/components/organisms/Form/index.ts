export enum FormOptionsTypes {
  checkbox = 'CHECKBOX',
  radio = 'RADIO',
  dropdown = 'DROP_DOWN'
  // multidropdown = 'MULTI_DROP_DOWN'
}

export enum FormOptionsLayout {
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical'
}

export type FormOptionsProps = {
  id: string
  title: string
  name: string
  helpMessage?: string
  helpMessageTitle?: string
  helpIcon?: string
  helpIconAlt?: string
  readonly?: boolean
  required?: boolean
  placeholder?: string
  type: FormOptionsTypes
  layout?: FormOptionsLayout
  items: FormItemProps[]
  parameters?: boolean
  isInEditor?: boolean
  radioCheckMark?: string
  checkboxCheckMark?: string
  requiredMessage?: string
  requiredMessageIcon?: string
  requiredMessageIconAlt?: string
  chevronIcon?: string
  chevronIconAlt?: string
  magnifyingGlass?: string
  magnifyingGlassAlt?: string
  checkMark?: string
  checkMarkAlt?: string
  closeIcon?: string
  closeIconAlt?: string
  onChange?: (item?: any) => void
}

export type FormItemProps = {
  text: string
  selected: boolean
  disabled: boolean
  value: string
}

export interface FormDropdownProps extends FormOptionsProps {
  onBlur?: () => void
  onClick?: (item?: any) => void
  searchEnabled?: boolean
}
