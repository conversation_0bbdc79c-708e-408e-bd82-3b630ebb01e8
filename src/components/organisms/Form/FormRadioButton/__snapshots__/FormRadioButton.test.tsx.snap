// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FormRadioButton component matches the snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="form-radio-button false"
        data-testid="form-radio-button-container"
      >
        <label
          class="d-flex align-items-center radio-button-title"
        >
          <div
            class="label-group"
          >
            <span
              class="label-info"
              id="form-radio-buttonform-radio-button-label"
            >
              <span
                class="label-title  false"
                data-testid="form-radio-buttonform-radio-button-label"
              >
                radio
              </span>
            </span>
          </div>
        </label>
        <div
          aria-labelledby="form-radio-buttonform-radio-button"
          class="horizontal form-options-radio-container"
          role="radiogroup"
        >
          <div
            class="false"
          >
            <input
              class="sc-bdVaJa grwaZk"
              data-testid="form-radio-button-radio-input-0"
              id="radiofirst"
              name="radio"
              type="radio"
              value="first"
            />
            <label
              class="form-radio-label"
              data-testid="form-radio-button-radio-label-0"
              for="radiofirst"
              title="radio"
            >
              <div
                aria-checked="false"
                aria-label="first"
                class="custom-radio  sc-bwzfXH feSHhC"
                role="radio"
              />
              <span
                data-testid="form-radio-button-radio-text-0"
              >
                first
              </span>
            </label>
          </div>
          <div
            class="false"
          >
            <input
              class="sc-bdVaJa grwaZk"
              data-testid="form-radio-button-radio-input-1"
              id="radiosecond"
              name="radio"
              type="radio"
              value="second"
            />
            <label
              class="form-radio-label"
              data-testid="form-radio-button-radio-label-1"
              for="radiosecond"
              title="radio"
            >
              <div
                aria-checked="false"
                aria-label="second"
                class="custom-radio  sc-bwzfXH feSHhC"
                role="radio"
              />
              <span
                data-testid="form-radio-button-radio-text-1"
              >
                second
              </span>
            </label>
          </div>
          <div
            class="false"
          >
            <input
              checked=""
              class="sc-bdVaJa grwaZk"
              data-testid="form-radio-button-radio-input-2"
              id="radioselected"
              name="radio"
              type="radio"
              value="selected"
            />
            <label
              class="form-radio-label"
              data-testid="form-radio-button-radio-label-2"
              for="radioselected"
              title="radio"
            >
              <div
                aria-checked="true"
                aria-label="selected"
                class="custom-radio checked sc-bwzfXH kfMEwZ"
                role="radio"
              />
              <span
                data-testid="form-radio-button-radio-text-2"
              >
                selected
              </span>
            </label>
          </div>
          <div
            class="disabled"
          >
            <input
              class="sc-bdVaJa grwaZk"
              data-testid="form-radio-button-radio-input-3"
              disabled=""
              id="radiodisabled"
              name="radio"
              type="radio"
              value="disabled"
            />
            <label
              class="form-radio-label"
              data-testid="form-radio-button-radio-label-3"
              for="radiodisabled"
              title="radio"
            >
              <div
                aria-checked="false"
                aria-label="disabled"
                class="custom-radio  sc-bwzfXH feSHhC"
                role="radio"
              />
              <span
                data-testid="form-radio-button-radio-text-3"
              >
                disabled
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="form-radio-button false"
      data-testid="form-radio-button-container"
    >
      <label
        class="d-flex align-items-center radio-button-title"
      >
        <div
          class="label-group"
        >
          <span
            class="label-info"
            id="form-radio-buttonform-radio-button-label"
          >
            <span
              class="label-title  false"
              data-testid="form-radio-buttonform-radio-button-label"
            >
              radio
            </span>
          </span>
        </div>
      </label>
      <div
        aria-labelledby="form-radio-buttonform-radio-button"
        class="horizontal form-options-radio-container"
        role="radiogroup"
      >
        <div
          class="false"
        >
          <input
            class="sc-bdVaJa grwaZk"
            data-testid="form-radio-button-radio-input-0"
            id="radiofirst"
            name="radio"
            type="radio"
            value="first"
          />
          <label
            class="form-radio-label"
            data-testid="form-radio-button-radio-label-0"
            for="radiofirst"
            title="radio"
          >
            <div
              aria-checked="false"
              aria-label="first"
              class="custom-radio  sc-bwzfXH feSHhC"
              role="radio"
            />
            <span
              data-testid="form-radio-button-radio-text-0"
            >
              first
            </span>
          </label>
        </div>
        <div
          class="false"
        >
          <input
            class="sc-bdVaJa grwaZk"
            data-testid="form-radio-button-radio-input-1"
            id="radiosecond"
            name="radio"
            type="radio"
            value="second"
          />
          <label
            class="form-radio-label"
            data-testid="form-radio-button-radio-label-1"
            for="radiosecond"
            title="radio"
          >
            <div
              aria-checked="false"
              aria-label="second"
              class="custom-radio  sc-bwzfXH feSHhC"
              role="radio"
            />
            <span
              data-testid="form-radio-button-radio-text-1"
            >
              second
            </span>
          </label>
        </div>
        <div
          class="false"
        >
          <input
            checked=""
            class="sc-bdVaJa grwaZk"
            data-testid="form-radio-button-radio-input-2"
            id="radioselected"
            name="radio"
            type="radio"
            value="selected"
          />
          <label
            class="form-radio-label"
            data-testid="form-radio-button-radio-label-2"
            for="radioselected"
            title="radio"
          >
            <div
              aria-checked="true"
              aria-label="selected"
              class="custom-radio checked sc-bwzfXH kfMEwZ"
              role="radio"
            />
            <span
              data-testid="form-radio-button-radio-text-2"
            >
              selected
            </span>
          </label>
        </div>
        <div
          class="disabled"
        >
          <input
            class="sc-bdVaJa grwaZk"
            data-testid="form-radio-button-radio-input-3"
            disabled=""
            id="radiodisabled"
            name="radio"
            type="radio"
            value="disabled"
          />
          <label
            class="form-radio-label"
            data-testid="form-radio-button-radio-label-3"
            for="radiodisabled"
            title="radio"
          >
            <div
              aria-checked="false"
              aria-label="disabled"
              class="custom-radio  sc-bwzfXH feSHhC"
              role="radio"
            />
            <span
              data-testid="form-radio-button-radio-text-3"
            >
              disabled
            </span>
          </label>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;