import React from 'react'

import { render, screen } from '@testing-library/react'

import { FormOptionsProps } from '..'
import props from './_mockData/mockData-radio.json'
import FormRadioButton from './FormRadioButton'
import { defaultState, setFormContext } from '../../../../contexts/_mockData/mockFormContext'
import { FormProvider } from '../../../../contexts/FormContext'

describe('FormRadioButton component', () => {
  test('renders FormRadioButton component', () => {
    render(
      <FormProvider value={{ formContext: defaultState, setFormContext: setFormContext }}>
        <FormRadioButton {...(props as FormOptionsProps)} />
      </FormProvider>
    )
    expect(screen.getByTestId('form-radio-button-container')).toBeTruthy()
  })
  it('matches the snapshot', () => {
    expect(
      render(
        <FormProvider value={{ formContext: defaultState, setFormContext: setFormContext }}>
          <FormRadioButton {...(props as FormOptionsProps)} />
        </FormProvider>
      )
    ).toMatchSnapshot()
  })
})
