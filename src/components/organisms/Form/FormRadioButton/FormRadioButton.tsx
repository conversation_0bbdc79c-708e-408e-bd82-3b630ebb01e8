import React, { useEffect } from 'react'

import { FormItemProps, FormOptionsLayout, FormOptionsProps } from '..'
import { ReactFormHiddenRadioWrapper, ReactFormRadioButtonWrapper } from './FormRadioButton.styled'
import { CHECKMARK_WHITE, DISABLED_QUESTION_CIRCLE, QUESTION_CIRCLE } from '../../../../assets/images'
import { useFormContext } from '../../../../contexts/FormContext'
import { useFormStatus } from '../../../../hooks/useFormStatus'
import { MessageStatus } from '../../../../types'
import { getImageSrc } from '../../../../utils/local-util'
import FormLabel from '../shared/FormLabel'
import FormMessage from '../shared/FormMessage'
import './FormRadioButton.scss'

export type FormRadioButtonProps = FormOptionsProps

const FormRadioButton = (props: FormRadioButtonProps): React.JSX.Element => {
  const { formContext, setFormContext } = useFormContext()
  const {
    id = 'form-radio-button',
    name = '',
    required = false,
    title = '',
    layout = FormOptionsLayout.HORIZONTAL,
    helpMessage = '',
    helpMessageTitle,
    helpIcon = QUESTION_CIRCLE,
    helpIconAlt = '',
    requiredMessage = 'This is required.',
    requiredMessageIcon = DISABLED_QUESTION_CIRCLE,
    requiredMessageIconAlt = '',
    radioCheckMark = CHECKMARK_WHITE,
    onChange,
    readonly = false
  } = props
  const contextItems: any =
    (formContext as any)?.[name]?.items && (formContext as any)?.[name]?.items?.length > 0
      ? (formContext as any)?.[name]?.items
      : null
  const items = contextItems || props?.items
  const value = (formContext as any)?.[name]?.value || ''
  const message = (formContext as any)?.[name]?.message || ''

  const status = useFormStatus({ name })

  useEffect(() => {
    const selectedItem =
      items?.find((item: any) => {
        return value ? item.value === value : item.selected
      }) ?? ({ value: '' } as FormItemProps)
    setFormContext(name, selectedItem?.value, props, false)
  }, [])

  function handleSelectRadioButton(e: React.ChangeEvent<HTMLInputElement>, item: FormItemProps) {
    const newItem = { ...item, selected: e.target.checked }

    setFormContext(name, item.value, props)

    if (onChange) {
      onChange(newItem)
    }
  }

  return (
    <div data-testid={`${id}-container`} className={`form-radio-button ${readonly && 'disabled'}`}>
      <label className={`d-flex align-items-center radio-button-title`}>
        <FormLabel
          title={title}
          required={required}
          requiredMessage={requiredMessage}
          helpMessage={helpMessage}
          helpMessageTitle={helpMessageTitle}
          helpIcon={helpIcon}
          helpIconAlt={helpIconAlt}
          id={`form-radio-button${id}`}
          readOnly={readonly}
        />
      </label>
      <div role='radiogroup' aria-labelledby={`form-radio-button${id}`} className={`${layout} form-options-radio-container`}>
        {items?.map((item: any, index: number) => {
          const isSelected = value ? item.value === value : item.selected
          return (
            <div key={item.value} className={`${(item.disabled || readonly) && 'disabled'}`}>
              <ReactFormHiddenRadioWrapper
                id={title + item.value}
                value={item.value}
                disabled={item.disabled}
                checked={isSelected}
                onChange={(e) => handleSelectRadioButton(e, item)}
                name={name}
                data-testid={`${id}-radio-input-${index}`}
              />
              <label
                title={title}
                htmlFor={title + item.value}
                className='form-radio-label'
                data-testid={`${id}-radio-label-${index}`}
              >
                <ReactFormRadioButtonWrapper
                  className={`custom-${status === MessageStatus.ERROR ? 'error-' : ''}radio ${isSelected ? 'checked' : ''}`}
                  checked={isSelected}
                  checkmarkIcon={getImageSrc(radioCheckMark)}
                  role='radio'
                  aria-label={item.text}
                  aria-checked={isSelected}
                ></ReactFormRadioButtonWrapper>
                <span data-testid={`${id}-radio-text-${index}`}>{item.text}</span>
              </label>
            </div>
          )
        })}
      </div>
      <FormMessage
        id={id}
        status={status}
        message={message}
        requiredMessageIcon={requiredMessageIcon}
        requiredMessageIconAlt={requiredMessageIconAlt}
      />
    </div>
  )
}
export default FormRadioButton
