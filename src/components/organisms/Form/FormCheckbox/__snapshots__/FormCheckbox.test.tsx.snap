// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FormCheckbox component matches the snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="form-options-checkbox false"
        data-testid="form-options-checkbox-container"
      >
        <label
          class="d-flex align-items-center checkbox-title"
        >
          <div
            class="label-group"
          >
            <span
              class="label-info"
              id="form-options-checkbox-label"
            >
              <span
                class="label-title  false"
                data-testid="form-options-checkbox-label"
              >
                reactcheckbox
              </span>
            </span>
            <img
              alt="help-icon"
              class="help-message-icon false"
              data-testid="form-options-checkbox-opens-modal"
              src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
              tabindex="0"
            />
          </div>
        </label>
        <div
          aria-labelledby="form-options-checkbox"
          class="form-options-checkbox-container"
          role="group"
        >
          <div>
            <div
              class="form-check false"
            >
              <input
                checked=""
                class="sc-bdVaJa grwaZk"
                data-testid="form-options-checkbox-checkbox-input-0"
                id="form-options-checkbox-0"
                name="reactcheckbox"
                type="checkbox"
                value="ert"
              />
              <label
                class="form-check-label"
                data-testid="form-options-checkbox-checkbox-label-0"
                for="form-options-checkbox-0"
                title="reactcheckbox"
              >
                <div
                  aria-checked="true"
                  aria-label="ert"
                  class="custom-checkbox checked sc-bwzfXH eLlvVY"
                  role="checkbox"
                />
                <span
                  data-testid="form-options-checkbox-checkbox-text-0"
                >
                  ert
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="form-options-checkbox false"
      data-testid="form-options-checkbox-container"
    >
      <label
        class="d-flex align-items-center checkbox-title"
      >
        <div
          class="label-group"
        >
          <span
            class="label-info"
            id="form-options-checkbox-label"
          >
            <span
              class="label-title  false"
              data-testid="form-options-checkbox-label"
            >
              reactcheckbox
            </span>
          </span>
          <img
            alt="help-icon"
            class="help-message-icon false"
            data-testid="form-options-checkbox-opens-modal"
            src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
            tabindex="0"
          />
        </div>
      </label>
      <div
        aria-labelledby="form-options-checkbox"
        class="form-options-checkbox-container"
        role="group"
      >
        <div>
          <div
            class="form-check false"
          >
            <input
              checked=""
              class="sc-bdVaJa grwaZk"
              data-testid="form-options-checkbox-checkbox-input-0"
              id="form-options-checkbox-0"
              name="reactcheckbox"
              type="checkbox"
              value="ert"
            />
            <label
              class="form-check-label"
              data-testid="form-options-checkbox-checkbox-label-0"
              for="form-options-checkbox-0"
              title="reactcheckbox"
            >
              <div
                aria-checked="true"
                aria-label="ert"
                class="custom-checkbox checked sc-bwzfXH eLlvVY"
                role="checkbox"
              />
              <span
                data-testid="form-options-checkbox-checkbox-text-0"
              >
                ert
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;