import React, { useEffect, useState } from 'react'

import { FormItemProps, FormOptionsProps } from '..'
import { Checkbox, HiddenCheckbox } from './FormCheckbox.styled'
import { CHECKMARK_WHITE } from '../../../../assets/images'
import { useFormContext } from '../../../../contexts/FormContext'
import { useFormStatus } from '../../../../hooks/useFormStatus'
import { MessageStatus } from '../../../../types'
import { getImageSrc } from '../../../../utils/local-util'
import FormLabel from '../shared/FormLabel'
import './FormCheckbox.scss'
import FormMessage from '../shared/FormMessage'

export type FormCheckboxProps = FormOptionsProps

const FormCheckbox = (props: FormCheckboxProps): React.JSX.Element => {
  const { formContext, setFormContext } = useFormContext()
  const {
    id = 'form-options-checkbox',
    name,
    required = false,
    title,
    layout,
    helpMessage,
    helpMessageTitle,
    helpIcon,
    helpIconAlt,
    requiredMessage = 'This is required.',
    requiredMessageIcon,
    requiredMessageIconAlt,
    checkboxCheckMark = CHECKMARK_WHITE,
    onChange,
    readonly = false
  } = props
  const contextItems: any =
    (formContext as any)?.[name]?.items && (formContext as any)?.[name]?.items?.length > 0
      ? (formContext as any)?.[name]?.items
      : null
  const items = contextItems || props?.items
  const value = (formContext as any)?.[name]?.value || ''
  const message = (formContext as any)?.[name]?.message || ''

  const [itemSelected, setItemSelected] = useState<FormItemProps[]>()

  const status = useFormStatus({ name })

  useEffect(() => {
    setItemSelected([...items])
    const selectCheckboxValue =
      items
        ?.filter((item: any) => item.selected || value?.includes(item?.value))
        .map((item: any) => {
          item.selected = true
          return item?.value
        })
        .join(',') ?? ''

    setFormContext(name, selectCheckboxValue, props, false)
  }, [])

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>, item: FormItemProps) => {
    const newItems: any = itemSelected?.map((i) => (i.text === item.text ? { ...i, selected: e.target.checked } : i))
    setItemSelected(newItems)

    const selectedItems = createdSelectedItemArray(newItems)

    setFormContext(name, selectedItems, props)

    if (onChange) {
      onChange(newItems)
    }
  }

  function createdSelectedItemArray(items: FormItemProps[]) {
    const filteredItems = items
      .filter((item) => {
        if (item.selected) {
          return item
        }
      })
      .map((item) => {
        return item?.value
      })
    return filteredItems && filteredItems.length > 0 ? filteredItems.join(',') : ''
  }

  return (
    <div className={`form-options-checkbox ${readonly && 'disabled'}`} data-testid={`${id}-container`}>
      <label className={`d-flex align-items-center checkbox-title`}>
        <FormLabel
          id={id}
          title={title}
          required={required}
          requiredMessage={requiredMessage}
          helpMessage={helpMessage}
          helpMessageTitle={helpMessageTitle}
          helpIcon={helpIcon}
          helpIconAlt={helpIconAlt}
          readOnly={readonly}
        />
      </label>
      <div role='group' aria-labelledby={id} className='form-options-checkbox-container'>
        <div className={layout}>
          {itemSelected && itemSelected?.length > 0
            ? itemSelected.map((item: FormItemProps, index: number) => {
                return (
                  <div key={item.value} className={`form-check ${(item.disabled || readonly) && 'disabled'}`}>
                    <HiddenCheckbox
                      id={`${id}-${index}`}
                      value={item.value}
                      checked={item.selected}
                      onChange={(e) => handleCheckboxChange(e, item)}
                      name={name}
                      data-testid={`${id}-checkbox-input-${index}`}
                    />
                    <label
                      title={title}
                      htmlFor={`${id}-${index}`}
                      className='form-check-label'
                      data-testid={`${id}-checkbox-label-${index}`}
                    >
                      <Checkbox
                        checked={item.selected}
                        className={`custom-${status === MessageStatus.ERROR ? 'error-' : ''}checkbox ${
                          item.selected ? 'checked' : ''
                        }`}
                        checkmarkIcon={getImageSrc(checkboxCheckMark)}
                        role='checkbox'
                        aria-label={item.text}
                        aria-checked={item.selected}
                      />
                      <span data-testid={`${id}-checkbox-text-${index}`}>{item.text}</span>
                    </label>
                  </div>
                )
              })
            : null}
        </div>
      </div>
      <FormMessage
        id={id}
        status={status}
        message={message}
        requiredMessageIcon={requiredMessageIcon}
        requiredMessageIconAlt={requiredMessageIconAlt}
      />
    </div>
  )
}
export default FormCheckbox
