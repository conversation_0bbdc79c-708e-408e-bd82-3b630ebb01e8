@import './../../../../assets/styles/v3/globalStyles.scss';

.form-field-and-numbers {
  .input {
    border: var(--utility-on-default-2) solid 2px;
    border-radius: 15px;
    padding: 1.25rem;
    display: flex;
    background-color: white;

    &:has(textarea) {
      padding-right: 0.25rem;
      padding-bottom: 0.25rem;
    }

    &:focus {
      outline: none;
      box-shadow: none;
    }

    input::placeholder {
      line-height: 24px;
      color: var(--foreground-on-default-4);
    }

    /* Override default edge password icons*/
    input[type='password']::-ms-reveal,
    input[type='password']::-ms-clear {
      display: none;
    }

    input:autofill,
    input:autofill:hover,
    input:autofill:focus,
    input:autofill:active {
      background-color: var(--background-default) !important;
      box-shadow: 0 0 0 50px var(--background-default) inset;
      transition: var(--background-default) 5000s ease-in-out 0s !important;
    }

    .input-link {
      @include cox-text-interactive3;
      color: var(--background-strong);
      text-decoration: none;
      padding: 2px;
      align-self: center;

      &:focus {
        border: 2px solid var(--utility-on-default-2);
        border-radius: 4px;
      }
    }
  }

  .flex-2 {
    flex: 2;
  }

  .clean-input {
    border: none;

    &:focus {
      box-shadow: none;
    }
  }

  .label {
    @include cox-resi-text-label-2-small;
    font-weight: 500;
    line-height: 22px;
    letter-spacing: -0.02em;
    display: flex;
  }

  .label.disabled {
    color: var(--color-neutral-300) !important;
  }

  .disabled {
    cursor: not-allowed;

    input {
      cursor: not-allowed;
    }

    input:disabled {
      background-color: var(--background-muted-1);
      color: var(--foreground-on-default-disabled);
    }

    input::placeholder {
      color: var(--foreground-on-default-disabled);
    }

    .input-link {
      color: var(--color-neutral-300);
      cursor: not-allowed;
      pointer-events: none;
    }
  }

  .form-control {
    padding: 0;
    line-height: 24px;
    color: var(--foreground-on-default-2);
  }

  .disabled.input {
    background-color: var(--background-muted-1);
  }

  .state-indicator {
    border-radius: 15px;

    &:focus-within {
      //outline: 2px solid $formfield-outline;

      > :has(:not(.error)) {
        outline: 2px solid $formfield-outline;
      }
    }

    .focus {
      border: 2px solid $formfield-outline;
    }

    .success {
      border: 2px solid var(--success-default);
    }

    .error {
      border: 2px solid var(--error-default);

      &:focus-within {
        outline: 1px solid var(--error-default);
      }
    }
  }

  .input-icon {
    width: 24px;
    height: 24px;
  }

  .required-label {
    @include cox-text-paragraph5-regular;
    color: var(--foreground-on-default-3);
  }

  .status-indicator-container {
    display: flex;
    align-items: center;
  }

  .status-indicator-text {
    @include cox-text-paragraph4-regular;
  }

  @media (min-width: $xs) {
    .responsive-size {
      width: 100%;
    }
  }
}
