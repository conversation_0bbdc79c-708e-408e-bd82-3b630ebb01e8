import React, { forwardRef, useEffect, useState } from 'react'

import InputWrapper from './InputWrapper'
import { FormInputProps, FormInputTypes, InputModes } from './types'
import './FormInput.scss'
import { MESSAGE_ERROR } from '../../../../assets/images'
import { useFormContext } from '../../../../contexts/FormContext'
import { useFormStatus } from '../../../../hooks/useFormStatus'
import { MessageStatus } from '../../../../types'
import { handleValidation, isPatternValid, VALIDATION_LIST } from '../../../../utils/form-validation-util'
import { getFormattedPhone, getFormattedZipcode } from '../../../../utils/helper-util'
import AutocompleteInput from '../../../atoms/AutocompleteInput'
import { RBForm, RBInputGroup } from '../../../shared/ReactBootstrap'
import FormChecklist, { FormValidation } from '../shared/FormChecklist'
import FormLabel from '../shared/FormLabel'
import FormMessage from '../shared/FormMessage'

const FormInput = forwardRef<HTMLInputElement, FormInputProps>((props, ref: React.Ref<HTMLInputElement>) => {
  const [listOfValidations, setListOfValidations] = useState<FormValidation[]>()
  const { formContext, setFormContext } = useFormContext()
  const {
    id = 'form-input',
    name = '',
    title = '',
    placeholder,
    placeholderText = '',
    readOnly,
    rows = 2,
    googleAutocomplete = false,
    isPII = false,
    requiredMessage = 'This is required.',
    requiredMessageIcon = MESSAGE_ERROR,
    requiredMessageIconAlt = '',
    skipValidationOnChange = false,
    allowNumberOnly = false,
    onBlur,
    onFocus,
    onChange,
    onAddressSelected,
    onIconClick,
    onKeyDown,
    onKeyUp
  } = props

  const {
    minLength,
    maxLength,
    min,
    max,
    validations = [],
    required = false,
    showChecklist = false,
    showChecklistAlways = false,
    message = '',
    type,
    value: inputValue = '',
    pattern = ''
  } = (formContext as any)?.[name] || props
  const [showPassword, setShowPassword] = useState(false)
  const status = useFormStatus({ name })

  useEffect(() => {
    setFormContext(name, inputValue, props, false)
  }, [])

  const handleOnBlur = (e: any) => {
    if (setFormContext) {
      setFormContext(name, inputValue, props)
      if (onBlur) {
        onBlur(e)
      }
    }
  }

  const handleKeyUp = (e: any) => {
    if (onKeyUp) {
      onKeyUp(e)
    }
  }

  const handleKeyDown = (e: any) => {
    if (onKeyDown) {
      onKeyDown(e)
    }
    return type === FormInputTypes.NUMBER ? blockInvalidNumberChar : () => true
  }

  const blockInvalidNumberChar = (e: React.KeyboardEvent<HTMLInputElement>) => {
    //see CCCOREUI-3305 - default behaviour of type number in browsers allow 'E,-,.,+'
    return ['e', 'E', '+', '-', '.'].includes(e.key) && e.preventDefault()
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    let input = e.target.value
    if (type === FormInputTypes.ZIPCODE) {
      input = getFormattedZipcode(input)
    } else if (type === FormInputTypes.NUMBER) {
      //validation happens in form-validaiton-util#validateInput
      //check if number is valid here before validation kicks in.
      if (!isPatternValid(/(\d)*/, input)) {
        return
      }
      if (maxLength && input.length >= parseInt(maxLength)) {
        return
      }
    } else if (type === FormInputTypes.PHONE) {
      input = getFormattedPhone(input, inputValue as string)
    } else if (type === FormInputTypes.PASSWORD) {
      if (allowNumberOnly && !isPatternValid(/^\d+$/, input)) {
        return
      }
    } else if (type === FormInputTypes.TEXT) {
      if (allowNumberOnly && !isPatternValid(/^\d+$/, input)) {
        return
      }
      if (input && !pattern && VALIDATION_LIST.text.pattern(input, props).status === MessageStatus.ERROR) {
        //@see form-validation-utils.ts#VALIDATION_LIST about the second parameter.
        return
      }
    }
    setFormContext(name, input, props, true, skipValidationOnChange) //handle the required message.
    setListOfValidations([])
    const { validationResponse } = handleValidation(validations, input)
    if (showChecklist) setListOfValidations(validationResponse)

    if (onChange) {
      onChange(e)
    }
  }

  const getUpdatedChildValue = (value: string) => {
    setFormContext(name, value, props)
  }

  const inputProps: any = {
    id: id,
    className: `clean-input flex-2  ${isPII && 'isPII'}`,
    placeholder: placeholderText || placeholder,
    name: name,
    type: showPassword ? FormInputTypes.TEXT : type,
    disabled: type == FormInputTypes.HIDDEN ? false : readOnly,
    value: inputValue,
    minLength: minLength,
    maxLength: maxLength,
    onChange: handleInputChange,
    onBlur: handleOnBlur,
    onFocus: onFocus,
    onKeyUp: handleKeyUp,
    onKeyDown: handleKeyDown
  }
  const accessibilityProps: any = {
    dataTestid: `${id}-input-control`,
    ariaLabel: title + (required ? ' (Required)' : '')
  }

  if (type === FormInputTypes.NUMBER || type === FormInputTypes.ZIPCODE) {
    inputProps.inputMode = InputModes.NUMERIC
  }

  if (type === FormInputTypes.TEXTAREA) {
    inputProps.rows = rows
    inputProps.as = 'textarea'
  }

  if (type === FormInputTypes.NUMBER) {
    inputProps.min = min
    inputProps.max = max
  }

  const handleIconClick = () => {
    if (onIconClick) {
      onIconClick()
    }
    if (type === FormInputTypes.PASSWORD) {
      setShowPassword(!showPassword)
    }
  }

  return (
    <div data-testid={`${id}-container`} className={`form-field-and-numbers`}>
      <RBForm.Label htmlFor={id} className={`${status} label ${readOnly && 'disabled'}`}>
        <FormLabel {...props}></FormLabel>
      </RBForm.Label>
      <RBInputGroup className={`mb-2 state-indicator responsive-size`}>
        <div className={`input responsive-size ${status} ${readOnly && 'disabled'}`}>
          <InputWrapper
            {...props}
            status={status}
            onIconClick={handleIconClick}
            showPassword={showPassword}
            isPassword={type === FormInputTypes.PASSWORD}
          >
            {googleAutocomplete ? (
              <AutocompleteInput
                {...inputProps}
                {...accessibilityProps}
                onAddressSelected={onAddressSelected}
                onInputChange={getUpdatedChildValue}
              />
            ) : (
              <RBForm.Control
                {...inputProps}
                ref={ref}
                data-testid={accessibilityProps.dataTestid}
                aria-label={accessibilityProps.ariaLabel}
              />
            )}
          </InputWrapper>
        </div>
      </RBInputGroup>
      <FormMessage
        {...props}
        status={status}
        message={message}
        requiredMessage={requiredMessage}
        requiredMessageIcon={requiredMessageIcon}
        requiredMessageIconAlt={requiredMessageIconAlt}
      />
      <FormChecklist id={id} checklist={listOfValidations} name={name} showChecklistAlways={showChecklistAlways} />
    </div>
  )
})

export default FormInput
