import { ReactNode } from 'react'

import {
  CIRCLE_CHECK_LIME_GREEN_CIRCLE,
  DISABLED_QUESTION_CIRCLE,
  PASSWORD_EYE,
  PASSWORD_EYE_STRIKETHROUGH,
  QUESTION_CIRCLE
} from '../../../../../assets/images'
import { MessageStatus } from '../../../../../types'
import { getImageSrc } from '../../../../../utils/local-util'
import { FormInputProps, FormInputTypes } from '../types'

interface InputWrapper extends Partial<FormInputProps> {
  children?: ReactNode
  status?: MessageStatus
  showPassword?: boolean
  isPassword?: boolean
}

// eslint-disable-next-line @typescript-eslint/no-redeclare
const InputWrapper = (props: InputWrapper) => {
  const {
    icon,
    iconPosition,
    link = '',
    linkText,
    onIconClick,
    type,
    children,
    passwordShowIcon = PASSWORD_EYE_STRIKETHROUGH,
    passwordHideIcon = PASSWORD_EYE,
    inlineErrorIcon = DISABLED_QUESTION_CIRCLE,
    inlineWarningIcon = QUESTION_CIRCLE,
    inlineSuccessIcon = CIRCLE_CHECK_LIME_GREEN_CIRCLE,
    showInlineIcon = false,
    status,
    showPassword,
    isPassword
  } = props

  const getIcon = () => {
    if (isPassword) {
      return showPassword ? getImageSrc(passwordShowIcon) : getImageSrc(passwordHideIcon)
    } else if (showInlineIcon) {
      if (status === MessageStatus.SUCCESS) {
        return getImageSrc(inlineSuccessIcon)
      } else if (status === MessageStatus.ERROR) {
        return getImageSrc(inlineErrorIcon)
      } else if (status === MessageStatus.WARNING) {
        return getImageSrc(inlineWarningIcon)
      }
    }
    return icon
  }

  const redirect = link
  const inlineIcon = getIcon()
  const isPasswordField = (inlineIcon && type === FormInputTypes.PASSWORD) || (inlineIcon && type === FormInputTypes.TEXT)

  if (inlineIcon && link) {
    return (
      <>
        <img src={inlineIcon} className='input-icon me-2' alt='input-icon' />
        {children}
        <a className='input-link' href={redirect}>
          {linkText}
        </a>
      </>
    )
  } else if (!inlineIcon && link) {
    return (
      <>
        {children}
        <a className='input-link' href={redirect}>
          {linkText}
        </a>
      </>
    )
  } else if ((inlineIcon && iconPosition === 'right') || isPasswordField) {
    return (
      <>
        {children}
        <img src={inlineIcon} className='input-icon' alt='input-icon' onClick={onIconClick} />
      </>
    )
  } else if (inlineIcon && iconPosition === 'left') {
    return (
      <>
        <img src={inlineIcon} className='input-icon me-2' alt='input-icon' onClick={onIconClick} />
        {children}
      </>
    )
  }

  return <>{children}</>
}
export default InputWrapper
