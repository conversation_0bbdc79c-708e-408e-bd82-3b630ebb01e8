import { ChangeEvent, FocusEvent } from 'react'

import { AutocompleteInputProps } from '../../../atoms/AutocompleteInput/types'
import { FormValidation } from '../shared/FormChecklist'
export interface FormInputProps extends AutocompleteInputProps {
  /**Used as a unique id for the input */
  id: string
  /**Specifies the label of the input */
  title: string
  /**Used for displaying inline icon for the input */
  icon?: string
  /**Specifies the Icon position (left/right) of the form input */
  iconPosition?: string
  /**Specifies the link of the form input */
  link?: string
  /**Specifies the link Id of the form input */
  linkText?: string
  /**Specifies the name of the form input */
  name?: string
  /**Specifies the value of the form input */
  value?: string
  /**Specifies the help message display inside modal */
  helpMessage?: string
  /**Specifies the help message title display as modal title*/
  helpMessageTitle?: string
  /**placeholder is displayed in the input field before the user enters a value.*/
  placeholder?: string
  /**Specifies the placeholder text of the form input */
  placeholderText?: string
  /**Specifies the validation to be triggered on change of the form input */
  skipValidationOnChange?: boolean
  /**Make the control readonly */
  readOnly?: boolean
  /** specifies that the form input contains PII data */
  isPII?: boolean
  /**specifies that an input field must be filled out before submitting the form */
  required?: boolean
  /**Specifies the required message of the form input */
  requiredMessage?: string
  /**Specifies the required message icon of the form input */
  requiredMessageIcon?: string
  /**Specifies the required message icon of the form input */
  requiredMessageIconAlt?: string
  /**Specifies the success message of the form input */
  requiredSuccessMessage?: string
  /**Specifies the type of the form input */
  type: FormInputTypes
  /**Specifies the help icon to display in label of the form input */
  helpIcon?: string
  /**Specifies the help icon alt text to display in label of the form input */
  helpIconAlt?: string
  /**Specifies the password show icon to display in the form input */
  passwordShowIcon?: string
  /**Specifies the password hide icon to display in the form input */
  passwordHideIcon?: string
  /**Specifies the inline error icon to display under the form input */
  inlineErrorIcon?: string
  /**Specifies the inline warning icon to display under the form input */
  inlineWarningIcon?: string
  /**Specifies the inline success icon to display under the form input */
  inlineSuccessIcon?: string
  /**Specifies the icon to be displayed inside the form input */
  showInlineIcon?: boolean
  /** specifies the visible height of a text area, in lines.*/
  rows?: number
  /**Specifies the allow speacial characters of the form input */
  splCharacters?: boolean
  /**Specifies the allow numbers Id of the form input */
  allowNumbers?: boolean
  /**Specifies the allow number only in the form input - type password*/
  allowNumberOnly?: boolean
  /**Specifies the min value of the form input - number */
  min?: string
  /**Specifies the max value of the form input - number */
  max?: string
  /**Renders google auto complete input*/
  googleAutocomplete?: boolean
  /** validations holds the checklist data. Each item has an pattern and message property */
  validations?: FormValidation[]
  /** Renders list of conditions/validations required under the input */
  showChecklist?: boolean
  /** Renders list of conditions/validations required under the input even after all the condtions are met */
  showChecklistAlways?: boolean
  /**Accepts the regex condition and perform validations */
  pattern?: RegExp
  /**Displays an error message if value is not matched with the regex pattern */
  patternErrorMessage?: string
  /**Displays a scucess message if value is matched with regex pattern */
  patternSuccessMessage?: string
  /**A callback fired to skip the default validations and perform custom validations */
  validator?: any
  /**A  callback fired when input has lost focus */
  onBlur?: (e?: FocusEvent<HTMLInputElement>) => void | Promise<void> | boolean
  /**A callback fired when input received focus*/
  onFocus?: (e: FocusEvent<HTMLInputElement>) => void | Promise<void> | boolean
  /**A callback fired when the value prop changes*/
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void | Promise<void> | boolean
  /**A callback fired when the key up changes*/
  onKeyUp?: (e: ChangeEvent<HTMLInputElement>) => void | Promise<void> | boolean
  /**A callback fired when the key down changes*/
  onKeyDown?: (e: ChangeEvent<HTMLInputElement>) => void | Promise<void> | boolean
  /** A callback fired when inline icon clicked */
  onIconClick?: () => void
}

export enum FormInputTypes {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  EMAIL = 'email',
  PHONE = 'tel',
  NUMBER = 'number',
  PASSWORD = 'password',
  ZIPCODE = 'zipcode',
  HIDDEN = 'hidden'
}

export enum InputModes {
  TEXT = 'text',
  NUMERIC = 'numeric'
}
