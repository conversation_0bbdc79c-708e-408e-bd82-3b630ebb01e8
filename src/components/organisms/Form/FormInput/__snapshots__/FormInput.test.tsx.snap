// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ReactFormInput component renders a disabled input field 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="form-field-and-numbers"
        data-testid="form-text-1092343631-container"
      >
        <label
          class="pending label disabled form-label"
          for="form-text-1092343631"
        >
          <div
            class="label-group"
          >
            <span
              class="label-info"
              id="form-text-1092343631-label"
            >
              <span
                class="label-title  disabled"
                data-testid="form-text-1092343631-label"
              >
                Disabled
              </span>
              <span
                class="required-label disabled"
              >
                (Required)
              </span>
            </span>
            <img
              alt=""
              class="help-message-icon true"
              data-testid="form-text-1092343631-opens-modal"
              src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
              tabindex="0"
            />
          </div>
        </label>
        <div
          class="mb-2 state-indicator responsive-size input-group"
        >
          <div
            class="input responsive-size pending disabled"
          >
            <input
              aria-label="Disabled"
              class="clean-input flex-2  false form-control"
              data-testid="form-text-1092343631-input-control"
              disabled=""
              id="form-text-1092343631"
              name="sampletxt"
              placeholder="Test"
              value="value"
            />
            <img
              alt="input-icon"
              class="input-icon"
              src="example-icon"
            />
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="form-field-and-numbers"
      data-testid="form-text-1092343631-container"
    >
      <label
        class="pending label disabled form-label"
        for="form-text-1092343631"
      >
        <div
          class="label-group"
        >
          <span
            class="label-info"
            id="form-text-1092343631-label"
          >
            <span
              class="label-title  disabled"
              data-testid="form-text-1092343631-label"
            >
              Disabled
            </span>
            <span
              class="required-label disabled"
            >
              (Required)
            </span>
          </span>
          <img
            alt=""
            class="help-message-icon true"
            data-testid="form-text-1092343631-opens-modal"
            src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
            tabindex="0"
          />
        </div>
      </label>
      <div
        class="mb-2 state-indicator responsive-size input-group"
      >
        <div
          class="input responsive-size pending disabled"
        >
          <input
            aria-label="Disabled"
            class="clean-input flex-2  false form-control"
            data-testid="form-text-1092343631-input-control"
            disabled=""
            id="form-text-1092343631"
            name="sampletxt"
            placeholder="Test"
            value="value"
          />
          <img
            alt="input-icon"
            class="input-icon"
            src="example-icon"
          />
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`ReactFormInput component renders a required field 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="form-field-and-numbers"
        data-testid="form-text-1092343631-container"
      >
        <label
          class="pending label false form-label"
          for="form-text-1092343631"
        >
          <div
            class="label-group"
          >
            <span
              class="label-info"
              id="form-text-1092343631-label"
            >
              <span
                class="label-title  false"
                data-testid="form-text-1092343631-label"
              >
                Sample Text
              </span>
              <span
                class="required-label false"
              >
                (Required)
              </span>
            </span>
            <img
              alt=""
              class="help-message-icon false"
              data-testid="form-text-1092343631-opens-modal"
              src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
              tabindex="0"
            />
          </div>
        </label>
        <div
          class="mb-2 state-indicator responsive-size input-group"
        >
          <div
            class="input responsive-size pending false"
          >
            <input
              aria-label="Sample Text"
              class="clean-input flex-2  false form-control"
              data-testid="form-text-1092343631-input-control"
              id="form-text-1092343631"
              name="sampletxt"
              placeholder="Test"
              value=""
            />
            <a
              class="input-link"
              href="/content/cox/en/us/indix.html"
            >
              Example
            </a>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="form-field-and-numbers"
      data-testid="form-text-1092343631-container"
    >
      <label
        class="pending label false form-label"
        for="form-text-1092343631"
      >
        <div
          class="label-group"
        >
          <span
            class="label-info"
            id="form-text-1092343631-label"
          >
            <span
              class="label-title  false"
              data-testid="form-text-1092343631-label"
            >
              Sample Text
            </span>
            <span
              class="required-label false"
            >
              (Required)
            </span>
          </span>
          <img
            alt=""
            class="help-message-icon false"
            data-testid="form-text-1092343631-opens-modal"
            src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
            tabindex="0"
          />
        </div>
      </label>
      <div
        class="mb-2 state-indicator responsive-size input-group"
      >
        <div
          class="input responsive-size pending false"
        >
          <input
            aria-label="Sample Text"
            class="clean-input flex-2  false form-control"
            data-testid="form-text-1092343631-input-control"
            id="form-text-1092343631"
            name="sampletxt"
            placeholder="Test"
            value=""
          />
          <a
            class="input-link"
            href="/content/cox/en/us/indix.html"
          >
            Example
          </a>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`ReactFormInput component renders an icon to the left of the input field 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="form-field-and-numbers"
        data-testid="form-input-container"
      >
        <label
          class="pending label false form-label"
          for="form-input"
        >
          <div
            class="label-group"
          >
            <span
              class="label-info"
              id="form-input-label"
            >
              <span
                class="label-title  false"
                data-testid="form-input-label"
              >
                Sample Text
              </span>
              <span
                class="required-label false"
              >
                (Required)
              </span>
            </span>
            <img
              alt=""
              class="help-message-icon false"
              data-testid="form-input-opens-modal"
              src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
              tabindex="0"
            />
          </div>
        </label>
        <div
          class="mb-2 state-indicator responsive-size input-group"
        >
          <div
            class="input responsive-size pending false"
          >
            <input
              aria-label="Sample Text"
              class="clean-input flex-2  false form-control"
              data-testid="form-input-input-control"
              id="form-input"
              name="sampletxt"
              placeholder="Test"
              value="value"
            />
            <img
              alt="input-icon"
              class="input-icon"
              src="example-icon"
            />
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="form-field-and-numbers"
      data-testid="form-input-container"
    >
      <label
        class="pending label false form-label"
        for="form-input"
      >
        <div
          class="label-group"
        >
          <span
            class="label-info"
            id="form-input-label"
          >
            <span
              class="label-title  false"
              data-testid="form-input-label"
            >
              Sample Text
            </span>
            <span
              class="required-label false"
            >
              (Required)
            </span>
          </span>
          <img
            alt=""
            class="help-message-icon false"
            data-testid="form-input-opens-modal"
            src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
            tabindex="0"
          />
        </div>
      </label>
      <div
        class="mb-2 state-indicator responsive-size input-group"
      >
        <div
          class="input responsive-size pending false"
        >
          <input
            aria-label="Sample Text"
            class="clean-input flex-2  false form-control"
            data-testid="form-input-input-control"
            id="form-input"
            name="sampletxt"
            placeholder="Test"
            value="value"
          />
          <img
            alt="input-icon"
            class="input-icon"
            src="example-icon"
          />
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`ReactFormInput component renders an icon to the left of the input field and a link to the right 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="form-field-and-numbers"
        data-testid="form-text-1092343631-container"
      >
        <label
          class="pending label false form-label"
          for="form-text-1092343631"
        >
          <div
            class="label-group"
          >
            <span
              class="label-info"
              id="form-text-1092343631-label"
            >
              <span
                class="label-title  false"
                data-testid="form-text-1092343631-label"
              >
                Sample Text
              </span>
              <span
                class="required-label false"
              >
                (Required)
              </span>
            </span>
            <img
              alt=""
              class="help-message-icon false"
              data-testid="form-text-1092343631-opens-modal"
              src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
              tabindex="0"
            />
          </div>
        </label>
        <div
          class="mb-2 state-indicator responsive-size input-group"
        >
          <div
            class="input responsive-size pending false"
          >
            <img
              alt="input-icon"
              class="input-icon me-2"
              src="example-icon"
            />
            <input
              aria-label="Sample Text"
              class="clean-input flex-2  false form-control"
              data-testid="form-text-1092343631-input-control"
              id="form-text-1092343631"
              name="sampletxt"
              placeholder="Test"
              value="value"
            />
            <a
              class="input-link"
              href="/content/cox/en/us/indix.html"
            >
              Test
            </a>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="form-field-and-numbers"
      data-testid="form-text-1092343631-container"
    >
      <label
        class="pending label false form-label"
        for="form-text-1092343631"
      >
        <div
          class="label-group"
        >
          <span
            class="label-info"
            id="form-text-1092343631-label"
          >
            <span
              class="label-title  false"
              data-testid="form-text-1092343631-label"
            >
              Sample Text
            </span>
            <span
              class="required-label false"
            >
              (Required)
            </span>
          </span>
          <img
            alt=""
            class="help-message-icon false"
            data-testid="form-text-1092343631-opens-modal"
            src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
            tabindex="0"
          />
        </div>
      </label>
      <div
        class="mb-2 state-indicator responsive-size input-group"
      >
        <div
          class="input responsive-size pending false"
        >
          <img
            alt="input-icon"
            class="input-icon me-2"
            src="example-icon"
          />
          <input
            aria-label="Sample Text"
            class="clean-input flex-2  false form-control"
            data-testid="form-text-1092343631-input-control"
            id="form-text-1092343631"
            name="sampletxt"
            placeholder="Test"
            value="value"
          />
          <a
            class="input-link"
            href="/content/cox/en/us/indix.html"
          >
            Test
          </a>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`ReactFormInput component renders an icon to the right of the input field 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="form-field-and-numbers"
        data-testid="form-text-1092343631-container"
      >
        <label
          class="pending label false form-label"
          for="form-text-1092343631"
        >
          <div
            class="label-group"
          >
            <span
              class="label-info"
              id="form-text-1092343631-label"
            >
              <span
                class="label-title  false"
                data-testid="form-text-1092343631-label"
              >
                Sample Text
              </span>
              <span
                class="required-label false"
              >
                (Required)
              </span>
            </span>
            <img
              alt=""
              class="help-message-icon false"
              data-testid="form-text-1092343631-opens-modal"
              src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
              tabindex="0"
            />
          </div>
        </label>
        <div
          class="mb-2 state-indicator responsive-size input-group"
        >
          <div
            class="input responsive-size pending false"
          >
            <input
              aria-label="Sample Text"
              class="clean-input flex-2  false form-control"
              data-testid="form-text-1092343631-input-control"
              id="form-text-1092343631"
              name="sampletxt"
              placeholder="Test"
              value="value"
            />
            <img
              alt="input-icon"
              class="input-icon"
              src="example-icon"
            />
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="form-field-and-numbers"
      data-testid="form-text-1092343631-container"
    >
      <label
        class="pending label false form-label"
        for="form-text-1092343631"
      >
        <div
          class="label-group"
        >
          <span
            class="label-info"
            id="form-text-1092343631-label"
          >
            <span
              class="label-title  false"
              data-testid="form-text-1092343631-label"
            >
              Sample Text
            </span>
            <span
              class="required-label false"
            >
              (Required)
            </span>
          </span>
          <img
            alt=""
            class="help-message-icon false"
            data-testid="form-text-1092343631-opens-modal"
            src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
            tabindex="0"
          />
        </div>
      </label>
      <div
        class="mb-2 state-indicator responsive-size input-group"
      >
        <div
          class="input responsive-size pending false"
        >
          <input
            aria-label="Sample Text"
            class="clean-input flex-2  false form-control"
            data-testid="form-text-1092343631-input-control"
            id="form-text-1092343631"
            name="sampletxt"
            placeholder="Test"
            value="value"
          />
          <img
            alt="input-icon"
            class="input-icon"
            src="example-icon"
          />
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`ReactFormInput component renders an link to the right of the input field 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="form-field-and-numbers"
        data-testid="form-text-1092343631-container"
      >
        <label
          class="pending label false form-label"
          for="form-text-1092343631"
        >
          <div
            class="label-group"
          >
            <span
              class="label-info"
              id="form-text-1092343631-label"
            >
              <span
                class="label-title  false"
                data-testid="form-text-1092343631-label"
              >
                Sample Text
              </span>
              <span
                class="required-label false"
              >
                (Required)
              </span>
            </span>
            <img
              alt=""
              class="help-message-icon false"
              data-testid="form-text-1092343631-opens-modal"
              src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
              tabindex="0"
            />
          </div>
        </label>
        <div
          class="mb-2 state-indicator responsive-size input-group"
        >
          <div
            class="input responsive-size pending false"
          >
            <input
              aria-label="Sample Text"
              class="clean-input flex-2  false form-control"
              data-testid="form-text-1092343631-input-control"
              id="form-text-1092343631"
              name="sampletxt"
              placeholder="Test"
              value="value"
            />
            <a
              class="input-link"
              href="/content/cox/en/us/indix.html"
            >
              Example
            </a>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="form-field-and-numbers"
      data-testid="form-text-1092343631-container"
    >
      <label
        class="pending label false form-label"
        for="form-text-1092343631"
      >
        <div
          class="label-group"
        >
          <span
            class="label-info"
            id="form-text-1092343631-label"
          >
            <span
              class="label-title  false"
              data-testid="form-text-1092343631-label"
            >
              Sample Text
            </span>
            <span
              class="required-label false"
            >
              (Required)
            </span>
          </span>
          <img
            alt=""
            class="help-message-icon false"
            data-testid="form-text-1092343631-opens-modal"
            src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
            tabindex="0"
          />
        </div>
      </label>
      <div
        class="mb-2 state-indicator responsive-size input-group"
      >
        <div
          class="input responsive-size pending false"
        >
          <input
            aria-label="Sample Text"
            class="clean-input flex-2  false form-control"
            data-testid="form-text-1092343631-input-control"
            id="form-text-1092343631"
            name="sampletxt"
            placeholder="Test"
            value="value"
          />
          <a
            class="input-link"
            href="/content/cox/en/us/indix.html"
          >
            Example
          </a>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
