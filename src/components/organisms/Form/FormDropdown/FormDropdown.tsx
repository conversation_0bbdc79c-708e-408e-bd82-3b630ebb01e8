import React, { useEffect, useRef, useState } from 'react'

import { FormDropdownProps, FormItemProps } from '..'
import { CHECK_MARK, CHEVRON_DOWN_RIVER_BLUE, CLOSE_RIVER_BLUE } from '../../../../assets/images'
import { useFormContext } from '../../../../contexts/FormContext'
import { useFormStatus } from '../../../../hooks/useFormStatus'
import { getImageSrc } from '../../../../utils/local-util'
import { RBDropdown, RBForm, RBInputGroup } from '../../../shared/ReactBootstrap'
import FormLabel from '../shared/FormLabel'
import FormMessage from '../shared/FormMessage'
import './FormDropdown.scss'

const FormDropdown = (props: FormDropdownProps): JSX.Element => {
  const { formContext, setFormContext } = useFormContext()
  const {
    id = 'form-basic-dropdown',
    title,
    name,
    helpMessage,
    helpMessageTitle,
    helpIcon,
    helpIconAlt,
    readonly,
    placeholder,
    requiredMessage,
    requiredMessageIcon,
    requiredMessageIconAlt = '',
    chevronIcon = CHEVRON_DOWN_RIVER_BLUE,
    chevronIconAlt,
    checkMark = CHECK_MARK,
    checkMarkAlt,
    closeIcon = CLOSE_RIVER_BLUE,
    closeIconAlt,
    searchEnabled = true,
    onChange
  } = props
  const contextItems: any =
    (formContext as any)?.[name]?.items && (formContext as any)?.[name]?.items?.length > 0
      ? (formContext as any)?.[name]?.items
      : null
  const items = contextItems || props?.items
  const value = (formContext as any)?.[name]?.value || ''
  const required = (formContext as any)?.[name]?.required || props?.required
  const message = (formContext as any)?.[name]?.message || ''
  const [filteredItems, setFilteredItems] = useState<FormItemProps[]>(items)
  const [isOpen, setIsOpen] = useState(false)
  const status = useFormStatus({ name })
  const focusElementRef = useRef(null)
  const [inputValue, setInputValue] = useState('')
  const toggleRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    let selected
    if (value) {
      selected = items?.find((item: any) => value !== '' && item?.value === value)
      if (selected) {
        setInputValue(selected.text)
        setFormContext(name, selected?.value, props, false)
      }
    } else {
      selected = items?.find((item: any) => item?.selected)
      if (selected) {
        setInputValue(selected.text)
        setFormContext(name, selected?.value, props, false)
      } else {
        setFormContext(name, '', props, false)
      }
    }
  }, [])

  const icon = { path: chevronIcon, alt: chevronIconAlt ?? 'dropdown icon' }
  const checkMarkIcon = { path: checkMark, alt: checkMarkAlt ?? 'checkmark icon' }
  const iconClose = { path: closeIcon, alt: closeIconAlt ?? 'close icon' }

  const toggleDropdown = () => {
    const isOpened = !isOpen
    handleElementFocus(isOpened)
    setIsOpen(isOpened)
  }

  const handleElementFocus = (isFocus: boolean) => {
    if (isFocus) {
      setTimeout(() => {
        if (focusElementRef?.current) {
          ;(focusElementRef?.current as any)?.focus()
        }
      }, 350)
    }
  }

  const handleDropdownChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!searchEnabled) return
    e.preventDefault()

    setInputValue(e.target.value)

    if (e.target.value === '') {
      resetSelectedItems()
      // resetItemList()
    } else {
      const matchedItems = searchesOptions(e.target.value)
      if (matchedItems.length > 0) {
        setFilteredItems(matchedItems)
      } else {
        setFilteredItems([])
      }
    }
  }

  const handleFormControlBlur = () => {
    let val = inputValue
    let showRequiredMsg = required
    /* Error out when user input doesn't match dropdown list items */
    if (val && !validatesDropdownItemSelected()) {
      val = ''
      if (!showRequiredMsg) {
        showRequiredMsg = true
      }
    }
    const matchItems = searchesOptions(val)
    const selectedDDItem = matchItems.length > 0 ? matchItems[0] : filteredItems.find((item: any) => item.selected)
    setFormContext(name, selectedDDItem?.value ?? '', props)
  }

  const searchesOptions = (value?: string) => {
    return items.filter((item: any) => {
      if (value && item.text.toLowerCase().includes(value.toLowerCase())) {
        return item
      }
    })
  }

  function handleOnClick(item: FormItemProps) {
    //resetSelectedItems()
    selectItem(item)
    //resetItemList()
    if (onChange) {
      onChange(item)
    }
    setTimeout(() => {
      toggleRef?.current?.click()
    }, 50)
  }

  // const resetItemList = () => {
  //   setFilteredItems(items)
  // }

  const selectItem = (item: FormItemProps) => {
    setInputValue(item.text)
    // setSelectedDDItem({ ...item, selected: true })
    const newItems = items.map((newItem: FormItemProps) => {
      return { ...newItem, selected: item.text === newItem.text }
    })
    setFilteredItems(newItems)
    // setSelectedDDItem(newItems.find((item: any) => item.selected))
    setFormContext(name, item.value, props)
  }

  const resetSelectedItems = () => {
    const newItems = items.map((item: FormItemProps) => {
      return { ...item, selected: false }
    })
    setFilteredItems(newItems)
  }

  const validatesDropdownItemSelected = () => {
    const isValid = items.find((item: any) => {
      return item.text === inputValue
    })
    return !!isValid
  }

  const placeholderText = placeholder ?? (searchEnabled ? 'Search or select…' : 'Select')

  const getSelectedState = (item: any) => {
    if (value) {
      return value === item.value
    }
    return item.selected
  }

  const getInputValue = () => {
    if (value) {
      return (
        items?.find((item: any) => {
          return item.value === value
        })?.text ?? ''
      )
    }
    return inputValue
  }

  const clearInputValue = () => {
    setInputValue('')
    resetSelectedItems()
    setFormContext(name, '', props)
    if (isOpen) {
      setTimeout(() => {
        toggleRef?.current?.click()
      }, 0)
    }
    toggleRef?.current?.focus()
  }

  return (
    <div data-testid={`${id}-container`} className={`form-basic-dropdown`} id={id}>
      <RBDropdown className={readonly ? 'disabled' : ''} onToggle={toggleDropdown} autoClose={'outside'}>
        <RBForm.Label className={`d-flex align-items-center`}>
          <FormLabel
            title={title}
            required={required}
            requiredMessage={requiredMessage}
            helpMessage={helpMessage}
            helpMessageTitle={helpMessageTitle}
            helpIcon={helpIcon}
            helpIconAlt={helpIconAlt}
            id={`${id}`}
            readOnly={readonly}
          />
        </RBForm.Label>
        <RBInputGroup className={`mb-2 responsive-size`}>
          <RBDropdown.Toggle
            variant='ghost'
            id={'dropdown-toggle'}
            className={`input responsive-size ${status}`}
            disabled={!!readonly}
            data-testid={`${id}-toggle`}
            ref={focusElementRef}
            aria-label={title}
            aria-haspopup={true}
            as={'div'}
          >
            <RBForm.Control
              className={`clean-input flex-2 ${!searchEnabled && 'hide-caret'}`}
              placeholder={placeholderText}
              aria-label={title + (required ? ' (Required)' : '')}
              onChange={handleDropdownChange}
              onBlur={handleFormControlBlur}
              value={getInputValue()}
              data-testid={`form-basic-dropdown${id}-select`}
              readOnly={readonly}
              name={name}
              ref={toggleRef}
            />
            <div className={`icon`}>
              {inputValue && (
                <div className={`icon--close`}>
                  <img
                    onClick={clearInputValue}
                    src={getImageSrc(iconClose.path)}
                    alt={iconClose.alt}
                    data-testid={`${id}-image__closeIcon`}
                  />
                  <span className='icon--close__stroke'></span>
                </div>
              )}
              <div className={`icon--toggle__${!isOpen ? 'up' : 'down'}`}>
                <img src={getImageSrc(icon.path)} alt={icon.alt} data-testid={`${id}-image`} />
              </div>
            </div>
          </RBDropdown.Toggle>
        </RBInputGroup>
        <RBDropdown.Menu data-testid={`${id}-menu`}>
          {filteredItems?.map((item) => {
            if (item.disabled) {
              return
            }
            return (
              <RBDropdown.Item
                data-testid={`${id}-dropdown-item`}
                className={`${getSelectedState(item) && 'dropdown-item__selected'}`}
                key={item.text}
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                value={item.value}
                onClick={() => handleOnClick(item)}
                onBlur={validatesDropdownItemSelected}
              >
                {item.text}
                {getSelectedState(item) && (
                  <img
                    src={getImageSrc(checkMarkIcon.path)}
                    alt={checkMarkIcon.alt}
                    data-testid={`${id}-image__checkMark`}
                  />
                )}
              </RBDropdown.Item>
            )
          })}
        </RBDropdown.Menu>
      </RBDropdown>
      <FormMessage
        id={id}
        status={status}
        message={message}
        requiredMessageIcon={requiredMessageIcon}
        requiredMessageIconAlt={requiredMessageIconAlt}
      />
    </div>
  )
}
export default FormDropdown
