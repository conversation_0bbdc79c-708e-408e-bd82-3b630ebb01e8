import React from 'react'

import { render } from '@testing-library/react'

import { FormOptionsProps } from '..'
import disabled from './_mockData/mockDropdown-disabled.json'
import manyItemsDropdown from './_mockData/mockDropdown-manyItems.json'
import baseProps from './_mockData/mockDropdown.json'
import FormDropdown from './FormDropdown'
import { defaultState, requiredState, setFormContext } from '../../../../contexts/_mockData/mockFormContext'
import { FormProvider } from '../../../../contexts/FormContext'

describe('FormDropdown component', () => {
  it('opens the dropdown and shows the options', () => {
    render(
      <FormProvider value={{ formContext: defaultState, setFormContext: setFormContext }}>
        <FormDropdown {...(baseProps as FormOptionsProps)} />
      </FormProvider>
    )
    // fireEvent.click(screen.getByRole('button'))
    // expect(screen.getByTestId('form-basic-dropdown-menu')).toBeTruthy()
  })
  it('shows (Required) if the input is required', () => {
    render(
      <FormProvider value={{ formContext: requiredState, setFormContext: setFormContext }}>
        <FormDropdown {...(manyItemsDropdown as FormOptionsProps)} />
      </FormProvider>
    )
    // expect(screen.getByText('(Required)')).toBeTruthy()
  })
  it('matches the snapshots', () => {
    expect(
      render(
        <FormProvider value={{ formContext: defaultState, setFormContext: setFormContext }}>
          <FormDropdown {...(baseProps as FormOptionsProps)} />
        </FormProvider>
      )
    ).toMatchSnapshot()
  })
  it('renders a disabled component', () => {
    expect(
      render(
        <FormProvider value={{ formContext: defaultState, setFormContext: setFormContext }}>
          <FormDropdown {...(disabled as FormOptionsProps)} />
        </FormProvider>
      )
    ).toMatchSnapshot()
  })
})
