@import './../../../../assets/styles/v3/globalStyles.scss';

.form-basic-dropdown {
  .btn.show {
    border: var(--utility-on-default-2) solid 2px;
  }

  .form-label {
    @include cox-text-title4-medium;
    line-height: 22px;
  }
  .required-label {
    @include cox-text-paragraph5-regular;
    color: var(--foreground-on-default-3);
  }

  .form-control {
    padding: 0;
    line-height: 24px;

    &::placeholder {
      color: var(--foreground-on-default-4);
    }
  }

  .input {
    border: var(--utility-on-default-2) solid 2px;
    border-radius: 15px;
    padding: 1.25rem;
    display: flex;
    align-items: center;

    background-color: var(--background-default);

    height: 64px;
    z-index: $zindex-auto;

    .form-control {
      color: var(--foreground-on-default-2);
    }

    &:focus {
      outline: none;
      box-shadow: none;
      border: var(--utility-on-default-2) solid 2px;
      border-radius: 15px;
    }

    &:hover {
      border: var(--utility-on-default-2) solid 2px;
      border-radius: 15px;

      background-color: var(--background-default);
    }

    &:active {
      outline: 2px solid $formfield-outline;
      border-radius: 15px;
    }

    &:focus-within {
      outline: 2px solid $formfield-outline;
      border-radius: 15px;
    }
  }

  .input.success {
    border: 2px solid var(--success-default);
  }
  .input.error {
    border: 2px solid var(--error-default);
  }

  .disabled {
    .input {
      background-color: var(--background-muted-1);
    }
    button:disabled input {
      background-color: var(--background-muted-1);
      color: var(--foreground-on-default-disabled);
    }
    .form-label {
      color: var(--utility-on-default-2);
    }
    .required-label {
      color: var(--utility-on-default-2);
    }
  }

  .btn {
    &:active {
      border-color: var(--utility-on-default-2) !important;
    }
  }

  .clean-input {
    border: none;

    &:focus {
      box-shadow: none;
    }
  }

  .hide-caret {
    caret-color: transparent;
    cursor: pointer;
  }

  .responsive-size {
    width: 100%;
  }
  .dropdown-menu {
    max-width: 100%;
    width: 100%;
  }

  .dropdown-toggle::after {
    display: none !important;
  }
  .icon {
    padding-left: 16px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    img {
      width: 24px;
      height: 24px;
      cursor: pointer;
    }
  }
  .icon--toggle__up {
    transform: rotate(0deg);
    transition: transform 0.3s ease;
  }

  .icon--toggle__down {
    transform: rotate(180deg);
    transition: transform 0.3s ease;
  }

  .icon--close {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }

  .icon--close__stroke {
    border-left: 2px solid var(--utility-on-default-2);
    height: 24px;
  }

  .dropdown-menu {
    overflow: auto;
    overflow-x: hidden; /* Hide horizontal scrollbar */

    max-height: 306px;
    min-width: 272px;

    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.15);
    border-radius: 20px;
    border: 0;

    background-color: var(--background-default);

    padding: 12px;
    margin-top: 10px;
    scrollbar-gutter: auto;
    z-index: $zindex-form-dropdown-menu;
  }

  .dropdown-item {
    @include cox-text-paragraph3-regular;
    padding: 12px 20px;
    overflow-wrap: break-word;
    word-break: break-word;
    white-space: break-spaces;

    border-radius: 5px;

    &:hover {
      background-color: var(--interactive-accent-button-primary-on-strong-hover);
      color: var(--foreground-on-default-2);
    }
    &:focus {
      outline: 2px solid $formfield-outline;
    }
  }

  .dropdown-item__selected {
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    img {
      width: 16px;
      height: 16px;
    }
  }

  /* width */
  ::-webkit-scrollbar {
    width: 12px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: var(--background-default);
    margin-bottom: 20px;
    margin-top: 20px;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: var(--utility-on-default-2);
    border-radius: 10px;
    max-height: 60px;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: var(--utility-on-default-2);
  }

  .status-indicator-container {
    display: flex;
    align-items: center;
  }
  .status-indicator-text {
    @include cox-text-paragraph4-regular;
  }
}
