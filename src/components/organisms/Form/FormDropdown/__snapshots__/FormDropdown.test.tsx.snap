// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FormDropdown component matches the snapshots 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="form-basic-dropdown"
        data-testid="form-basic-dropdown-container"
        id="form-basic-dropdown"
      >
        <div
          class="dropdown"
        >
          <label
            class="d-flex align-items-center form-label"
          >
            <div
              class="label-group"
            >
              <span
                class="label-info"
                id="form-basic-dropdown-label"
              >
                <span
                  class="label-title  false"
                  data-testid="form-basic-dropdown-label"
                >
                  States
                </span>
              </span>
              <img
                alt="help-icon"
                class="help-message-icon false"
                data-testid="form-basic-dropdown-opens-modal"
                src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
                tabindex="0"
              />
            </div>
          </label>
          <div
            class="mb-2 responsive-size input-group"
          >
            <div
              aria-expanded="false"
              aria-haspopup="true"
              aria-label="States"
              class="input responsive-size pending dropdown-toggle"
              data-testid="form-basic-dropdown-toggle"
              id="dropdown-toggle"
              variant="ghost"
            >
              <input
                aria-label="States"
                class="clean-input flex-2 false form-control"
                data-testid="form-basic-dropdownform-basic-dropdown-select"
                name="States"
                placeholder="Search or select…"
                value="Arizona"
              />
              <div
                class="icon"
              >
                <div
                  class="icon--close"
                >
                  <img
                    alt="close icon"
                    data-testid="form-basic-dropdown-image__closeIcon"
                    src="/content/dam/cox/common/icons/ui_components/close-river-blue.svg"
                  />
                  <span
                    class="icon--close__stroke"
                  />
                </div>
                <div
                  class="icon--toggle__up"
                >
                  <img
                    alt="chevron-icon"
                    data-testid="form-basic-dropdown-image"
                    src="/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="form-basic-dropdown"
      data-testid="form-basic-dropdown-container"
      id="form-basic-dropdown"
    >
      <div
        class="dropdown"
      >
        <label
          class="d-flex align-items-center form-label"
        >
          <div
            class="label-group"
          >
            <span
              class="label-info"
              id="form-basic-dropdown-label"
            >
              <span
                class="label-title  false"
                data-testid="form-basic-dropdown-label"
              >
                States
              </span>
            </span>
            <img
              alt="help-icon"
              class="help-message-icon false"
              data-testid="form-basic-dropdown-opens-modal"
              src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
              tabindex="0"
            />
          </div>
        </label>
        <div
          class="mb-2 responsive-size input-group"
        >
          <div
            aria-expanded="false"
            aria-haspopup="true"
            aria-label="States"
            class="input responsive-size pending dropdown-toggle"
            data-testid="form-basic-dropdown-toggle"
            id="dropdown-toggle"
            variant="ghost"
          >
            <input
              aria-label="States"
              class="clean-input flex-2 false form-control"
              data-testid="form-basic-dropdownform-basic-dropdown-select"
              name="States"
              placeholder="Search or select…"
              value="Arizona"
            />
            <div
              class="icon"
            >
              <div
                class="icon--close"
              >
                <img
                  alt="close icon"
                  data-testid="form-basic-dropdown-image__closeIcon"
                  src="/content/dam/cox/common/icons/ui_components/close-river-blue.svg"
                />
                <span
                  class="icon--close__stroke"
                />
              </div>
              <div
                class="icon--toggle__up"
              >
                <img
                  alt="chevron-icon"
                  data-testid="form-basic-dropdown-image"
                  src="/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`FormDropdown component renders a disabled component 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="form-basic-dropdown"
        data-testid="state1-container"
        id="state1"
      >
        <div
          class="disabled dropdown"
        >
          <label
            class="d-flex align-items-center form-label"
          >
            <div
              class="label-group"
            >
              <span
                class="label-info"
                id="state1-label"
              >
                <span
                  class="label-title  disabled"
                  data-testid="state1-label"
                >
                  States
                </span>
              </span>
              <img
                alt=""
                class="help-message-icon true"
                data-testid="state1-opens-modal"
                src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
                tabindex="0"
              />
            </div>
          </label>
          <div
            class="mb-2 responsive-size input-group"
          >
            <div
              aria-expanded="false"
              aria-haspopup="true"
              aria-label="States"
              class="input responsive-size pending dropdown-toggle"
              data-testid="state1-toggle"
              disabled=""
              id="dropdown-toggle"
              variant="ghost"
            >
              <input
                aria-label="States"
                class="clean-input flex-2 false form-control"
                data-testid="form-basic-dropdownstate1-select"
                name="States"
                placeholder="Search or select…"
                readonly=""
                value="Arizona"
              />
              <div
                class="icon"
              >
                <div
                  class="icon--close"
                >
                  <img
                    alt="close icon"
                    data-testid="state1-image__closeIcon"
                    src="/content/dam/cox/common/icons/ui_components/close-river-blue.svg"
                  />
                  <span
                    class="icon--close__stroke"
                  />
                </div>
                <div
                  class="icon--toggle__up"
                >
                  <img
                    alt="dropdown icon"
                    data-testid="state1-image"
                    src="/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="form-basic-dropdown"
      data-testid="state1-container"
      id="state1"
    >
      <div
        class="disabled dropdown"
      >
        <label
          class="d-flex align-items-center form-label"
        >
          <div
            class="label-group"
          >
            <span
              class="label-info"
              id="state1-label"
            >
              <span
                class="label-title  disabled"
                data-testid="state1-label"
              >
                States
              </span>
            </span>
            <img
              alt=""
              class="help-message-icon true"
              data-testid="state1-opens-modal"
              src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
              tabindex="0"
            />
          </div>
        </label>
        <div
          class="mb-2 responsive-size input-group"
        >
          <div
            aria-expanded="false"
            aria-haspopup="true"
            aria-label="States"
            class="input responsive-size pending dropdown-toggle"
            data-testid="state1-toggle"
            disabled=""
            id="dropdown-toggle"
            variant="ghost"
          >
            <input
              aria-label="States"
              class="clean-input flex-2 false form-control"
              data-testid="form-basic-dropdownstate1-select"
              name="States"
              placeholder="Search or select…"
              readonly=""
              value="Arizona"
            />
            <div
              class="icon"
            >
              <div
                class="icon--close"
              >
                <img
                  alt="close icon"
                  data-testid="state1-image__closeIcon"
                  src="/content/dam/cox/common/icons/ui_components/close-river-blue.svg"
                />
                <span
                  class="icon--close__stroke"
                />
              </div>
              <div
                class="icon--toggle__up"
              >
                <img
                  alt="dropdown icon"
                  data-testid="state1-image"
                  src="/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;