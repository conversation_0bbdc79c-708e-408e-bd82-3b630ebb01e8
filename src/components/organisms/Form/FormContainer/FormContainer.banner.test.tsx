import React from 'react'

import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import axios from 'axios'

import formContainerProps from './_mockData/mockData-formcontainer.json'
import { FormStateType, MessageStatus } from '../../../../types'
import * as formValidationUtil from '../../../../utils/form-validation-util'

import FormContainer, { FormContainerProps } from '.'

jest.mock('axios', () => ({
  __esModule: true,
  default: jest.fn(() => Promise.resolve({ status: 200, data: {} }))
}))

describe('FormContainer Banner display and positioning', () => {
  // Import enums for type-safe props

  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const { FormFieldsAlign, FormStatusPosition } = require('./types')
  test('renders the banner correctly', () => {
    render(<FormContainer {...(formContainerProps as unknown as FormContainerProps)} />)
    const bannerElement = screen.getByTestId('form-container')
    expect(bannerElement).toBeTruthy()
  })

  it('should display success banner with correct properties', async () => {
    const formState: FormStateType = {
      name: { value: 'John Doe', isRequired: false }
    }
    ;(axios as unknown as jest.Mock).mockResolvedValueOnce({ status: 200, data: {} })
    jest.spyOn(formValidationUtil, 'validateForm').mockImplementation(() => true)
    render(
      <FormContainer
        {...(formContainerProps as unknown as FormContainerProps)}
        formState={formState}
        method='post'
        action='/test-endpoint'
        successMessage='Success Message'
      />
    )
    fireEvent.submit(screen.getByRole('button', { name: 'SUBMIT' }))
    await waitFor(() => {
      expect(axios).toHaveBeenCalled()
    })
    expect(screen.getByTestId('form-container')).toBeInTheDocument()
  })

  it('renders with fieldsAlignment left', () => {
    render(
      <FormContainer {...(formContainerProps as unknown as FormContainerProps)} fieldsAlignment={FormFieldsAlign.LEFT} />
    )
    expect(screen.getByTestId('form-container').className).toContain('fields-align-left')
  })

  it('shows banner at the top when statusMessagePosition is TOP', async () => {
    jest.spyOn(formValidationUtil, 'validateForm').mockImplementation(() => true)
    ;(axios as unknown as jest.Mock).mockResolvedValueOnce({ status: 200, data: {} })
    render(
      <FormContainer
        {...(formContainerProps as unknown as FormContainerProps)}
        formState={{ name: { value: 'John', isRequired: false } }}
        method={'post'}
        action={'/test'}
        successMessage={'Success'}
        statusMessagePosition={FormStatusPosition.TOP}
      />
    )
    fireEvent.submit(screen.getByRole('button', { name: 'SUBMIT' }))
    expect(await screen.findByTestId('form-container')).toBeInTheDocument()
    expect(await screen.findByText('Success')).toBeInTheDocument()
  })

  it('uses only eyebrow as formHeading', () => {
    render(
      <FormContainer
        {...(formContainerProps as unknown as FormContainerProps)}
        eyebrow={'Eyebrow Only'}
        heading={''}
        subheading={''}
      />
    )
    expect(screen.getByText('Eyebrow Only')).toBeInTheDocument()
  })

  it('calls sendData if onSubmit is not provided', async () => {
    jest.spyOn(formValidationUtil, 'validateForm').mockImplementation(() => true)
    ;(axios as unknown as jest.Mock).mockResolvedValueOnce({ status: 200, data: {} })
    render(
      <FormContainer
        {...(formContainerProps as unknown as FormContainerProps)}
        formState={{ name: { value: 'John', isRequired: false } }}
        method={'post'}
        action={'/test'}
      />
    )
    fireEvent.submit(screen.getByRole('button', { name: 'SUBMIT' }))
    await waitFor(() => expect(axios).toHaveBeenCalled())
  })

  it('renders banner with success and error links/icons', async () => {
    jest.spyOn(formValidationUtil, 'validateForm').mockImplementation(() => true)
    ;(axios as unknown as jest.Mock).mockResolvedValueOnce({ status: 200, data: {} })
    render(
      <FormContainer
        {...(formContainerProps as unknown as FormContainerProps)}
        formState={{ name: { value: 'John', isRequired: false } }}
        method={'post'}
        action={'/test'}
        successMessage={'Success'}
        successMessageLink={'https://success'}
        successMessageLinkText={'Success Link'}
        successIcon={'success-icon.svg'}
        errorMessage={'Error'}
        errorMessageLink={'https://error'}
        errorMessageLinkText={'Error Link'}
        errorIcon={'error-icon.svg'}
      />
    )
    fireEvent.submit(screen.getByRole('button', { name: 'SUBMIT' }))
    expect(await screen.findByTestId('form-container')).toBeInTheDocument()
    expect(await screen.findByText('Success')).toBeInTheDocument()
  })

  it('uses only eyebrow as formHeading', () => {
    render(
      <FormContainer
        {...(formContainerProps as unknown as FormContainerProps)}
        eyebrow='Eyebrow Only'
        heading=''
        subheading=''
      />
    )
    expect(screen.getByText('Eyebrow Only')).toBeInTheDocument()
  })

  it('disables submit button after success', async () => {
    jest.spyOn(formValidationUtil, 'validateForm').mockImplementation(() => true)
    ;(axios as unknown as jest.Mock).mockResolvedValueOnce({ status: 200, data: {} })
    render(
      <FormContainer
        {...(formContainerProps as unknown as FormContainerProps)}
        formState={{ name: { value: 'John', isRequired: false } }}
        method='post'
        action='/test'
        successMessage='Success'
      />
    )
    fireEvent.submit(screen.getByRole('button', { name: 'SUBMIT' }))
    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'SUBMIT' }).classList.contains('disabled')).toBeTruthy()
    })
  })
})
describe('Banner message focus', () => {
  it('should focus on banner message after form validation fails', async () => {
    // Create a form state with a required field that has no value to fail validation
    const invalidFormState: FormStateType = {
      sampletxt: {
        value: '',
        isRequired: true,
        status: MessageStatus.PENDING,
        inputType: 'text'
      }
    }

    // Mock validateForm to return false (validation fails)
    jest.spyOn(formValidationUtil, 'validateForm').mockImplementation(() => false)

    // Create a spy on document.querySelector to track if focus is called
    const mockElement = {
      focus: jest.fn()
    }
    jest.spyOn(document, 'querySelector').mockImplementation(() => mockElement as unknown as Element)

    // Use fake timers to control setTimeout
    jest.useFakeTimers()

    render(
      <FormContainer
        {...(formContainerProps as unknown as FormContainerProps)}
        formState={invalidFormState}
        errorMessage='Validation Error'
      />
    )

    // Submit the form to trigger validation failure
    fireEvent.submit(screen.getByRole('button', { name: 'SUBMIT' }))

    // Fast-forward timers
    jest.advanceTimersByTime(400)

    // Check that the form container is present
    expect(screen.getByTestId('form-container')).toBeInTheDocument()

    // Restore mocks and timers
    jest.restoreAllMocks()
    jest.useRealTimers()
  })
})
