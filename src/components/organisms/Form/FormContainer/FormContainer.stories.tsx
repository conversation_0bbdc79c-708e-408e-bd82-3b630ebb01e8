import type { <PERSON>a, StoryObj } from '@storybook/react'

import { FormOptionsLayout, FormOptionsTypes } from '..'
import FormInput, { FormInputTypes } from './../FormInput'
import FormContainer from './FormContainer'
import { MessageStatus } from '../../../../types'
import { ToggleValue } from '../../../atoms/Toggle'
import FormCheckbox from '../FormCheckbox'
import FormDropdown from '../FormDropdown'
import FormRadioButton from '../FormRadioButton'
import FormToggle, { FormToggleType } from '../FormToggle'
import { FormValidation } from '../shared/FormChecklist/types'

const meta: Meta<typeof FormContainer> = {
  title: 'organisms/FormContainer',
  component: FormContainer,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {}
}

export default meta
type Story = StoryObj<typeof FormContainer>

const validations: FormValidation[] = [
  { message: 'Min length required', pattern: new RegExp(/.{3,}$/) }, //do we need to add for each validation??
  { message: 'One Upper case rquired', pattern: new RegExp(/.*[A-Z].*/) },
  { message: 'Number required', pattern: new RegExp(/.*[0-9].*/) },
  { message: 'Max length exceeded', pattern: new RegExp(/^.{0,10}$/) }
]

export const FormSubmit: Story = {
  args: {
    children: (
      <>
        <FormInput
          iconPosition='left'
          required={true}
          title='Email1'
          helpMessage=''
          rows={2}
          requiredMessage='This field is required'
          placeholderText='Enter the email here'
          allowNumbers={false}
          splCharacters={true}
          name='externalEmail'
          isPII={true}
          value='test'
          id='form-text-**********'
          type={FormInputTypes.EMAIL}
          readOnly={false}
        />

        <FormDropdown
          type={FormOptionsTypes.dropdown}
          searchEnabled={false}
          title='Type of Property Needing Service'
          name='currentProvider'
          layout={FormOptionsLayout.HORIZONTAL}
          id='form-options-**********'
          required={true}
          requiredMessage='This field is required'
          parameters={false}
          helpMessage=''
          chevronIcon='/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg'
          magnifyingGlass='/content/dam/cox/common/icons/ui_components/magnifying-glass-grey.svg'
          magnifyingGlassAlt='magnifying-glass-alt'
          chevronIconAlt='chevron-icon-alt'
          items={[
            {
              text: 'Other',
              selected: false,
              disabled: false,
              value: 'Other'
            },
            {
              text: 'Verizon',
              selected: false,
              disabled: false,
              value: 'Verizon'
            }
          ]}
        />

        <FormRadioButton
          id={'gender-id'}
          title={`Are you already a Cox Internet
							customer?`}
          name={'visitorType'}
          required={true}
          requiredMessage='This field is required'
          helpMessage=''
          type={FormOptionsTypes.radio}
          items={[
            {
              text: 'Yes',
              selected: false,
              disabled: false,
              value: 'Current customer'
            },
            {
              text: 'No',
              selected: false,
              disabled: false,
              value: 'Prospect'
            }
          ]}
        />
        <FormToggle
          name={'eMailNotification'}
          // selected={true}
          value={ToggleValue.selected}
          label={'Email Notification'}
          id='form-toggle-email'
          type={FormToggleType.TOGGLE}
          required={true}
          requiredMessage='This field is required'
        />
        <FormCheckbox
          type={FormOptionsTypes.checkbox}
          title='By clicking submit you authorize Cox to contact you. Your ZIP code will be used to confirm service availability in your area'
          name='externalMobileConsent'
          layout={FormOptionsLayout.HORIZONTAL}
          id='form-options-1326188867'
          required={true}
          requiredMessage='This field is required'
          parameters={false}
          helpMessage=''
          radioCheckMark='/content/dam/cox/common/icons/ui_components/white-check-mark.svg'
          checkboxCheckMark='/content/dam/cox/common/icons/ui_components/white-check-mark.svg'
          items={[
            {
              text: 'I authorize Cox to contact me by phone',
              selected: false,
              disabled: false,
              value: 'on'
            }
          ]}
        />
      </>
    ),
    formState: {
      currentProvider: {
        value: '',
        isRequired: false,
        status: MessageStatus.PENDING,
        inputType: 'DROP_DOWN',
        parameters: false
      },
      visitorType: {
        value: '',
        isRequired: true,
        status: MessageStatus.PENDING,
        inputType: 'RADIO'
      },
      externalEmail: {
        value: '',
        isRequired: false,
        status: MessageStatus.PENDING,
        inputType: 'text'
      },
      eMailNotification: {
        value: '',
        inputType: 'toggle'
      },
      externalMobileConsent: {
        isRequired: false,
        value: '',
        status: MessageStatus.PENDING,
        inputType: 'CHECKBOX',
        parameters: false
      }
    },
    disclaimer:
      '<p><span class="cox-text-paragraph5-regular">Disclaimer - By clicking <a title="gigUnlimitedPlanDetails" data-link-type="modal" href="/residential/storybook/react-form-container.html#gigUnlimitedPlanDetails" target="modal" rel="noopener noreferrer">submit</a> you authorize Cox to contact you.</span></p>\r\n',
    action: 'https://test.cox.com/residential-shop/lead-capture',
    method: 'post',
    errorMessage: 'Error Message',
    successMessage: 'Success Message',
    linkInNewTab: false,
    errorIcon: '/content/dam/cox/common/icons/ui_components/circle-exclamation-moderate-red.svg',
    successIcon: '/content/dam/cox/common/icons/ui_components/circle-check-lime-green.svg',
    technicalErrormessage: 'Technical Error message',
    redirectInNewTab: false,
    eyebrow: 'Lead Capture Form',
    heading: 'Form submission',
    isHtmlForm: false
  }
}

export const FormWithCheckList: Story = {
  args: {
    children: (
      <>
        <FormInput
          iconPosition='left'
          required={true}
          title='Name'
          helpMessage='this is help message'
          rows={2}
          requiredMessage='This field is required'
          allowNumbers={true}
          splCharacters={true}
          name='name'
          value=''
          id='form-text-**********'
          type={FormInputTypes.TEXT}
          readOnly={true}
          showChecklist={true}
          pattern={new RegExp(/.{3,8}$/)}
          patternErrorMessage='At least 3 characters are required'
          validations={validations}
          patternSuccessMessage=''
        />
        <FormInput
          iconPosition='left'
          required={true}
          title='Number of Household'
          helpMessage=''
          rows={2}
          // requiredMessage='This field is required'
          name='numberField'
          value=''
          id='form-text-1978025874'
          type={FormInputTypes.ZIPCODE}
          readOnly={false}
        />
        <FormInput
          iconPosition='left'
          required={true}
          title='Number of Household'
          helpMessage=''
          rows={2}
          requiredMessage='This field is required'
          name='numberField1'
          value=''
          id='form-text-1978025874'
          type={FormInputTypes.PHONE}
          readOnly={false}
        />
        <FormDropdown
          type={FormOptionsTypes.dropdown}
          title='Type of Property Needing Service'
          name='propertyType'
          layout={FormOptionsLayout.HORIZONTAL}
          id='form-options-**********'
          required={true}
          requiredMessage='Selection is required'
          parameters={false}
          chevronIcon='/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg'
          magnifyingGlass='/content/dam/cox/common/icons/ui_components/magnifying-glass-grey.svg'
          magnifyingGlassAlt='magnifying-glass-alt'
          chevronIconAlt='chevron-icon-alt'
          items={[
            {
              text: 'Select Item',
              selected: true,
              disabled: false,
              value: ''
            },
            {
              text: 'OTHER',
              selected: false,
              disabled: false,
              value: 'OTHER'
            },
            {
              text: 'SFU',
              selected: false,
              disabled: false,
              value: 'SFU'
            }
          ]}
        />
        <FormRadioButton
          id={'gender-id'}
          title={'Gender'}
          name={'gender'}
          required={true}
          helpMessage='help'
          requiredMessage='Gender is required field'
          type={FormOptionsTypes.radio}
          items={[
            {
              text: 'Male',
              selected: false,
              disabled: false,
              value: 'Male'
            },
            {
              text: 'Female',
              selected: false,
              disabled: false,
              value: 'Female'
            }
          ]}
        />
        <FormCheckbox
          type={FormOptionsTypes.checkbox}
          title='By clicking submit you authorize Cox to contact you. Your ZIP code will be used to confirm service availability in your area'
          name='externalMobileConsent'
          layout={FormOptionsLayout.HORIZONTAL}
          id='form-options-1326188867'
          required={true}
          parameters={false}
          requiredMessage='This field is required'
          radioCheckMark='/content/dam/cox/common/icons/ui_components/white-check-mark.svg'
          checkboxCheckMark='/content/dam/cox/common/icons/ui_components/white-check-mark.svg'
          items={[
            {
              text: 'I authorize Cox to contact me by phone',
              selected: false,
              disabled: false,
              value: 'on'
            }
          ]}
        />
      </>
    ),
    formState: {
      propertyType: {
        value: 'OTHER',
        isRequired: false,
        status: MessageStatus.PENDING,
        inputType: 'DROP_DOWN',
        parameters: false
      },
      gender: {
        value: '',
        isRequired: true,
        status: MessageStatus.PENDING,
        inputType: 'RADIO'
      },
      name: {
        value: '',
        status: MessageStatus.PENDING,
        message: ''
      },
      numberField: {
        value: '',
        status: MessageStatus.PENDING,
        message: ''
      },
      numberField1: {
        value: '',
        status: MessageStatus.PENDING,
        message: ''
      },
      externalMobileConsent: {
        isRequired: false,
        value: [],
        status: MessageStatus.PENDING,
        inputType: 'CHECKBOX',
        parameters: false
      }
    },
    disclaimer:
      '<p><span class="cox-text-paragraph5-regular">Disclaimer - By clicking <a title="gigUnlimitedPlanDetails" data-link-type="modal" href="/residential/storybook/react-form-container.html#gigUnlimitedPlanDetails" target="modal" rel="noopener noreferrer">submit</a> you authorize Cox to contact you.</span></p>\r\n',
    action: 'https://test.cox.com/residential-shop/lead-capture',
    method: 'post',
    errorMessage: 'Please fill highlighed fields to submit',
    successMessage: 'Success Message',
    linkInNewTab: false,
    errorIcon: '/content/dam/cox/common/icons/ui_components/circle-exclamation-moderate-red.svg',
    successIcon: '/content/dam/cox/common/icons/ui_components/circle-check-lime-green.svg',
    technicalErrormessage: 'Technical Error message',
    redirectInNewTab: false,
    eyebrow: 'Bussiness',
    heading: 'MDU  Form',
    id: 'test01'
  }
}

export const WithOnSubmit: Story = {
  args: {
    children: (
      <>
        <FormInput
          iconPosition='left'
          required={true}
          title='Name'
          helpMessage=''
          rows={2}
          requiredMessage='is requried'
          patternErrorMessage='test'
          allowNumbers={true}
          splCharacters={true}
          name='name'
          id='form-text-**********'
          type={FormInputTypes.PASSWORD}
          readOnly={false}
        />
        <FormInput
          iconPosition='left'
          required={true}
          title='Phone'
          helpMessage=''
          rows={2}
          requiredMessage='This is required'
          allowNumbers={false}
          splCharacters={true}
          name='phone'
          value=''
          id='form-text-19780255'
          type={FormInputTypes.PHONE}
          readOnly={false}
        />
        <FormDropdown
          type={FormOptionsTypes.dropdown}
          title='Type of Property Needing Service'
          name='propertyType'
          layout={FormOptionsLayout.HORIZONTAL}
          id='form-options-**********'
          required={false}
          parameters={false}
          chevronIcon='/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg'
          magnifyingGlass='/content/dam/cox/common/icons/ui_components/magnifying-glass-grey.svg'
          magnifyingGlassAlt='magnifying-glass-alt'
          chevronIconAlt='chevron-icon-alt'
          onChange={() => {
            console.log('onChange')
          }}
          items={[
            {
              text: 'OTHER',
              selected: true,
              disabled: false,
              value: 'OTHER'
            },
            {
              text: 'SFU',
              selected: false,
              disabled: false,
              value: 'SFU'
            }
          ]}
        />
        <FormRadioButton
          id={'gender-id'}
          title={'Gender'}
          name={'gender'}
          type={FormOptionsTypes.radio}
          items={[
            {
              text: 'Male',
              selected: false,
              disabled: false,
              value: 'Male'
            },
            {
              text: 'Female',
              selected: false,
              disabled: false,
              value: 'Female'
            }
          ]}
        />
        <FormCheckbox
          type={FormOptionsTypes.checkbox}
          title='By clicking submit you authorize Cox to contact you. Your ZIP code will be used to confirm service availability in your area'
          name='externalMobileConsent'
          layout={FormOptionsLayout.HORIZONTAL}
          id='form-options-1326188867'
          required={false}
          parameters={false}
          radioCheckMark='/content/dam/cox/common/icons/ui_components/white-check-mark.svg'
          checkboxCheckMark='/content/dam/cox/common/icons/ui_components/white-check-mark.svg'
          items={[
            {
              text: 'I authorize Cox to contact me by phone',
              selected: false,
              disabled: false,
              value: 'phone'
            },
            {
              text: 'Opt for SMS/ text messages',
              selected: false,
              disabled: false,
              value: 'sms'
            }
          ]}
        />
        <FormToggle
          name={'eMailNotification'}
          // selected={true}
          value={ToggleValue.selected}
          label={'Email Notification'}
          id='form-toggle-email'
          type={FormToggleType.TOGGLE}
        />
      </>
    ),
    // formState: {
    //   // propertyType: {
    //   //   value: '',
    //   //   isRequired: false,
    //   //   status: MessageStatus.PENDING,
    //   //   inputType: 'DROP_DOWN',
    //   //   parameters: false
    //   // },
    //   // gender: {
    //   //   value: 'Female',
    //   //   isRequired: true,
    //   //   status: MessageStatus.PENDING,
    //   //   inputType: 'RADIO'
    //   // },
    //   // name: {
    //   //   value: 'John Doe #1',
    //   //   isRequired: false,
    //   //   status: MessageStatus.PENDING
    //   // },
    //   // externalMobileConsent: {
    //   //   isRequired: false,
    //   //   value: [''],
    //   //   status: MessageStatus.PENDING,
    //   //   inputType: 'CHECKBOX',
    //   //   parameters: false
    //   // },
    //   // eMailNotification: {
    //   //   value: 'off',
    //   //   inputType: 'toggle'
    //   // }
    // },
    disclaimer:
      '<p><span class="cox-text-paragraph5-regular">Disclaimer - By clicking <a title="gigUnlimitedPlanDetails" data-link-type="modal" href="/residential/storybook/react-form-container.html#gigUnlimitedPlanDetails" target="modal" rel="noopener noreferrer">submit</a> you authorize Cox to contact you.</span></p>\r\n',
    action: 'https://webto.salesforce.com/servlet/servlet.WebToLead?encoding=UTF-8',
    method: 'post',
    errorMessage: 'Error Message',
    successMessage: 'Success Message',
    linkInNewTab: false,
    errorIcon: '/content/dam/cox/common/icons/ui_components/circle-exclamation-moderate-red.svg',
    successIcon: '/content/dam/cox/common/icons/ui_components/circle-check-lime-green.svg',
    technicalErrormessage: 'Technical Error message',
    redirectInNewTab: false,
    eyebrow: 'Bussiness',
    heading: 'MDU  Form',
    // onSubmit: () => {
    //   console.log('sucess')
    // },
    isHtmlForm: true
  }
}

export const NestedForm: Story = {
  args: {
    children: (
      <>
        <FormInput
          iconPosition='left'
          required={true}
          title='Email'
          helpMessage='this is help message'
          rows={2}
          requiredMessage='This field is required'
          allowNumbers={false}
          splCharacters={true}
          name='externalEmail'
          value='test'
          id='form-text-**********'
          type={FormInputTypes.TEXT}
          readOnly={false}
        />

        <FormDropdown
          type={FormOptionsTypes.dropdown}
          title='Type of Property Needing Service'
          name='currentProvider'
          layout={FormOptionsLayout.HORIZONTAL}
          id='form-options-**********'
          required={true}
          parameters={false}
          chevronIcon='/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg'
          magnifyingGlass='/content/dam/cox/common/icons/ui_components/magnifying-glass-grey.svg'
          magnifyingGlassAlt='magnifying-glass-alt'
          chevronIconAlt='chevron-icon-alt'
          items={[
            {
              text: 'Other',
              selected: true,
              disabled: false,
              value: 'Other'
            },
            {
              text: 'Verizon',
              selected: false,
              disabled: false,
              value: 'Verizon'
            }
          ]}
        />
        <FormRadioButton
          id={'gender-id'}
          title={`Are you already a Cox Internet
      customer?`}
          name={'visitorType'}
          type={FormOptionsTypes.radio}
          items={[
            {
              text: 'Yes',
              selected: true,
              disabled: false,
              value: 'Current customer'
            },
            {
              text: 'No',
              selected: false,
              disabled: false,
              value: 'Prospect'
            }
          ]}
        />
        <FormToggle
          name={'eMailNotification'}
          // selected={true}
          type={FormToggleType.TOGGLE}
          value={ToggleValue.selected}
          label={'Email Notification'}
          id='form-toggle-email'
        />
        <FormCheckbox
          type={FormOptionsTypes.checkbox}
          title='By clicking submit you authorize Cox to contact you. Your ZIP code will be used to confirm service availability in your area'
          name='externalMobileConsent'
          layout={FormOptionsLayout.HORIZONTAL}
          id='form-options-1326188867'
          required={false}
          parameters={false}
          radioCheckMark='/content/dam/cox/common/icons/ui_components/white-check-mark.svg'
          checkboxCheckMark='/content/dam/cox/common/icons/ui_components/white-check-mark.svg'
          items={[
            {
              text: 'I authorize Cox to contact me by phone',
              selected: false,
              disabled: false,
              value: 'on'
            }
          ]}
        />
      </>
    ),
    formState: {
      currentProvider: {
        value: 'Other',
        isRequired: false,
        status: MessageStatus.PENDING,
        inputType: 'DROP_DOWN',
        parameters: false
      },
      visitorType: {
        value: '',
        isRequired: true,
        status: MessageStatus.PENDING,
        inputType: 'RADIO'
      },
      externalEmail: {
        value: '',
        isRequired: false,
        status: MessageStatus.PENDING,
        inputType: 'text'
      },
      phone: {
        value: '',
        isRequired: true,
        status: MessageStatus.PENDING,
        inputType: 'text'
      },
      eMailNotification: {
        value: 'off',
        inputType: 'toggle'
      },
      externalMobileConsent: {
        isRequired: false,
        value: '',
        status: MessageStatus.PENDING,
        inputType: 'CHECKBOX',
        parameters: false
      }
    },
    disclaimer:
      '<p><span class="cox-text-paragraph5-regular">Disclaimer - By clicking <a title="gigUnlimitedPlanDetails" data-link-type="modal" href="/residential/storybook/react-form-container.html#gigUnlimitedPlanDetails" target="modal" rel="noopener noreferrer">submit</a> you authorize Cox to contact you.</span></p>\r\n',
    action: 'https://test.cox.com/residential-shop/lead-capture',
    method: 'post',
    errorMessage: 'Error Message',
    successMessage: 'Success Message',
    linkInNewTab: false,
    errorIcon: '/content/dam/cox/common/icons/ui_components/circle-exclamation-moderate-red.svg',
    successIcon: '/content/dam/cox/common/icons/ui_components/circle-check-lime-green.svg',
    technicalErrormessage: 'Technical Error message',
    redirectInNewTab: false,
    eyebrow: 'Bussiness',
    heading: 'MDU  Form'
  }
}
export const WithInlineFormIcon: Story = {
  args: {
    children: (
      <>
        <FormInput
          iconPosition='right'
          required={true}
          title='Name'
          helpMessage={'This is default help message'}
          rows={2}
          requiredMessage=''
          allowNumbers={false}
          splCharacters={true}
          name='name'
          value=''
          id='form-text-**********'
          type={FormInputTypes.TEXT}
          readOnly={false}
          showInlineIcon={true}
        />
        <FormInput
          iconPosition='left'
          required={false}
          title='City'
          helpMessage=''
          rows={2}
          requiredMessage=''
          allowNumbers={false}
          splCharacters={true}
          name='city'
          value=''
          id='form-text-1978025574'
          type={FormInputTypes.TEXT}
          readOnly={false}
        />
      </>
    ),
    formState: {
      name: {
        value: '',
        isRequired: true,
        status: MessageStatus.PENDING,
        inputType: 'text'
      },
      city: {
        value: '',
        isRequired: false,
        status: MessageStatus.PENDING,
        inputType: 'text'
      }
    },
    disclaimer:
      '<p><span class="cox-text-paragraph5-regular">Disclaimer - By clicking <a title="gigUnlimitedPlanDetails" data-link-type="modal" href="/residential/storybook/react-form-container.html#gigUnlimitedPlanDetails" target="modal" rel="noopener noreferrer">submit</a> you authorize Cox to contact you.</span></p>\r\n',
    action: 'https://test.cox.com/residential-shop/lead-capture',
    method: 'post',
    errorMessage: 'Error Message',
    successMessage: 'Success Message',
    linkInNewTab: false,
    errorIcon: '/content/dam/cox/common/icons/ui_components/circle-exclamation-moderate-red.svg',
    successIcon: '/content/dam/cox/common/icons/ui_components/circle-check-lime-green.svg',
    technicalErrormessage: 'Technical Error message',
    redirectInNewTab: false,
    eyebrow: 'Form Validation',
    heading: 'Default  Form',
    onSubmit: () => {
      alert('success ')
    }
  }
}
