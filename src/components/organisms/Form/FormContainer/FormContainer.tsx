import React, { FormEvent, KeyboardEvent, useCallback, useEffect, useRef, useState } from 'react'

import axios from 'axios'
import { useImmerReducer } from 'use-immer'

import { FormContainerProps, FormFieldsAlign, FormReducer, FormStatusPosition } from './types'
import { FormProvider } from '../../../../contexts/FormContext'
import { FormStateType, MessageStatus } from '../../../../types'
import { validateForm, validateInput, validateInputArray } from '../../../../utils/form-validation-util'
import { extractNumericNumber } from '../../../../utils/helper-util'
import { Banner, BannerType, BannerVariaion } from '../../../atoms/Banner'
import { ButtonStates, ButtonTypes } from '../../../atoms/Button'
import ButtonGroup from '../../../atoms/ButtonGroup'
import { RichText } from '../../../atoms/RichText'
import { SectionHeader } from '../../../atoms/SectionHeader'
import { FormInputTypes } from '../FormInput'

import './FormContainer.scss'

const FormContainer: React.FC<FormContainerProps> = ({
  id = 'form',
  cssClass = '',
  action = '',
  redirectPage = '',
  withCredentials = true,
  eyebrow = '',
  heading = '',
  method = '',
  subheading = '',
  disclaimer = '',
  linkInNewTab = false,
  redirectInNewTab = false,
  technicalErrormessage = '',
  errorMessage = '',
  errorMessageLink = '',
  errorMessageLinkText = '',
  errorIcon,
  errorIconAlt,
  successMessage = '',
  successMessageLink = '',
  successMessageLinkText = '',
  successIcon,
  successIconAlt,
  formState = {},
  children = [],
  successLinkId = '',
  successLinkType,
  errorLinkId = '',
  errorLinkType,
  isHtmlForm = false,
  statusMessagePosition = FormStatusPosition.BOTTOM,
  fieldsAlignment = FormFieldsAlign.CENTER,
  onSubmit,
  formButtons = [
    {
      isFormSubmit: true,
      buttonTypes: ButtonTypes.PRIMARY,
      text: 'SUBMIT',
      cssClass: ''
    }
  ]

  // ...rest
}) => {
  const [status, setStatus] = useState(MessageStatus.PENDING)
  const [showBanner, setShowBanner] = useState(false)
  const [error, setError] = useState(errorMessage)
  const [updatedFormButtons, setUpdatedFormButtons] = useState(formButtons)
  const [state, dispatch] = useImmerReducer(FormReducer, formState)
  const isFormAlignLeft = fieldsAlignment == FormFieldsAlign.LEFT
  const formHeading = eyebrow || heading || subheading
  const bannerMessageRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    setShowBanner(status === MessageStatus.SUCCESS || status === MessageStatus.ERROR)
    setUpdatedFormButtons((prevItems) =>
      prevItems.map((item) =>
        item.isFormSubmit
          ? { ...item, buttonStates: status === MessageStatus.SUCCESS ? ButtonStates.DISABLED : ButtonStates.ACTIVE }
          : item
      )
    )
  }, [status])

  interface Response {
    valid: boolean
    message: string
  }

  const handleIsDirty = (name: string, newValue: any, fieldProps: any, isDirty = true, skipValidation = false) => {
    let response: Response
    if (Array.isArray(newValue)) {
      response = validateInputArray(fieldProps, newValue)
      dispatch({
        key: name,
        payload: {
          ...state[name],
          ...fieldProps,
          value: newValue || '',
          status: response.valid ? MessageStatus.SUCCESS : MessageStatus.ERROR,
          message: response.message,
          isDirty: isDirty
        }
      })
    } else if (typeof newValue === 'object') {
      const isEmpty = newValue.value === '' ? false : isDirty
      const getInputResponse = (): Response => {
        if (skipValidation) {
          return {
            valid: newValue.status === MessageStatus.SUCCESS,
            message:
              newValue.status === MessageStatus.SUCCESS ? newValue.patternSuccessMessage : newValue.patternErrorMessage
          }
        } else {
          return validateInput(fieldProps, newValue.value, isEmpty)
        }
      }
      response = getInputResponse()
      dispatch({
        key: name,
        payload: {
          ...state[name],
          ...fieldProps,
          ...newValue,
          value: newValue.value || '',
          status: response.valid ? MessageStatus.SUCCESS : MessageStatus.ERROR,
          message: response.message,
          isDirty: isEmpty
        }
      })
    } else {
      const isEmpty = newValue === '' ? false : isDirty
      const getInputResponse = (): Response => {
        if (skipValidation) {
          return {
            valid: true,
            message: ''
          }
        } else {
          return validateInput(fieldProps, newValue, isEmpty)
        }
      }
      response = getInputResponse()
      dispatch({
        key: name,
        payload: {
          ...state[name],
          ...fieldProps,
          value: newValue || '',
          status: response.valid ? MessageStatus.SUCCESS : MessageStatus.ERROR,
          message: response.message,
          isDirty: isEmpty
        }
      })
    }
  }

  const handleInitialState = (name: string, newValue: any, fieldProps: any = {}) => {
    if (typeof newValue === 'object') {
      dispatch({
        key: name,
        payload: {
          ...state[name],
          ...fieldProps,
          ...newValue
        }
      })
    } else {
      dispatch({
        key: name,
        payload: {
          ...state[name],
          ...fieldProps,
          value: newValue || ''
        }
      })
    }
  }

  const dispatchFormContext = useCallback(
    (name: string, newValue: any, fieldProps: any = {}, isDirty = true, skipValidation = false) => {
      if (isDirty) {
        handleIsDirty(name, newValue, fieldProps, isDirty, skipValidation)
      } else {
        handleInitialState(name, newValue, fieldProps)
      }
    },
    [dispatch]
  )
  type FormDataType = {
    [key: string]: string | object
  }
  const createFormData = () => {
    const formData: FormDataType = {}
    let additionalNotes = ''
    for (const [key, value] of Object.entries(state)) {
      if (value?.parameters) {
        additionalNotes = `${additionalNotes}${additionalNotes && ', '}${key} : ${value.value}`
      } else {
        formData[key as keyof FormStateType] =
          value.inputType == FormInputTypes.PHONE ? extractNumericNumber(value.value.toString()) : value.value
      }
    }
    // adds additionalNotes per Stacie specs
    if (additionalNotes) {
      formData.additionalNotes = additionalNotes
    }
    return formData
  }
  const focusBannerMessage = () => {
    setTimeout(() => {
      if (bannerMessageRef) {
        const { current } = bannerMessageRef
        if (current) {
          current.focus()
        }
      }
    }, 350)
  }

  const submitForm = async (e: FormEvent<HTMLFormElement>) => {
    setStatus(MessageStatus.PENDING)
    if (validateForm(state, dispatch)) {
      if (!isHtmlForm) {
        e.preventDefault()
        if (onSubmit) {
          onSubmit(e)
        } else {
          sendData()
        }
      }
    } else {
      e.preventDefault()
      setStatus(MessageStatus.ERROR)
      focusBannerMessage()
    }
  }
  const sendData = () => {
    const formData = createFormData()
    const sendData = async (formData: FormDataType) => {
      await axios({
        method: method,
        url: action,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        withCredentials: withCredentials,
        data: {
          ...formData
        }
      })
        .then((response) => {
          if (response.status === 200) {
            // eslint-disable-next-line @typescript-eslint/no-unused-expressions
            successMessage ? setStatus(MessageStatus.SUCCESS) : setStatus(MessageStatus.PENDING)
            if (redirectPage) {
              const successPageURL = redirectPage
              const redirectToTab = redirectInNewTab ? '_blank' : '_self'
              window.open(successPageURL, redirectToTab)
            }
          }
        })
        .catch(() => {
          setStatus(MessageStatus.ERROR)
          setError(technicalErrormessage)
        })
    }
    sendData(formData)
  }
  const handleKeyDown = (e: KeyboardEvent<HTMLFormElement>) => {
    const { target } = e
    if (target instanceof HTMLElement) {
      if (target.nodeName === 'INPUT') {
        const keyValue = e.which || e.keyCode || 0
        if (keyValue === 13) {
          e.preventDefault()
          submitForm(e)
        }
      }
    }
  }
  const getBanner = () => {
    return (
      <div className={`status-banner`} ref={bannerMessageRef} tabIndex={0}>
        <Banner
          bannerType={BannerType.DYNAMIC}
          variation={status === MessageStatus.SUCCESS ? BannerVariaion.SUCCESS : BannerVariaion.ERROR}
          message={status === MessageStatus.SUCCESS ? successMessage : error}
          iconPath={status === MessageStatus.SUCCESS ? successIcon : errorIcon}
          altIcon={status === MessageStatus.SUCCESS ? successIconAlt : errorIconAlt}
          linkUrl={status === MessageStatus.SUCCESS ? successMessageLink : errorMessageLink}
          linkText={status === MessageStatus.SUCCESS ? successMessageLinkText : errorMessageLinkText}
          openInNewTab={linkInNewTab}
          linkId={status === MessageStatus.SUCCESS ? successLinkId : errorLinkId}
          linkType={status === MessageStatus.SUCCESS ? successLinkType : errorLinkType}
          id={`${id}-banner`}
        />
      </div>
    )
  }
  return (
    <div
      className={`form-container ${isFormAlignLeft ? 'fields-align-left' : 'fields-align-center '} ${cssClass}`}
      data-testid={`${id}-container`}
    >
      {formHeading && (
        <div className={`form-main-heading ${isFormAlignLeft ? 'align-items-left' : 'align-items-center'}`}>
          <SectionHeader
            rootClass='form-heading'
            eyebrow={{
              value: eyebrow,
              additionalClass: 'eyebrow',
              isRTE: true
            }}
            title={{ value: heading, additionalClass: 'heading', isRTE: true }}
            description={{
              value: subheading,
              additionalClass: 'subheading'
            }}
            rootDataTestId={`${id}-section-header`}
          />
        </div>
      )}
      <form action={action} method='post' onSubmit={submitForm} onKeyDown={handleKeyDown} id={id} noValidate>
        {showBanner && statusMessagePosition == FormStatusPosition.TOP && getBanner()}
        <FormProvider value={{ formContext: state, setFormContext: dispatchFormContext }}>
          <div className='form-fields'>{children}</div>
          <div className={`form-footer ${isFormAlignLeft && 'align-items-left'}`}>
            {showBanner && statusMessagePosition == FormStatusPosition.BOTTOM && getBanner()}
            <ButtonGroup id={`form-buttons${id && '-' + id}`} buttons={updatedFormButtons}></ButtonGroup>
            <RichText text={disclaimer} className='disclaimer' id={`${id}-disclaimer`} />
          </div>
        </FormProvider>
      </form>
    </div>
  )
}
export default FormContainer
