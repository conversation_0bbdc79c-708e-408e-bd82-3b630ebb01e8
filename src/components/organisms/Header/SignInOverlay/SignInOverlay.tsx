import React, { useEffect, useState } from 'react'

import './SignInOverlay.scss'
import { LoginStatus, SignInOverlayProps } from './types'
import { GLOBAL_PROFILE } from '../../../../assets/images'
import { getLogoutUrl, ORIGIN } from '../../../../constants'
import { useHeaderProfileContext } from '../../../../contexts/HeaderProfileContext'
import useClickAwayListener from '../../../../hooks/useClickAwayListener'
import useLobExperience from '../../../../hooks/useLobExperience'
import { getSignInProfileMenu } from '../../../../services'
import { getPrimaryAndSecondaryMenuItems, replaceColon } from '../../../../utils/helper-util'
import { getImageSrc } from '../../../../utils/local-util'
import { NavbarLinkType } from '../../../molecules/GlobalNav'

const SignInOverlay = (props: SignInOverlayProps): React.JSX.Element => {
  const {
    loginUser,
    username,
    signMenuItems,
    signInUrl,
    signInAltTxt = '',
    signInIcon = GLOBAL_PROFILE,
    rightNavSelectedIndex,
    setRightNavSelectedIndex,
    setShow
  } = props
  const { isBusiness } = useLobExperience()
  const modifiedSigninUrl = signInUrl ? signInUrl.replace('$currentUrl', encodeURI(window.location.href)) : ''
  useEffect(() => {
    if (rightNavSelectedIndex !== 4) {
      setSignInIsActive(false)
    }
  }, [rightNavSelectedIndex])

  const handleClickAway = () => {
    setSignInIsActive(false)
  }
  const signRef = useClickAwayListener(handleClickAway)

  const [signInMenuItems, setSignInMenuItems] = useState(signMenuItems)
  const { primaryMenuOptions, secondaryMenuOptions } = getPrimaryAndSecondaryMenuItems(signInMenuItems)

  const [signInIsActive, setSignInIsActive] = useState(false)
  const { headerProfileRes } = useHeaderProfileContext()
  const loginStatus = headerProfileRes?.['loginStatus'] ?? 'none'
  const loginID = headerProfileRes?.['loginID'] ?? ''
  const transactionalID = headerProfileRes?.['transaction_id'] || new Date().valueOf()
  const loggedIn = loginStatus === LoginStatus.FULL

  const getSignInProfileMenuDataForBusi = async () => {
    try {
      const response = await getSignInProfileMenu({ loginID: loginID, transactionalID: transactionalID })
      if (response) {
        const linksViaAPI: any[] = []

        response.navigationGroupList.forEach((group: any) => {
          linksViaAPI.push(group.navigationSectionsList)

          group.navigationSectionsList.forEach((section: any) => {
            if (section.navigationItems) {
              linksViaAPI.push(section.navigationItems)
            }
          })
        })
        const enabledLinksViaAPI = linksViaAPI.flat().filter((link) => link.enabled)

        const links_Busi = signMenuItems.filter((menuItem: any) =>
          enabledLinksViaAPI.some((link: any) =>
            menuItem.isDynamicLink
              ? menuItem.dynamicLinkId === link.id
              : menuItem.linkText.toLowerCase() === link.name.toLowerCase()
          )
        )
        setSignInMenuItems(links_Busi)
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      setSignInMenuItems([])
    }
  }
  useEffect(() => {
    if (isBusiness && loggedIn) {
      getSignInProfileMenuDataForBusi()
    }
  }, [loggedIn])

  const handleProfileIconClick = (
    event: React.MouseEvent<HTMLAnchorElement, MouseEvent> | React.TouchEvent<HTMLAnchorElement>
  ) => {
    setShow(false)
    if (loginStatus == LoginStatus.FULL) {
      event.preventDefault()
      setSignInIsActive(!signInIsActive)
      if (rightNavSelectedIndex !== 4) {
        setRightNavSelectedIndex(4)
      } else {
        setRightNavSelectedIndex(0)
      }
    } else {
      window.location.href = modifiedSigninUrl
    }
  }

  const getLinkUrl = (linkUrl: string) => {
    if (linkUrl?.includes('logout')) {
      const newCurrentUrl = getLogoutUrl(ORIGIN, linkUrl, isBusiness)
      return newCurrentUrl
    }
    return replaceColon(linkUrl)
  }

  const getMenuOptions = (menuOptions: Array<NavbarLinkType>, id: string) => {
    if (menuOptions.length > 0) {
      return (
        <ul className={`${id}-menu-options`}>
          {menuOptions.map((menuItem: NavbarLinkType, index: number) => {
            const { linkId, linkUrl = '', linkText } = menuItem
            return (
              <>
                <li key={index}>
                  <a href={getLinkUrl(linkUrl)} id={linkId} className={`menu-link ${id}`} role='button' target={'_self'}>
                    <span className='link-label'>{linkText}</span>
                  </a>
                </li>
              </>
            )
          })}
        </ul>
      )
    }
  }

  return (
    <div data-testid={`sign-in-overlay-container`} className={`sign-in-overlay-container`}>
      <ul className='main-nav-primary-links' ref={signRef}>
        <li className={`cox-parent-menu-left-item ${isBusiness && 'business'}`}>
          <a
            href={loggedIn ? 'javascript:void(0)' : `${modifiedSigninUrl}`}
            className={`cox-menu-link icon-link ${loggedIn && 'reveal-caret'}  ${signInIsActive && 'active'}`}
            onClick={(event) => handleProfileIconClick(event)}
            role='button'
          >
            <img src={getImageSrc(signInIcon)} className='icon-menu-item' aria-label={signInAltTxt} alt={signInAltTxt} />
            {isBusiness && (
              <div className='salutation-text'>
                <span>{loginUser}</span>
                <span>MyAccount</span>
              </div>
            )}
          </a>
          {signInIsActive && loggedIn && (
            <div className='cox-menu-list'>
              <div className='salutation-container'>
                <span>Hi{username !== '' && <span>, {username}</span>}!</span>
              </div>
              <div className='trim-bottom'>
                {getMenuOptions(primaryMenuOptions, 'primary')}
                {getMenuOptions(secondaryMenuOptions, 'secondary')}
              </div>
            </div>
          )}
        </li>
      </ul>
    </div>
  )
}

export default SignInOverlay
