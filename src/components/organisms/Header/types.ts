export interface HeaderProps {
  lob?: string
  logo?: string
  logoAltTxt?: string
  navigationdetails: string
  timesCircle?: string
  timesCircleWhite?: string
  globalNavMenu?: string
  arrowDownLargeBlack?: string
  chevronUpBlack?: string
  geoLocationDetails: string
  displayContactUs: string
  displayLocation: string
  locationPin?: string
  geoError?: string
  search?: string
  searchAltTxt?: string
  displaySignInOut: string
  signInUrl: string
  signIn?: string
  signInAltTxt?: string
  bellnotification?: string
  bellNotificationAltTxt?: string
  notificationViewAllUrl?: string
  shoppingCart?: string
  shoppingCartAltTxt?: string
  businessLinks?: any[]
  residentialLinks?: any[]
  className?: string
  displayShoppingCart: string
  shoppingCartUrl: string
  minimalHeader?: boolean
}

export interface MenuItem {
  addHRAfter: boolean
  addPromoContainer: boolean
  containsSubNav: boolean
  dontShowInMobile: boolean
  dynamicLinkId: string
  isDynamicLink: boolean
  linkDescription: string
  linkId: string
  linkText: string
  linkUrl: string
  showOnlyInMobile: boolean
  showOnlyInSignIn: boolean
  showOnlyInShoppingCart: boolean
  startSecondarySection: boolean
  useExactLink: boolean
  workassignout: boolean
  showForProspect?: boolean
  showForCustomer?: boolean
}

export enum LoginStatus {
  FULL = 'full',
  PARTIAL = 'partial',
  NONE = 'none'
}
