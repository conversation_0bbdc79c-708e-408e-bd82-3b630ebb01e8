import './Header.scss'

import { useEffect, useState } from 'react'

import styled from 'styled-components'

import BellOverlay from './BellOverlay'
import Header<PERSON>ogo from './HeaderLogo'
import SearchOverlay from './SearchOverlay'
import ShoppingCartOverlay from './ShoppingCartOverlay'
import SignInOverlay from './SignInOverlay'
import TopLinks from './TopLinks'
import { HeaderProps, LoginStatus, MenuItem } from './types'
import { HEADER_OVERLAY_ZINDEX, isLocalHost, MOTION_POINT_EASY_LINK, MOTION_POINT_SNIPPET_URL } from '../../../constants'
import { useHeaderProfileContext } from '../../../contexts/HeaderProfileContext'
import { useLocationContext } from '../../../contexts/LocationContext'
import useLobExperience from '../../../hooks/useLobExperience'
import { getHeaderProfile } from '../../../services'
import { convertStringToArray } from '../../../utils/helper-util'
import { loadScript } from '../../../utils/utag-util'
import GlobalNav from '../../molecules/GlobalNav'
import Overlay from '../../shared/Overlay'
import MinimalHeader from '../MinimalHeader/MinimalHeader'

const Header = (props: HeaderProps) => {
  const [rightNavSelectedIndex, setRightNavSelectedIndex] = useState(0)
  const [applyOverlay, setApplyOverlay] = useState(false)
  const [show, setShow] = useState(false)

  const {
    logo,
    logoAltTxt = 'Cox Homepage',
    navigationdetails,
    timesCircle,
    timesCircleWhite,
    globalNavMenu,
    arrowDownLargeBlack,
    chevronUpBlack,
    displayContactUs,
    displayLocation,
    locationPin,
    geoError,
    search,
    searchAltTxt = 'Search',
    displaySignInOut,
    signInUrl,
    geoLocationDetails,
    signIn,
    signInAltTxt = 'Profile',
    notificationViewAllUrl,
    bellnotification,
    bellNotificationAltTxt = 'Notifications',
    shoppingCart,
    shoppingCartAltTxt = 'Shopping Cart',
    className = '',
    displayShoppingCart = 'false',
    shoppingCartUrl,
    minimalHeader
  } = props

  const menuItems = convertStringToArray(navigationdetails)
  const signMenuItems = menuItems.filter((menuItem: MenuItem) => menuItem.showOnlyInSignIn === true)
  const leftNavItems = JSON.stringify(
    menuItems.filter((menuItem: MenuItem) => menuItem.showOnlyInSignIn !== true && menuItem.showOnlyInShoppingCart !== true)
  )
  const { setGeoLocationDetails } = useLocationContext()
  const { headerProfileRes, setHeaderProfileRes } = useHeaderProfileContext()
  const loginStatus = headerProfileRes?.['loginStatus'] ?? 'none'

  const getLoginUserDetails = () => {
    const loginUser = headerProfileRes?.['loginUser']

    if (loginUser) {
      if (loginStatus === LoginStatus.FULL) {
        return `Hi, ${loginUser}`
      } else if (loginStatus === LoginStatus.PARTIAL) {
        return `Sign In, ${loginUser}`
      }
    }

    return 'Sign In'
  }

  const isLoggedIn = loginStatus !== LoginStatus.NONE
  const locationType = headerProfileRes?.['locationType'] ?? ''
  const locationText = headerProfileRes?.['locationText'] || ''
  const zipcode = headerProfileRes?.['zipcode'] || ''
  const username = headerProfileRes?.['loginUser'] ?? ''
  const hasFullLoginStatus = loginStatus == LoginStatus.FULL
  const { isBusiness } = useLobExperience()

  useEffect(() => {
    if (!minimalHeader) {
      setGeoLocationDetails(geoLocationDetails && JSON.parse(geoLocationDetails))
    }
  }, [])

  useEffect(() => {
    const getHeaderProfileData = async () => {
      try {
        const response = await getHeaderProfile()
        if (response) {
          setHeaderProfileRes(response)
        }
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        setHeaderProfileRes({})
      }
    }
    if (!minimalHeader) {
      getHeaderProfileData()
    }
  }, [])

  useEffect(() => {
    if (applyOverlay) {
      setRightNavSelectedIndex(0)
    }
  }, [applyOverlay])

  useEffect(() => {
    loadScript({ url: MOTION_POINT_SNIPPET_URL, id: 'mp_snippet', isAsync: false })
    if (!isLocalHost()) {
      //@ts-expect-error Element implicitly has 'any' type
      document.querySelector('#mp_snippet').addEventListener('load', function () {
        loadScript({ url: MOTION_POINT_EASY_LINK, id: 'mpelid' })
      })
    }
  }, [])

  if (minimalHeader) {
    return <MinimalHeader />
  }

  const hideOverlay = () => {
    setRightNavSelectedIndex(0)
    setShow(false)
  }
  const handleRightNavMenuSelection = (selectedIndex: number) => {
    setRightNavSelectedIndex(selectedIndex)
  }

  return (
    <>
      <div className={`header-container ${className}`}>
        <div id='top-skip-navigation'>
          <a href='#top-header-container' className='top-skip-nav' aria-label='Skip to Main Content'>
            Skip to Main Content
          </a>
        </div>
        <div id='top-header-container' className='top-header-bar-container'>
          <TopLinks
            displayContactUs={displayContactUs}
            displayLocation={displayLocation}
            locationType={locationType}
            locationText={locationText}
            zipCode={zipcode}
            locationPin={locationPin}
            geoErrorIcon={geoError}
          />
        </div>
        <div className='bottom-header-bar-container'>
          <div className='bottom-header-bar'>
            <div className='link-container'>
              <div className='main-left-nav'>
                <div className='item-2 mobile'>
                  <GlobalNav
                    navigationdetails={leftNavItems}
                    setRightNavSelectedIndex={setRightNavSelectedIndex}
                    globalNavMenu={globalNavMenu}
                    timesCircle={timesCircle}
                    timesCircleWhite={timesCircleWhite}
                  />
                </div>
                <HeaderLogo logo={logo} logoAltTxt={logoAltTxt} />
                <div className='item-2 desktop'>
                  <GlobalNav
                    navigationdetails={leftNavItems}
                    setApplyOverlay={setApplyOverlay}
                    globalNavMenu={globalNavMenu}
                    timesCircle={timesCircle}
                    timesCircleWhite={timesCircleWhite}
                    arrowDownLargeBlack={arrowDownLargeBlack}
                    chevronUpBlack={chevronUpBlack}
                  />
                </div>
              </div>
              <div className={`main-right-nav ${isBusiness && 'business'}`}>
                <StyledContainer active={rightNavSelectedIndex === 1}>
                  <SearchOverlay
                    rightNavSelectedIndex={rightNavSelectedIndex}
                    setRightNavSelectedIndex={handleRightNavMenuSelection}
                    isLoggedIn={isLoggedIn}
                    searchIcon={search}
                    searchAltTxt={searchAltTxt}
                    show={show}
                    setShow={setShow}
                  />
                </StyledContainer>
                {isLoggedIn && !isBusiness && (
                  <StyledContainer active={rightNavSelectedIndex === 2}>
                    <BellOverlay
                      notificationViewAllUrl={notificationViewAllUrl}
                      hasFullLoginStatus={hasFullLoginStatus}
                      rightNavSelectedIndex={rightNavSelectedIndex}
                      bellNotificationAltTxt={bellNotificationAltTxt}
                      setRightNavSelectedIndex={handleRightNavMenuSelection}
                      setShow={setShow}
                      //icons
                      bellnotificationIcon={bellnotification}
                    />
                  </StyledContainer>
                )}
                {displayShoppingCart === 'true' && (
                  <StyledContainer active={rightNavSelectedIndex === 3}>
                    <ShoppingCartOverlay
                      rightNavSelectedIndex={rightNavSelectedIndex}
                      shoppingCart={shoppingCart}
                      shoppingCartAltTxt={shoppingCartAltTxt}
                      setShow={setShow}
                      shoppingCartUrl={shoppingCartUrl}
                    />
                  </StyledContainer>
                )}
                <StyledContainer active={rightNavSelectedIndex === 4}>
                  {displaySignInOut && (
                    <SignInOverlay
                      rightNavSelectedIndex={rightNavSelectedIndex}
                      setRightNavSelectedIndex={handleRightNavMenuSelection}
                      loginUser={getLoginUserDetails()}
                      username={username}
                      signMenuItems={signMenuItems}
                      signInAltTxt={signInAltTxt}
                      signInUrl={signInUrl}
                      setShow={setShow}
                      //icons
                      signInIcon={signIn}
                    />
                  )}
                </StyledContainer>
              </div>
            </div>
          </div>
          <div className='bottom-gradient'></div>
        </div>
      </div>
      <Overlay
        active={rightNavSelectedIndex > 0 || applyOverlay}
        onClick={() => hideOverlay()}
        zIndex={HEADER_OVERLAY_ZINDEX}
        backgroundColor='rgba(0, 0, 0, 0.65)'
      />
    </>
  )
}

const Container = styled.div`
  width: 40px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;

  @media(min-width: 992px){
  width: 48px;
  }
}
`

type OverlayProps = {
  active: boolean
}

const StyledContainer = styled(Container)<OverlayProps>`
  background-color: ${(props) => props.active && `#ebeff0`};
`

export default Header
