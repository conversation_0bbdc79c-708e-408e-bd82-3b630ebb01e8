@import '../../../../assets/styles/v3/globalStyles.scss';

.bell-overlay-container {
  .main-nav-primary-links {
    list-style: none;
    margin-bottom: 0;
    padding-left: 0rem;

    .cox-parent-menu-left-item {
      position: relative;

      .header-bell-notification {
        cursor: pointer;

        img[aria-label='bell'] {
          width: 24px;
          height: 24px;
        }

        .chevron-icon {
          width: 15px;
          height: 20px;
          transform: rotate(180deg);
          margin-top: 5px;

          &.bell-section-active {
            transform: rotate(0deg);
          }
        }
      }

      .bell-overlay {
        @media (max-width: $md) {
          width: 100%;
          position: fixed;
          margin-left: 0px;
          padding-left: 0px;
          padding-top: 0px;
          right: 0px !important;
        }

        margin-top: 8px !important;
        margin-left: -400px;
        position: absolute;
        z-index: $zindex-header-footer-menu;
        right: -70px;
        padding-top: 10px;
        padding-left: 0px;
        width: 300px;
        max-width: none;
      }
    }
  }
}
