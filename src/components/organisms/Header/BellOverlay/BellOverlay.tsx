import React, { useEffect, useState } from 'react'

import { BellOverlayProps } from './types'
import { BELL } from '../../../../assets/images'
import './BellOverlay.scss'
import { getBellNotifications } from '../../../../services'
import { getImageSrc } from '../../../../utils/local-util'
import NotificationBadge from '../../../atoms/NotificationBadge'
import BellNotification from '../BellNotification'
import { BellAlertsResponse } from '../BellNotification/types'

const BellOverlay = (props: BellOverlayProps): React.JSX.Element => {
  const {
    notificationViewAllUrl,
    hasFullLoginStatus,
    bellnotificationIcon = BELL,
    bellNotificationAltTxt = '',
    rightNavSelectedIndex,
    setRightNavSelectedIndex,
    setShow
  } = props

  const [displayBellOverlay, setDisplayBellOverlay] = useState(false)
  const [userAlerts, setUserAlerts] = useState([] as BellAlertsResponse)
  const [totalBellAlerts, setTotalBellAlerts] = useState(0)

  const getBellNotification = () => {
    getBellNotifications()
      .then((data) => {
        const { totalAlerts = 0 } = data as BellAlertsResponse
        setTotalBellAlerts(totalAlerts)
        setUserAlerts(data)
      })
      .catch((error: Error) => {
        console.error('Bell notification error: ', error)
        setTotalBellAlerts(0)
        setUserAlerts([] as BellAlertsResponse)
      })
  }

  const updateBellCount = (count = 0) => {
    setTotalBellAlerts(count)
  }

  useEffect(() => {
    if (hasFullLoginStatus) {
      getBellNotification()
    }
  }, [hasFullLoginStatus])

  useEffect(() => {
    if (rightNavSelectedIndex !== 2) {
      setDisplayBellOverlay(false)
    }
  }, [rightNavSelectedIndex])

  const handleBellIconClick = () => {
    setShow(false)
    if (rightNavSelectedIndex !== 2) {
      setRightNavSelectedIndex(2)
    } else {
      setRightNavSelectedIndex(0)
    }
    setDisplayBellOverlay(!displayBellOverlay)
  }

  return (
    <div data-testid={`bell-overlay-container`} className={`bell-overlay-container`}>
      <ul className='main-nav-primary-links'>
        <li className={`cox-parent-menu-left-item`}>
          <a
            className={`header-bell-notification icon-link ${displayBellOverlay && 'active'}`}
            onClick={() => handleBellIconClick()}
          >
            <img src={getImageSrc(bellnotificationIcon)} aria-label={bellNotificationAltTxt} alt={bellNotificationAltTxt} />
            {hasFullLoginStatus && <NotificationBadge count={totalBellAlerts} showCount={true} standalone={false} />}
          </a>
          {displayBellOverlay && (
            <div className='bell-overlay'>
              <BellNotification
                notificationDetails={userAlerts}
                displayType='show'
                notificationViewAllUrl={notificationViewAllUrl}
                updateBellCount={updateBellCount}
              ></BellNotification>
            </div>
          )}
        </li>
      </ul>
    </div>
  )
}
export default BellOverlay
