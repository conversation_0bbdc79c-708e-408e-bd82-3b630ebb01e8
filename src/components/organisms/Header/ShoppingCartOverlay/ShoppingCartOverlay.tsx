import './ShoppingCartOverlay.scss'
import Cookies from 'js-cookie'

import { ShoppingCartOverlayProps } from './types'
import { CART_SKY_BLUE } from '../../../../assets/images'
import { getImageSrc } from '../../../../utils/local-util'
import NotificationBadge from '../../../atoms/NotificationBadge'

const ShoppingCartOverlay = (props: ShoppingCartOverlayProps): React.JSX.Element => {
  const { shoppingCart = CART_SKY_BLUE, shoppingCartAltTxt = '', setShow, shoppingCartUrl } = props

  const cartCookie = Cookies.get('cox-active-cart') ?? 'false'

  return (
    <div data-testid={`shopping-cart-overlay-container`} className={`shopping-cart-overlay-container`}>
      <ul className='main-nav-primary-links'>
        <li className={`cox-parent-menu-left-item`}>
          <a href={shoppingCartUrl} role='button' className='cox-menu-link' target={'_self'} onClick={() => setShow(false)}>
            {cartCookie === 'true' && <NotificationBadge standalone={false} />}
            <img
              src={getImageSrc(shoppingCart)}
              aria-label={shoppingCartAltTxt}
              alt={shoppingCartAltTxt}
              className='icon-menu-item'
            />
          </a>
        </li>
      </ul>
    </div>
  )
}

export default ShoppingCartOverlay
