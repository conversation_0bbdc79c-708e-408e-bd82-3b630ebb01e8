// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Header component Matches DOM Snapshot 1`] = `
[
  <div
    className="header-container "
  >
    <div
      id="top-skip-navigation"
    >
      <a
        aria-label="Skip to Main Content"
        className="top-skip-nav"
        href="#top-header-container"
      >
        Skip to Main Content
      </a>
    </div>
    <div
      className="top-header-bar-container"
      id="top-header-container"
    >
      <div
        className="top-header-bar"
        data-testid="top-links-container"
      >
        <div
          className="link-container"
        >
          <div
            className="links-block"
          />
          <div
            className="links-block"
          >
            <div
              className="geo-location residential"
            >
              <div
                className="geo-location-flex-container"
              >
                <div
                  className="location-container"
                >
                  <img
                    alt="location-icon"
                    className="location-icon"
                    src="/content/dam/cox/common/icons/ui_components/location.svg"
                  />
                  <a
                    aria-label=" , Current location is "
                    className="location-text"
                    href="#"
                    onClick={[Function]}
                    role="button"
                  >
                    <span
                      className="location-name-text "
                    >
                      Select a Location
                    </span>
                  </a>
                </div>
                <div
                  className="geolocation-overlay display-none"
                  data-testid="geolocation-overlay"
                >
                  <div
                    className="close-btn-container"
                  >
                    <a
                      href="#"
                      onClick={[Function]}
                      role="button"
                      title="Close Location Selection"
                    >
                      <img
                        alt="close-icon"
                        className="close-btn"
                        src="/content/dam/cox/common/icons/ui_components/close-btn.svg"
                      />
                    </a>
                  </div>
                  <div
                    className="location-form"
                  >
                    <div
                      className="location-panel-mid-section"
                    >
                      <form
                        action="https://test.cox.com/dispatch/7592854197940506870/intercept.cox?lob=residential&s=pf"
                        id="GeolocationForm"
                        method="post"
                        onSubmit={[Function]}
                      >
                        <input
                          name="dest"
                          type="hidden"
                          value="https://test.cox.com/webapi/cdncache/cookieset?resource=http://localhost:3000/"
                        />
                        <div
                          className="form-container"
                        >
                          <div
                            className="form-section-1"
                          >
                            <input
                              className=""
                              id="zipcode"
                              maxLength={5}
                              name="zipcode"
                              onChange={[Function]}
                              placeholder="Zip"
                              title="Zip Code"
                              type="tel"
                              value=""
                            />
                            <input
                              className="location-panel-submit-form"
                              type="submit"
                              value=""
                            />
                          </div>
                          <div
                            className="form-section-2"
                          >
                            <span
                              className="location-panel-or"
                            >
                              OR
                            </span>
                            <span
                              className="location-panel-or-divider"
                            />
                          </div>
                          <div
                            className="form-section-3"
                          >
                            <input
                              name="state"
                              type="hidden"
                            />
                            <div
                              className="select-container dropdown"
                            >
                              <button
                                aria-expanded={false}
                                className="dropdown-toggle dropdown-toggle-split btn btn-primary"
                                disabled={false}
                                id="state"
                                onClick={[Function]}
                                type="button"
                              >
                                <div
                                  className="toggle-text"
                                >
                                  Choose a state
                                </div>
                                <div
                                  className="select-icon"
                                  onClick={[Function]}
                                >
                                  <img
                                    alt="chevron-icon"
                                    src="/content/dam/cox/common/icons/ui_components/chevron-down-blue.svg"
                                  />
                                </div>
                              </button>
                            </div>
                            <input
                              name="city"
                              type="hidden"
                            />
                          </div>
                        </div>
                      </form>
                    </div>
                    <div
                      className="divider"
                    />
                    <div
                      className="location-links"
                    >
                      <p>
                        Already a Cox Residential customer?
                         
                        <a
                          href="https://www.cox.com/content/dam/cox/okta/signin.html?onsuccess=https%3A%2F%2Fwww.cox.com%2Fwebapi%2Fcdncache%2Fcookieset%3Fresource%3Dhttps%3A%2F%2Fwww.cox.com%2Fresaccount%2Fhome.html"
                        >
                          Sign in
                        </a>
                      </p>
                      <p>
                        Looking for Business service?
                         
                        <a
                          href="https://www.cox.com/business/home.html"
                        >
                          Go to Cox Business
                        </a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      className="bottom-header-bar-container"
    >
      <div
        className="bottom-header-bar"
      >
        <div
          className="link-container"
        >
          <div
            className="main-left-nav"
          >
            <div
              className="item-2 mobile"
            >
              <div
                className="global-nav-container "
                data-testid="main-nav-container"
              >
                <a
                  aria-label="Menu Icon"
                  className="menu-icon"
                  data-cox-menu-name="navigation"
                  href="#"
                  onClick={[Function]}
                  role="link"
                  title="Menu Icon"
                >
                  <img
                    alt="hamburger-menu"
                    src="/content/dam/cox/common/icons/ui_components/hamburger-menu.svg"
                  />
                   
                </a>
                <div
                  className="desktop-global-nav-container"
                  data-testid="desktop-global-nav-container"
                />
              </div>
            </div>
            <div
              className="logo-block"
            >
              <a
                href="/residential/home.html"
              >
                <img
                  alt="Cox Homepage"
                  aria-label="Cox Homepage"
                  className="cox-logo-resi"
                  src="/content/dam/cox/common/icons/ui_components/cox_logo.png"
                />
              </a>
            </div>
            <div
              className="item-2 desktop"
            >
              <div
                className="global-nav-container "
                data-testid="main-nav-container"
              >
                <a
                  aria-label="Menu Icon"
                  className="menu-icon"
                  data-cox-menu-name="navigation"
                  href="#"
                  onClick={[Function]}
                  role="link"
                  title="Menu Icon"
                >
                  <img
                    alt="hamburger-menu"
                    src="/content/dam/cox/common/icons/ui_components/hamburger-menu.svg"
                  />
                   
                </a>
                <div
                  className="desktop-global-nav-container"
                  data-testid="desktop-global-nav-container"
                />
              </div>
            </div>
          </div>
          <div
            className="main-right-nav false"
          >
            <div
              className="sc-gzVnrw sc-htoDjs jgdcvd"
            >
              <div
                className="search-overlay-container"
                data-testid="search-overlay-container"
              >
                <ul
                  className="main-nav-primary-links"
                >
                  <li
                    className="cox-parent-menu-left-item"
                  >
                    <a
                      href="#"
                      onClick={[Function]}
                      role="button"
                    >
                      <img
                        alt="Search"
                        aria-label="Search"
                        src="/content/dam/cox/common/icons/ui_components/magnifying-glass.svg"
                      />
                    </a>
                    <div
                      className="header-search-bar  no-bell-icon"
                    >
                      <div
                        className="header-search-bar-fill"
                      />
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <div
              className="sc-gzVnrw sc-htoDjs jgdcvd"
            />
          </div>
        </div>
      </div>
      <div
        className="bottom-gradient"
      />
    </div>
  </div>,
  <div
    className="sc-bZQynM dbUlgm"
    onClick={[Function]}
  />,
]
`;
