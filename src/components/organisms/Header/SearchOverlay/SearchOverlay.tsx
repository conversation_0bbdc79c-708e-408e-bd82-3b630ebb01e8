import './SearchOverlay.scss'

import { useEffect } from 'react'

import { SearchOverlayProps } from './types'
import { GLOBAL_MAGNIFYING_GLASS } from '../../../../assets/images'
import { getImageSrc } from '../../../../utils/local-util'
import YextSearch, { SearchStyleStrings } from '../../../atoms/YextSearch'
import Slide from '../Slide'

const SearchOverlay = (props: SearchOverlayProps): React.JSX.Element => {
  const {
    rightNavSelectedIndex,
    setRightNavSelectedIndex,
    searchIcon = GLOBAL_MAGNIFYING_GLASS,
    searchAltTxt = '',
    isLoggedIn,
    show,
    setShow
  } = props

  useEffect(() => {
    if (rightNavSelectedIndex == 0) {
      setShow(false)
    }
  }, [rightNavSelectedIndex])

  const handleSearchIconClick = (e: any) => {
    e.preventDefault()
    if (rightNavSelectedIndex !== 1) {
      setRightNavSelectedIndex(1)
      setShow(true)
    } else {
      setRightNavSelectedIndex(0)
      setShow(false)
    }
  }

  return (
    <div data-testid={`search-overlay-container`} className={`search-overlay-container`}>
      <ul className='main-nav-primary-links'>
        <li className={`cox-parent-menu-left-item`}>
          <a href='#' role='button' onClick={(e) => handleSearchIconClick(e)}>
            <img src={getImageSrc(searchIcon)} aria-label={searchAltTxt} alt={searchAltTxt} />
          </a>
          <div className={isLoggedIn ? 'header-search-bar' : 'header-search-bar  no-bell-icon'}>
            <div className='header-search-bar-fill'>
              <Slide show={show}>
                <YextSearch
                  id={'header-search-bar'}
                  searchstyle={SearchStyleStrings.WHITE}
                  searchmagnifyingglassicon={searchIcon}
                  handleCloseSearchInput={handleSearchIconClick}
                />
              </Slide>
            </div>
          </div>
        </li>
      </ul>
    </div>
  )
}

export default SearchOverlay
