import React from 'react'

import renderer from 'react-test-renderer'

import Header from './Header'

describe('Header component', () => {
  it('Matches DOM Snapshot', () => {
    const domTree = renderer
      .create(
        <Header
          navigationdetails={''}
          geoLocationDetails={''}
          displayContactUs={''}
          displaySignInOut={''}
          signInUrl={''}
          displayLocation={'true'}
          displayShoppingCart={''}
          shoppingCartUrl={''}
        />
      )
      .toJSON()
    expect(domTree).toMatchSnapshot()
  })
})
