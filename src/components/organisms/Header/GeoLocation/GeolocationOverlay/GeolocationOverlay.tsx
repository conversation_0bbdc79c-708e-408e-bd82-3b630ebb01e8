import { <PERSON><PERSON><PERSON>, MouseEvent, useEffect, useRef, useState } from 'react'

import './GeolocationOverlay.scss'

import { CHEVRON_DOWN_BLUE, ERROR_TRIANGLE, LOCATION_CLOSE } from '../../../../../assets/images'
import {
  COX_BUSI_SIGN_IN_URL,
  COX_BUSINESS_HOME_URL,
  COX_DOMAIN_URL,
  COX_RESI_HOME_URL,
  COX_RESIDENTIAL_SIGN_IN_URL,
  HREF,
  SERVICABLE_CITIES_API
} from '../../../../../constants'
import { useHeaderProfileContext } from '../../../../../contexts/HeaderProfileContext'
import useLobExperience from '../../../../../hooks/useLobExperience'
import { getGeoLocationStates } from '../../../../../services'
import { setLocalStorage } from '../../../../../utils/local-storage-util'
import RichText from '../../../../atoms/RichText'
import { RBDropdown } from '../../../../shared/ReactBootstrap'
import { CityObj, locationObj, StateObj } from '../../GeoLocation/GeolocationOverlay/types'
import { CityType, GeolocationOverlayProps, GeoLocationType } from '../../GeoLocation/types'
import { LoginStatus } from '../../types'

const GeolocationOverlay = (props: GeolocationOverlayProps) => {
  const {
    isOverlayActive,
    setLocationCallback,
    setGeolocationIsActive,
    isMobile = false,
    geoErrorIcon = ERROR_TRIANGLE,
    geoLocationConfig
  } = props

  const { lob, isBusiness } = useLobExperience()

  // Get locationType and zipCode from HeaderProfileContext:
  const { headerProfileRes } = useHeaderProfileContext()
  const { locationType = '', zipcode: contextZipCode = '', loginStatus = LoginStatus.NONE } = headerProfileRes

  // Initialize state with proper initial values and ensure it's never undefined
  const [zipcode, setZipcode] = useState(contextZipCode || '')
  const [selectedState, setSelectedState] = useState<StateObj | null>(null)
  const [selectedCity, setSelectedCity] = useState<CityObj | null>(null)
  const [actionUrl, setActionUrl] = useState('')
  const [destUrl, setDestUrl] = useState('')

  const [citiesOptions, setCitiesOptions] = useState<CityObj[]>([])
  const [zipcodeService, setZipcodeService] = useState({
    isServiceable: false,
    isInvalid: false,
    statusMessage: ''
  })

  const [displayCityDropdown, setDisplayCityDropdown] = useState(false)
  const [displayZipcodeRequiredErrorMessage, setDisplayZipcodeRequiredErrorMessage] = useState(false)
  const [displayInvalidZipcodeErrorMessage, setDisplayInvalidZipcodeErrorMessage] = useState(false)
  const [geoLocationStates, setGeoLocationStates] = useState<locationObj[]>([])

  const [isOpen, setIsOpen] = useState(false)

  const randomToken = Math.random().toString().replace('.', '')

  const {
    buttonLabel = 'Check This Area',
    cbOnlyMarket = '',
    invalid = '',
    nonserviceable = '',
    genericMsg = `Let us know the location you'd like to browse.`,
    errorScenario = '',
    chevronIcon = CHEVRON_DOWN_BLUE,
    chevronAlt = '',
    closeIcon = LOCATION_CLOSE,
    closeAlt = '',
    welcomeText = 'Welcome Back!'
  } = geoLocationConfig

  useEffect(() => {
    setActionUrl(`${COX_DOMAIN_URL}/dispatch/7592854197940506870/intercept.cox?lob=residential&s=pf`)
    setDestUrl(`${COX_DOMAIN_URL}/webapi/cdncache/cookieset?resource=${HREF}`)
  })
  useEffect(() => {
    // Only update zipcode if contextZipCode is defined and not empty
    if (contextZipCode) {
      setZipcode(contextZipCode)
    }
    setZipcodeService({
      isServiceable: locationType !== 'outOfMarket' && locationType !== '',
      isInvalid: locationType == 'invalid',
      statusMessage: getStatusMessage(locationType, contextZipCode)
    })
  }, [contextZipCode, locationType])

  useEffect(() => {
    getGeoLocationStates().then((data) => {
      setGeoLocationStates(data?.geoLocationStates || [])
    })
  }, [])

  const toggleDropdown = () => {
    setIsOpen(!isOpen)
  }

  const fetchServicableCities = async (state: string) => {
    const response = await fetch(SERVICABLE_CITIES_API + state + '?lob=' + lob + '&_=' + randomToken)
    const data = await response.json()
    const servicableCitiesList = data.servicable.locations
    setCitiesOptions(servicableCitiesList)
  }

  const handleStateChange = async (event: MouseEvent, geoState: StateObj) => {
    event.preventDefault()
    setZipcode('')
    setDisplayZipcodeRequiredErrorMessage(false)
    setDisplayInvalidZipcodeErrorMessage(false)
    setSelectedState(geoState)
    fetchServicableCities(geoState.abbreviation)
    setDisplayCityDropdown(true)
  }

  const handleCityChange = (event: MouseEvent, city: CityObj) => {
    event.preventDefault()
    setZipcode('')
    setSelectedCity(city)
    setLocationCallback?.(city.name + ', ' + selectedState?.abbreviation)
    handleSubmit()
  }

  /**
   * Handles zipcode input changes with validation and error state management.
   * This is better than a direct setState approach because it:
   * 1. Validates input to only allow numeric values using regex
   * 2. Manages multiple error states (invalid zipcode and required field)
   * 3. Provides immediate feedback by clearing error messages when valid input is entered
   *
   * @param e - React change event from the input element
   */
  const handleZipcodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.trim()
    // Only allow numeric input
    if (/^\d*$/.test(newValue)) {
      setZipcode(newValue)
      setDisplayInvalidZipcodeErrorMessage(false)
      if (newValue.length === 5) {
        setDisplayZipcodeRequiredErrorMessage(false)
      }
    }
  }

  const formRef = useRef<any>()
  async function handleSubmit() {
    setTimeout(() => {
      if (formRef) {
        formRef.current?.submit()
      }
    }, 300)
  }

  const closeGeolocationOverlay = (event: MouseEvent<HTMLAnchorElement>) => {
    event.preventDefault()
    setGeolocationIsActive?.(event, false)
  }

  const validateFormSubmit = (event: FormEvent<HTMLFormElement>) => {
    if (displayZipcodeRequiredErrorMessage && !selectedCity) {
      event.preventDefault()
    }
    if (!selectedCity && zipcode && zipcode.length !== 5) {
      setDisplayInvalidZipcodeErrorMessage(true)
      event.preventDefault()
    }
    setLocalStorage('geoLocationSearch', 'true')
  }

  const getStatusMessage = (type: string, zip: string) => {
    let message = ''
    switch (type) {
      case GeoLocationType.outOfMarket:
        message = nonserviceable
        break
      case GeoLocationType.cbOnlyMarket:
        message = cbOnlyMarket
        break
      case GeoLocationType.invalid:
        message = invalid
        break
      default:
        message = ''
        break
    }
    return message ? message.replace('[[zipcode]]', zip) : ''
  }

  const getLocationTexts = () => {
    return {
      signInText: `Already a Cox ${isBusiness ? 'Business' : 'Residential'} customer?`,
      serviceText: `Looking for ${isBusiness ? 'Residential' : 'Business'} service?`,
      homeURLText: `Go to Cox ${isBusiness ? 'Residential' : 'Business'}`
    }
  }

  return (
    <div
      data-testid='geolocation-overlay'
      className={'geolocation-overlay ' + (!isOverlayActive ? 'display-none' : '') + (isMobile ? ' mobile-view' : '')}
    >
      <div className='close-btn-container'>
        <a href='#' role='button' title='Close Location Selection' onClick={(event) => closeGeolocationOverlay(event)}>
          <img className='close-btn' src={closeIcon} alt={closeAlt} />
        </a>
      </div>
      <div className='location-form'>
        {(loginStatus == LoginStatus.PARTIAL || loginStatus == LoginStatus.FULL) && (
          <>
            <RichText text={welcomeText} className='welcome-text' />
            <div className='divider'></div>
          </>
        )}
        {zipcodeService.statusMessage && (
          <div className='location-info'>
            <div className='location-info-bold-paragraph'>
              {zipcodeService.statusMessage && <RichText text={zipcodeService.statusMessage} />}
            </div>
            <div className='divider'></div>
          </div>
        )}
        {!isMobile && <RichText className='generic-message' text={genericMsg} />}
        {isMobile && <p>Current location:</p>}
        {(displayZipcodeRequiredErrorMessage || displayInvalidZipcodeErrorMessage) && (
          <div id='location-form-status' className='form-error'>
            <img className='error-icon' src={geoErrorIcon} alt='error-icon' />
            <div className='error-text'>{errorScenario && <RichText text={errorScenario} />}</div>
          </div>
        )}
        <div className='location-panel-mid-section'>
          <form
            method='post'
            action={actionUrl}
            onSubmit={(e) => validateFormSubmit(e)}
            id={'GeolocationForm'}
            ref={formRef}
          >
            <input type='hidden' name='dest' value={destUrl} />
            <div className='form-container'>
              <div className='form-section-1'>
                <input
                  type='tel'
                  placeholder='Zip'
                  title='Zip Code'
                  name='zipcode'
                  id='zipcode'
                  maxLength={5}
                  value={zipcode}
                  onChange={handleZipcodeChange}
                  className={displayZipcodeRequiredErrorMessage || displayInvalidZipcodeErrorMessage ? 'error-bg' : ''}
                />
                {(displayZipcodeRequiredErrorMessage || displayInvalidZipcodeErrorMessage) && (
                  <div className='error-message'>
                    {displayZipcodeRequiredErrorMessage && (
                      <div className='error-container'>
                        <img className='error-icon' src={geoErrorIcon} alt='error-icon' />
                        <p className='error-text'>This field is required.</p>
                      </div>
                    )}
                    {displayInvalidZipcodeErrorMessage && (
                      <div className='error-container'>
                        <img className='error-icon' src={geoErrorIcon} alt='error-icon' />
                        <p className='error-text'>Please enter a valid Zip Code.</p>
                      </div>
                    )}
                  </div>
                )}
                <input type='submit' value={buttonLabel} className='location-panel-submit-form' />
              </div>
              <div className='form-section-2'>
                <span className='location-panel-or'>OR</span>
                <span className='location-panel-or-divider'></span>
              </div>
              <div className='form-section-3'>
                <input type='hidden' value={selectedState?.abbreviation} name='state' />
                <RBDropdown className='select-container'>
                  <RBDropdown.Toggle split id='state'>
                    <div className='toggle-text'>{selectedState?.name ?? 'Choose a state'}</div>
                    <div className='select-icon' onClick={toggleDropdown}>
                      <img src={chevronIcon} alt={chevronAlt} />
                    </div>
                  </RBDropdown.Toggle>

                  <RBDropdown.Menu>
                    {geoLocationStates.map((servicedState) => (
                      <RBDropdown.Item
                        key={servicedState.abbreviation}
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        // @ts-ignore
                        value={servicedState.abbreviation}
                        onClick={(event: any) => handleStateChange(event, servicedState)}
                      >
                        {servicedState.name}
                      </RBDropdown.Item>
                    ))}
                  </RBDropdown.Menu>
                </RBDropdown>
                <input type='hidden' value={selectedCity?.id} name='city' />
                {citiesOptions.length > 0 && (
                  <RBDropdown className={`select-container ${displayCityDropdown ? '' : 'hidden '}`}>
                    <RBDropdown.Toggle split id='state'>
                      <div className='toggle-text'>{selectedCity?.name ?? 'Choose a city'}</div>
                      <div className='select-icon' onClick={toggleDropdown}>
                        <img src={chevronIcon} alt={chevronAlt} />
                      </div>
                    </RBDropdown.Toggle>

                    <RBDropdown.Menu>
                      {citiesOptions.length > 0 &&
                        citiesOptions.map((city: CityType, index) => {
                          return (
                            <RBDropdown.Item
                              key={index}
                              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                              // @ts-ignore
                              value={city.id}
                              onClick={(event: any) => {
                                handleCityChange(event, city)
                              }}
                            >
                              {city.name}
                            </RBDropdown.Item>
                          )
                        })}
                    </RBDropdown.Menu>
                  </RBDropdown>
                )}
              </div>
            </div>
          </form>
        </div>
        <div className='divider'></div>
        <div className='location-links'>
          <p>
            {getLocationTexts().signInText}{' '}
            <a href={`${isBusiness ? COX_BUSI_SIGN_IN_URL : COX_RESIDENTIAL_SIGN_IN_URL}`}>Sign in</a>
          </p>
          <p>
            {getLocationTexts().serviceText}{' '}
            <a href={`${isBusiness ? COX_RESI_HOME_URL : COX_BUSINESS_HOME_URL}`}>{getLocationTexts().homeURLText}</a>
          </p>
        </div>
      </div>
    </div>
  )
}

export default GeolocationOverlay
