import { useState } from 'react'

import './Footer.scss'
import FooterNav from './FooterNav'
import { FooterProps } from './types'
import { CHEVRON_UP_BLACK } from '../../../assets/images'
import { FOOTER_OVERLAY_ZINDEX } from '../../../constants'
import RichText from '../../atoms/RichText'
import Overlay from '../../shared/Overlay'
import MinimalFooter from '../MinimalFooter'

const Footer = (props: FooterProps) => {
  const {
    navigationdetails,
    disclaimerText = '',
    chevron = CHEVRON_UP_BLACK,
    baseCssClass = '',
    className = '',
    minimalFooter
  } = props

  const [displayOverlay, setDisplayOverlay] = useState(false)
  if (minimalFooter) {
    return <MinimalFooter />
  }

  const setApplyOverlay = (isActive: boolean) => {
    setDisplayOverlay(isActive)
    const bodyElement = document.body
    if (isActive) {
      bodyElement.style.height = '100%'
      bodyElement.style.overflow = 'hidden'
    } else {
      bodyElement.style.overflow = 'visible'
    }
  }

  const footerNav = navigationdetails ? (
    <FooterNav navigationdetails={navigationdetails} footerIcon={chevron} setApplyOverlay={setApplyOverlay} />
  ) : (
    <></>
  )

  const copyRightText = ` © 1998 - ${new Date().getFullYear()} Cox Communications, Inc.`
  return (
    <>
      <div className={`footer mb-0 ${baseCssClass} ${className}`}>
        {footerNav}
        <RichText className='copyright-note' text={disclaimerText}></RichText>
        <RichText className='copyright-note' text={copyRightText}></RichText>
      </div>
      <Overlay active={displayOverlay} zIndex={FOOTER_OVERLAY_ZINDEX} backgroundColor='rgba(0, 0, 0, 0.65)' />
    </>
  )
}

export default Footer
