import React, { useState } from 'react'

import './FooterNav.scss'
import useClickAwayListener from '../../../../hooks/useClickAwayListener'
import useLobExperience from '../../../../hooks/useLobExperience'
import { replaceColon } from '../../../../utils/helper-util'
import { GlobalNavProps, NavbarLinkType } from '../../../molecules/GlobalNav/types'

const FooterNav = (props: GlobalNavProps): JSX.Element => {
  const { navigationdetails, footerIcon, setApplyOverlay } = props

  const { themeClass } = useLobExperience()

  const [selectedMenuId, setSelectedMenuId] = useState('')

  const convertStringToJSON = function (JSONString: string | undefined) {
    return JSONString && JSON.parse(JSONString)
  }

  const data = convertStringToJSON(navigationdetails)

  const handleMenuSelection = (event: React.MouseEvent<HTMLButtonElement>, menuId: string) => {
    event.preventDefault()
    if (selectedMenuId === menuId) {
      setSelectedMenuId('')
      if (setApplyOverlay) {
        setApplyOverlay(false)
      }
      return false
    }
    setSelectedMenuId(menuId)
    if (setApplyOverlay) {
      setApplyOverlay(true)
    }
  }

  const handleOutsideClick = () => {
    setSelectedMenuId('')
    if (setApplyOverlay) {
      setApplyOverlay(false)
    }
  }
  const refNav = useClickAwayListener(handleOutsideClick)

  return (
    <>
      <div ref={refNav} data-testid={`footer-nav-container`} className={`footer-nav-container ` + themeClass}>
        {data && data.length && (
          <nav aria-label='Footer' role='navigation'>
            <ul className='level-one'>
              {data.map(
                ({ linkId = '', linkText = '', linkUrl = '#', subItems = [], containsSubNav = false }: NavbarLinkType) => (
                  <li key={linkId} id={linkId} className={selectedMenuId === linkId ? 'expanded' : ''}>
                    {containsSubNav ? (
                      <>
                        <button
                          aria-expanded={selectedMenuId === linkId}
                          className='menu-button'
                          onClick={(event) => handleMenuSelection(event, linkId)}
                        >
                          {linkText}
                          <img src={footerIcon} className='chevron-icon' alt='' />
                        </button>
                        {subItems && subItems.length > 0 && (
                          <ul className='level-two'>
                            {subItems.map(({ linkId = '', linkText = '', linkUrl = '#' }: NavbarLinkType) => {
                              return (
                                <li key={linkId} id={linkId}>
                                  <a href={replaceColon(linkUrl)} aria-label={linkText} target={'_self'}>
                                    {linkText}
                                  </a>
                                </li>
                              )
                            })}
                          </ul>
                        )}
                      </>
                    ) : (
                      <a href={linkUrl} aria-label={linkText} target={'_self'}>
                        {linkText}
                      </a>
                    )}
                  </li>
                )
              )}
            </ul>
          </nav>
        )}
      </div>
    </>
  )
}

export default FooterNav
