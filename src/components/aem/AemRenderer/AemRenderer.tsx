import { AemRendererProps } from './types'
import { useAxios } from '../../../hooks/useAxios'
import CustomComponentRenderer from '../CustomComponentRenderer'

const AemRenderer = (props: AemRendererProps) => {
  const { url } = props
  const { response } = useAxios({
    url: url
  })
  const aemData = response?.[':items'].root?.[':items'].responsivegrid
  if (aemData && Object.keys(aemData).length > 0) {
    return aemData[':itemsOrder'].map((item: any) => {
      // eslint-disable-next-line react/jsx-key
      return <CustomComponentRenderer componentProps={aemData[':items'][item]} />
    })
  }
}

export default AemRenderer
