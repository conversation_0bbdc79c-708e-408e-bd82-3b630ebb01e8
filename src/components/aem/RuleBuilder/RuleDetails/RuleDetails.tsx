import './RuleDetails.scss'

const RuleDetails = ({
  isInEditor,
  ruleId,
  ruleType,
  ruleName,
  ruleDescription,
  ruleExpression,
  trueExpFragmentPath,
  falseExpFragmentPath,
  defaultVariationFragmentPath,
  showDefaultContent
}: {
  isInEditor: boolean
  ruleId: string
  ruleType: string
  ruleName: string
  ruleDescription: string
  ruleExpression: string
  trueExpFragmentPath: string
  falseExpFragmentPath: string
  defaultVariationFragmentPath?: string
  showDefaultContent?: boolean
}) => {
  if (isInEditor) {
    return (
      <div className='rule-details-container' data-testid='rule-details-container'>
        <div>
          {ruleId && (
            <>
              <b>Rule Id</b>
              <p>{ruleId}</p>
            </>
          )}
          {ruleName && (
            <>
              <b>Rule Name</b>
              <p>{ruleName}</p>
            </>
          )}
          {ruleDescription && (
            <>
              <b>Rule Description </b>
              <p>{ruleDescription}</p>
            </>
          )}
          {ruleExpression && (
            <>
              <b>Rule Expression</b>
              <p>{ruleExpression}</p>
            </>
          )}
        </div>

        <div>
          {ruleType && (
            <>
              <b>Rule Type</b>
              <p>{ruleType.toUpperCase()}</p>
            </>
          )}
          {trueExpFragmentPath && (
            <>
              <b>True Exp Fragment Path</b>
              <p>
                <a href={`/editor.html${trueExpFragmentPath}.html`} target='_blank' rel='noreferrer'>
                  {trueExpFragmentPath}
                </a>
              </p>
            </>
          )}
          {falseExpFragmentPath && (
            <>
              <b>False Exp Fragment Path</b>
              <p>
                <a href={`/editor.html${falseExpFragmentPath}.html`} target='_blank' rel='noreferrer'>
                  {falseExpFragmentPath}
                </a>
              </p>
            </>
          )}
          {defaultVariationFragmentPath && showDefaultContent && (
            <>
              <b>Default Exp Fragment Path</b>
              <p>
                <a href={`/editor.html${defaultVariationFragmentPath}.html`} target='_blank' rel='noreferrer'>
                  {defaultVariationFragmentPath}
                </a>
              </p>
            </>
          )}
        </div>
      </div>
    )
  }
  return <></>
}

export default RuleDetails
