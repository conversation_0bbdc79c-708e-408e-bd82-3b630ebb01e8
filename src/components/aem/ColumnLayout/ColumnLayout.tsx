import { ColumnLayoutStyled } from './ColumnLayout.styled'
import { HorizontalAlignment, LayoutBackgroundTypes } from './types'
import './ColumnLayout.scss'
import { AEM_CONSTANTS } from '../../../constants'
import { getSpacing } from '../../../utils/helper-util'
import ResponsiveContainer from '../ResponsiveContainer'

const ColumnLayout = (props: any): JSX.Element => {
  const {
    layoutBackground,
    backgroundColor,
    reverseStacking,
    sideSpace = false,
    borderRadius = false,
    top = false,
    right = false,
    bottom = false,
    left = false,
    lob = '',
    id = 'id'
  } = props
  const reverseStackingClass = reverseStacking ? `flex-lg-row flex-column-reverse` : ''
  const bgColor = layoutBackground === LayoutBackgroundTypes.color ? backgroundColor : ''
  const removeLayoutSectionPadding = sideSpace === true ? 'remove-layout-section-padding' : ''
  const hasBorder = borderRadius === true ? 'bordered' : ''

  const displaySection = () => {
    const { horizontalAlignment, hideInMobile } = props
    const horizontalAlignmentClass =
      horizontalAlignment !== HorizontalAlignment.default
        ? `align-items-${HorizontalAlignment[horizontalAlignment as keyof typeof HorizontalAlignment]} text-${
            HorizontalAlignment[horizontalAlignment as keyof typeof HorizontalAlignment]
          }`
        : ''

    try {
      if (props && Object.keys(props).length > 0) {
        return props?.[AEM_CONSTANTS.ITEMS_ORDER_PROP]?.map((item: any, index: number) => {
          const columnProps = props?.[AEM_CONSTANTS.ITEMS_PROP]?.[item]
          const bootstrapGridClass = columnProps?.bootstrapGridClasses
          const displayClass = hideInMobile ? (index === 1 ? 'd-none d-lg-flex' : 'd-flex') : 'd-flex'

          return (
            <div
              key={'section-' + index}
              className={`${bootstrapGridClass} ${displayClass} flex-column ${horizontalAlignmentClass}`}
            >
              <ResponsiveContainer componentProps={columnProps} />
            </div>
          )
        })
      }
      return <></>
    } catch (err) {
      console.error(err)
    }
  }
  return (
    <>
      <div data-testid={`column-layout-${id}`} className={`column-layout ${getSpacing(top, right, bottom, left)} ${lob}`}>
        <ColumnLayoutStyled
          {...props}
          className={`row ${bgColor} ${reverseStackingClass} ${removeLayoutSectionPadding} ${hasBorder} mobile-spacing`}
        >
          {displaySection()}
        </ColumnLayoutStyled>
      </div>
    </>
  )
}

export default ColumnLayout
