@import '././../../../assets/styles/v3/globalStyles.scss';

.column-layout {
  .row {
    margin-left: 0 !important;
    margin-right: 0 !important;

    // CCCOREUI-4058 Need BT margin for row class - contains Form component
    &:has(.form-container > form .form-fields) {
      @media (min-width: $xs) and (max-width: $md) {
        margin-right: calc(-0.5 * var(--bs-gutter-x)) !important;
        margin-left: calc(-0.5 * var(--bs-gutter-x)) !important;
      }
    }

    &.remove-layout-section-padding {
      > div {
        @media (max-width: $lg) {
          padding-left: 0;
          padding-right: 0;
        }

        @media (min-width: $lg) {
          &:first-child {
            padding-left: 0;
          }

          &:last-child {
            padding-right: 0;
          }
        }
      }
    }

    &.bordered {
      border-radius: $borderradius-100;
    }

    .col-12 {
      > .container {
        padding-left: 0 !important;
        padding-right: 0 !important;

        .react-title {
          display: block;
        }
      }
    }
  }

  @media (max-width: ($lg - 1px)) {
    .mobile-spacing {
      gap: 24px;
    }
  }
}
