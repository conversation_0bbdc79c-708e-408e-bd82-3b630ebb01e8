import { useEffect } from 'react'

import { BBFLComponent, BBFLDisplayType, BBFLProps, BBFLTheme } from './types'
import './BBFL.scss'
import RichText from '../../atoms/RichText'
import { SectionHeader } from '../../atoms/SectionHeader'

const BBFL = (props: BBFLProps): JSX.Element => {
  const {
    bbflTheme = BBFLTheme.RESIDENTIAL,
    display = '',
    bbflData = [],
    sectionID = 'bbfl_section',
    eyebrow = '',
    title = '',
    description = '',
    backToOffersText = '',
    backToOffersAnchorId = [],
    className = 'container',
    bbflComponentName = []
  } = props

  if (!document.getElementById('cox-bbfl')) {
    return <></>
  }

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (document.getElementById('cox-bbfl')) {
      document.dispatchEvent(new Event('load-cox-bbfl'))
    }
  }, [])

  if (display == BBFLDisplayType.SECTION) {
    const bbflUPI: any = []
    return (
      <div id={sectionID} data-testid={`bbfl-section-container`} className={`bbfl-section-container ${className}`}>
        <SectionHeader
          eyebrow={{
            value: eyebrow,
            isRTE: true
          }}
          title={{
            value: title,
            isRTE: true
          }}
          description={{
            className: 'main-description',
            value: description,
            isRTE: true
          }}
        />
        <div className={`bbfl-section row row-cols-xl-3 row-cols-md-2 row-cols-1`}>
          {bbflData?.constructor === Array &&
            bbflData?.map((item: any, index: any) => {
              const bbflItem = item && JSON.parse(item)[0]
              const UPI = bbflItem?.upi
              if (Object.keys(bbflItem).length === 0 || !bbflUPI.includes(UPI)) {
                bbflUPI.push(UPI)
                let anchor = `<a className="back-to-offers" href="#${backToOffersAnchorId[index]}">
                ${backToOffersText}
              </a>`
                if (
                  bbflComponentName[index] === BBFLComponent.PRODUCTCAROUSEL ||
                  bbflComponentName[index] === BBFLComponent.PSUCAROUSEL
                ) {
                  anchor = `<a className="back-to-offers" href="#${backToOffersAnchorId[index]}" data-link-type="carousel" target="carousel">
                ${backToOffersText}
              </a>`
                }
                const backToOffers = `<div className="text-center text-md-start">
                    ${anchor}</div>`
                const theme = bbflItem?.planType === 'Wireless' ? BBFLTheme.MOBILE : bbflTheme
                return (
                  <div key={index} className='col'>
                    <div id={UPI} className={`cox-bbfl ${theme}`} data-bbfl={item}></div>
                    <RichText text={backToOffers} />
                  </div>
                )
              }
            })}
        </div>
      </div>
    )
  } else if (display == BBFLDisplayType.INDIVIDUAL) {
    const bbflItems = bbflData && bbflData.constructor === String && JSON.parse(bbflData)
    return (
      <div data-testid={`bbfl-container`} className={`bbfl-container ${className}`}>
        {bbflItems.length > 0 ? (
          bbflItems?.map((bbflItem: any) => {
            const theme = bbflItem?.planType === 'Wireless' ? BBFLTheme.MOBILE : bbflTheme
            return (
              // eslint-disable-next-line react/jsx-key
              <div
                id={bbflItem?.upi}
                className={`cox-bbfl ${theme} xl-double`}
                data-bbfl={JSON.stringify(new Array(bbflItem))}
              ></div>
            )
          })
        ) : (
          <div className={`cox-bbfl ${bbflTheme} xl-double`} data-bbfl={bbflData}></div>
        )}
      </div>
    )
  } else {
    return <></>
  }
}

export default BBFL
