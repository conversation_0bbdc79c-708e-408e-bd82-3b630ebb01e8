import type { Meta, StoryObj } from '@storybook/react'

import { solutionListData } from './mockData/solutionListData'
import SolutionList from './SolutionList'

const meta: Meta<typeof SolutionList> = {
  title: 'molecules/SolutionList',
  component: SolutionList,
  parameters: {
    layout: 'fullscreen'
  },
  tags: ['autodocs'],
  argTypes: {}
}

export default meta
type Story = StoryObj<typeof SolutionList>

export const solutionList: Story = {
  args: solutionListData
}

export const BusinessSolutionList1Column: Story = {
  args: { ...solutionListData, lob: 'cox-busi' }
}

export const BusinessSolutionList2Column: Story = {
  args: { ...solutionListData, lob: 'cox-busi', twocolumn: true }
}
