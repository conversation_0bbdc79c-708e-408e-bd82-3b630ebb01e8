import React from 'react'

import { render, screen } from '@testing-library/react'
import renderer from 'react-test-renderer'

import { solutionListData } from './mockData/solutionListData'
import SolutionList from './SolutionList'
import useLobExperience from '../../../hooks/useLobExperience'

jest.mock('../../../hooks/useLobExperience')
const mockUseLobExperience = useLobExperience as jest.Mock

describe('SolutionList component', () => {
  beforeEach(() => {
    mockUseLobExperience.mockReturnValue({
      lob: 'residential',
      isBusiness: false,
      isResidential: true,
      setLob: jest.fn()
    })
  })
  test('renders SolutionList heading details for Resi', () => {
    render(<SolutionList {...solutionListData} />)
    expect(screen.getByTestId('solutionlist-wrapper')).toBeTruthy()
    expect(screen.getAllByText('This is title')).toBeTruthy()
    expect(screen.getAllByText('This is Description')).toBeTruthy()
  })
  test('renders SolutionList items details for Resi', () => {
    render(<SolutionList {...solutionListData} />)
    expect(screen.getByTestId('solutionlist-items')).toBeTruthy()
    expect(screen.getAllByTestId('solutionlist-item')).toHaveLength(3)
  })
  test('renders SolutionList pagination for Resi', () => {
    render(<SolutionList {...solutionListData} />)
    expect(screen.getByTestId('pagination-container')).toBeTruthy()
  })

  it('renders business links when isBusiness is true', () => {
    mockUseLobExperience.mockReturnValue({
      lob: 'business',
      isBusiness: true,
      isResidential: false,
      setLob: jest.fn()
    })

    render(<SolutionList {...solutionListData} />)

    // Wait for container to be present
    const busiContainer = screen.getByTestId('solutionlist-business-container')
    expect(busiContainer).toHaveClass('solutionlist-business-container')

    expect(screen.getAllByText('This is title')).toBeTruthy()
    expect(screen.queryByText('This is Description')).not.toBeInTheDocument()
  })
  it('Matches DOM Snapshot for Resi', () => {
    const domTree = renderer.create(<SolutionList {...solutionListData} />).toJSON()
    expect(domTree).toMatchSnapshot()
  })
  it('Matches DOM Snapshot for Busi', () => {
    const domTree = renderer.create(<SolutionList {...solutionListData} lob={'cox-busi'} />).toJSON()
    expect(domTree).toMatchSnapshot()
  })
})
