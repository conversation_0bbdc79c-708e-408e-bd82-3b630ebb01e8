@import '././../../../assets/styles/v3/globalStyles.scss';

.solutions-list {
  .solutionlist-container {
    display: flex;
    flex-direction: column;
    .solutionlist-details {
      display: flex;
      flex-direction: column;
      gap: 1.25rem;
      padding: 0 0 2.5rem 0;
      .title {
        text-align: center;
        @include cox-text-heading1;
      }
      .description {
        text-align: center;
        @include cox-text-paragraph3-regular;
      }
    }
    .solutions-container {
      display: flex;
      flex-direction: column;
      gap: 2.5rem;
      .solution {
        display: flex;
        flex-direction: column;
        border-bottom: 1px solid var(--utility-on-default-2);
        &:last-child {
          border-bottom: none;
        }
        .title {
          margin-bottom: 0.3125rem;
          @include cox-text-paragraph1-regular;
          cursor: pointer;
          a {
            text-decoration: underline;
            color: var(--color-blue-600);
            &:hover {
              text-decoration: none;
            }
          }
        }
        .description {
          margin-bottom: 2.5rem;
          @include cox-text-paragraph3-regular;
          .read-more {
            cursor: pointer;
            color: var(--color-neutral-400);
          }
        }
      }
    }
  }
  .solutionlist-business-container {
    display: flex;
    flex-direction: column;
    gap: 40px;
    .solutions-container-one {
      display: flex;
      flex-direction: column;
      gap: 2.5rem;
      @media (min-width: $xl) {
        gap: 3rem;
      }
      .solutions {
        display: flex;
        flex-direction: column;
        gap: 40px;
        @media (min-width: $xl) {
          gap: 48px;
        }
      }
    }
    .solutions-container-two {
      display: flex;
      flex-direction: column;
      gap: 2.5rem;
      @media (min-width: $lg) {
        flex-direction: row;
        gap: 1.5rem;
      }
      .solutions {
        display: flex;
        flex-direction: column;
        gap: 40px;
        @media (min-width: $xl) {
          gap: 48px;
        }
      }
    }
    @media (min-width: $xl) {
      gap: 48px;
    }
  }
}
