import React, { MouseEvent, useState } from 'react'

import './MainNavDesktop.scss'
import { CHEVRON_DOWN_LIGHT } from '../../../../../assets/images'
import useClickAwayListener from '../../../../../hooks/useClickAwayListener'
import { DeviceVariation } from '../../../../../types'
import { replaceColon } from '../../../../../utils/helper-util'
import { getImageSrc } from '../../../../../utils/local-util'
import { MainNavDesktopProps, NavbarLinkType } from '../../types'
import EscapeHatchBar from '../EscapeHatchBar'
import GlobalNavMenu from '../GlobalNavMenu'

const MainNavDesktop = (props: MainNavDesktopProps): React.JSX.Element => {
  const { data, timesCircle, timesCircleWhite, setApplyOverlay, chevronUpBlack = CHEVRON_DOWN_LIGHT } = props

  const topLevelMenuOptions: Array<NavbarLinkType> = []
  if (data) {
    for (const element of data) {
      if (element.containsSubNav) {
        topLevelMenuOptions.push(element)
      }
    }
  }

  const [activePrimaryMenuOption, setActivePrimaryMenuOption] = useState('')
  const [activeSelection, setActiveSelection] = useState('')

  const closeMenu = () => {
    setActivePrimaryMenuOption('')
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    setApplyOverlay && setApplyOverlay(false)
  }

  const handleClick = (event: MouseEvent, linkId: string) => {
    event.preventDefault()
    if (activePrimaryMenuOption == linkId) {
      closeMenu()
    } else {
      setActivePrimaryMenuOption(linkId)
      setActiveSelection('')
      if (setApplyOverlay) {
        setApplyOverlay(true)
      }
    }
  }

  const getActiveSelection = (activeSelection: string) => {
    setActiveSelection(activeSelection)
  }

  const globalNavRef = useClickAwayListener(closeMenu)

  return (
    <div data-testid={`desktop-global-nav-container`} className={`desktop-global-nav-container`}>
      {topLevelMenuOptions.length > 0 && (
        <ul className='main-nav-primary-links' ref={globalNavRef}>
          {topLevelMenuOptions.map((topLevelMenuItem: NavbarLinkType) => {
            const { linkId = '', containsSubNav, linkUrl, linkText, subItems } = topLevelMenuItem
            const displayMenu = linkId == activePrimaryMenuOption
            return (
              <li
                key={linkId}
                className={`cox-parent-menu-item  ${linkId == activePrimaryMenuOption && `active-parent-menu-item`}`}
                data-cox-menu-name={linkId}
              >
                <a
                  href={containsSubNav ? '#' : replaceColon(linkUrl)}
                  role='link'
                  id={linkId}
                  className='cox-menu-link'
                  target={'_self'}
                  title={linkText}
                  aria-label={linkText}
                  aria-expanded={displayMenu}
                  onClick={(event) => handleClick(event, linkId ?? '')}
                >
                  {linkText}&nbsp;
                  <img src={getImageSrc(chevronUpBlack)} className='chevron-icon' alt='' />
                </a>
                {displayMenu && (
                  <nav className='cox-menus' role='navigation' aria-label={linkText}>
                    <div className='menu-left'>
                      <EscapeHatchBar
                        variation={DeviceVariation.DESKTOP}
                        closeMenuFunction={closeMenu}
                        //icons
                        timesCircle={timesCircle}
                        timesCircleWhite={timesCircleWhite}
                      />
                      <GlobalNavMenu menuItems={subItems} getActiveSelection={getActiveSelection} />
                    </div>
                    {topLevelMenuItem?.subItems?.map((menuItem: NavbarLinkType) => {
                      if (menuItem.containsSubNav && menuItem.linkId == activeSelection) {
                        return (
                          <div className='menu-right' key={menuItem.linkId}>
                            <GlobalNavMenu menuItems={menuItem.subItems} />
                          </div>
                        )
                      }
                    })}
                  </nav>
                )}
              </li>
            )
          })}
        </ul>
      )}
    </div>
  )
}

export default MainNavDesktop
