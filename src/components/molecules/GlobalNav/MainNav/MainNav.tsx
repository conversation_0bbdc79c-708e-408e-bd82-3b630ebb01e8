import './MainNav.scss'

import { MouseEvent, useEffect, useState } from 'react'

import MainNavDesktop from './MainNavDesktop'
import MainNavMobile from './MainNavMobile'
import { HAMBURGER_MENU } from '../../../../assets/images'
import { convertStringToArray } from '../../../../utils/helper-util'
import { getImageSrc } from '../../../../utils/local-util'
import { MenuItem } from '../../../organisms/Header/types'
import { GlobalNavProps } from '../types'

const MainNav = (props: GlobalNavProps): JSX.Element => {
  const {
    navigationdetails,
    globalNavMenu = HAMBURGER_MENU,
    timesCircle,
    timesCircleWhite,
    setApplyOverlay,
    chevronUpBlack,
    setRightNavSelectedIndex,
    className = ''
  } = props

  const menuItems = convertStringToArray(navigationdetails).filter(
    (menuItem: MenuItem) => menuItem.showOnlyInSignIn !== true
  )

  const [displayMobileMenu, setDisplayMobileMenu] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    const spaRootElement = document.getElementById('spa-root')
    if (displayMobileMenu && spaRootElement?.style?.position) {
      spaRootElement.style.position = 'fixed'
    } else if (!displayMobileMenu && spaRootElement?.style?.position) {
      spaRootElement.style.position = 'static'
    }
  }, [displayMobileMenu])

  const handleDisplayMobileMenu = (state: boolean, event?: MouseEvent) => {
    event?.preventDefault()
    setIsMounted(!isMounted)
    setDisplayMobileMenu(state)
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    setRightNavSelectedIndex && setRightNavSelectedIndex(0)
  }

  return (
    <div data-testid={`main-nav-container`} className={`global-nav-container ${className}`}>
      <a
        href='#'
        role='link'
        title='Menu Icon'
        aria-label='Menu Icon'
        className='menu-icon'
        data-cox-menu-name='navigation'
        onClick={(event) => handleDisplayMobileMenu(true, event)}
      >
        <img src={getImageSrc(globalNavMenu)} alt='hamburger-menu' />
        &nbsp;
      </a>
      {displayMobileMenu && (
        <div className={'slide-mobile-menu'}>
          <MainNavMobile
            data={menuItems}
            handleDisplayMobileMenu={handleDisplayMobileMenu}
            timesCircle={timesCircle}
            timesCircleWhite={timesCircleWhite}
          />
        </div>
      )}
      <MainNavDesktop
        data={menuItems}
        setApplyOverlay={setApplyOverlay}
        timesCircle={timesCircle}
        timesCircleWhite={timesCircleWhite}
        chevronUpBlack={chevronUpBlack}
      />
    </div>
  )
}

export default MainNav
