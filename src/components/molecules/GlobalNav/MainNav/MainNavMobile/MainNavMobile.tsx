import React, { useState } from 'react'

import './MainNavMobile.scss'
import { CHEVRON_RIGHT_BLACK } from '../../../../../assets/images'
import { useUdoContext } from '../../../../../contexts/UdoContext'
import { DeviceVariation } from '../../../../../types'
import {
  getEspanolLink,
  getFilteredItemsForProspectCustomer,
  getPrimaryAndSecondaryMenuItems,
  replaceColon
} from '../../../../../utils/helper-util'
import { getImageSrc } from '../../../../../utils/local-util'
import { MainNavMobileProps, NavbarLinkType } from '../../types'
import EscapeHatchBar from '../EscapeHatchBar'

const MainNavMobile = (props: MainNavMobileProps): React.JSX.Element => {
  let { displayItems } = props
  const { menuTitle, data, handleDisplayMobileMenu, timesCircle, timesCircleWhite } = props
  const { udo } = useUdoContext()
  const { visitorType = '' } = udo
  const isNoncustomer = visitorType && visitorType === 'noncustomer'
  const isCustomer = visitorType && visitorType !== 'noncustomer'
  if (displayItems == undefined) {
    displayItems = data
  }

  const [displayMenu] = useState(true)
  const [displaySubNav, setDisplaySubNav] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  const [subNavProps, setSubNavProps] = useState({ data, menuTitle, displayItems, handleDisplayMobileMenu })
  const { primaryMenuOptions, secondaryMenuOptions } = getPrimaryAndSecondaryMenuItems(displayItems)

  const closeMenu = () => {
    handleDisplayMobileMenu(false)
  }

  const renderSubNav = (menuTitle?: string, subItems?: Array<NavbarLinkType>) => {
    let prevMenuTitle
    if (!data.find((e) => e.subItems === displayItems)) {
      prevMenuTitle = 'Main Menu'
    } else {
      for (const element of data) {
        const menuName = element.linkText
        const menu = element.subItems
        if (menu) {
          for (const element of menu) {
            if (element.linkText == menuTitle) {
              prevMenuTitle = menuName
            }
          }
        }
      }
    }
    setIsMounted(!isMounted)
    setSubNavProps({ data, menuTitle: prevMenuTitle, displayItems: subItems!, handleDisplayMobileMenu })
  }

  const renderPreviousNav = () => {
    // For Level 2 Menus:
    let items
    if (data?.find((e) => e.subItems === displayItems)) {
      items = data
      setSubNavProps({ data, menuTitle: '', displayItems: items!, handleDisplayMobileMenu })
      setDisplaySubNav(true)
    } else {
      let previousMenu
      // For Level 3 Menus:
      for (const element of data) {
        const secondLevelMenu = element.subItems
        if (secondLevelMenu) {
          for (const element of secondLevelMenu) {
            const thirdLevelMenu = element.subItems
            if (thirdLevelMenu == displayItems) {
              previousMenu = secondLevelMenu
              setSubNavProps({ data, menuTitle: 'Main Menu', displayItems: previousMenu!, handleDisplayMobileMenu })
              setDisplaySubNav(true)
            }
          }
        }
      }
    }
  }

  const getMenuOptions = (secondaryMenuOptions: Array<NavbarLinkType>, id: string) => {
    if (secondaryMenuOptions.length > 0) {
      return (
        <ul className={`menu-${id}`}>
          {secondaryMenuOptions.map((menuItem: NavbarLinkType) => {
            const { linkId = '', dontShowInMobile, showinespanol = false, linkUrl, linkText } = menuItem
            const url = replaceColon(linkUrl)
            const langAttr = showinespanol && { mporgnav: '', 'data-lang': 'es', 'data-href': getEspanolLink() }

            return (
              <div key={linkId}>
                <li className={`${dontShowInMobile && 'hide'}`} data-cox-menu-name={linkId}>
                  <a
                    href={showinespanol ? getEspanolLink() : url}
                    id={linkId}
                    className={`${id}-menu-item ${showinespanol && 'langLink'}`}
                    role='link'
                    target={'_self'}
                    title={linkText}
                    aria-label={linkText}
                    {...langAttr}
                  >
                    <span className='link-label'>{linkText}</span>
                  </a>
                </li>
              </div>
            )
          })}
        </ul>
      )
    }
  }

  const getSubNavMenuOptions = (primaryMenuOptions: Array<NavbarLinkType>, id: string) => {
    if (primaryMenuOptions.length > 0) {
      return (
        <ul className='menu'>
          {primaryMenuOptions.map((menuItem: NavbarLinkType) => {
            const {
              containsSubNav,
              linkId = '',
              dontShowInMobile,
              linkDescription,
              linkUrl,
              linkText,
              addHRAfter,
              subItems
            } = menuItem
            return (
              <>
                <div key={linkId}>
                  <li
                    className={`${dontShowInMobile && 'hide'}`}
                    onClick={() => renderSubNav(linkText, subItems)}
                    data-cox-menu-name={linkId}
                  >
                    <a
                      href={containsSubNav ? '#' : replaceColon(linkUrl)}
                      id={linkId}
                      className={`${id}-menu-item ${containsSubNav && 'contains-sub-nav'}`}
                      role='link'
                      target={'_self'}
                      title={linkText}
                      aria-label={linkText}
                      aria-expanded={displayMenu}
                    >
                      <div className='menu-item-content'>
                        <div className='link-section'>
                          <span className='link-label'>{linkText}</span>
                          {linkDescription && <span className='link-description'>{replaceColon(linkDescription)}</span>}
                        </div>
                        {containsSubNav && (
                          <img className='chevron-right-icon' src={getImageSrc(CHEVRON_RIGHT_BLACK)} alt='chevron-right' />
                        )}
                      </div>
                    </a>
                  </li>
                </div>
                {addHRAfter && <hr></hr>}
              </>
            )
          })}
        </ul>
      )
    }
  }
  const mountedStyle = { animation: 'inAnimationRight 150ms ease-in' }
  const unmountedStyle = { animation: 'outAnimationRight 1ms ease-out' }
  return !displaySubNav ? (
    <div data-testid={`mobile-global-nav-container`} className={`mobile-global-nav-container`}>
      {displayMenu && (
        <div
          className={`mobile-global-nav-slide ${menuTitle}`}
          style={!isMounted ? mountedStyle : unmountedStyle}
          onAnimationEnd={() => {
            if (!isMounted) {
              setDisplaySubNav(false)
            } else if (isMounted) {
              setDisplaySubNav(true)
            }
          }}
        >
          <EscapeHatchBar
            variation={DeviceVariation.MOBILE}
            menuTitle={menuTitle}
            backButtonFunction={renderPreviousNav}
            closeMenuFunction={closeMenu}
            //icons
            timesCircle={timesCircle}
            timesCircleWhite={timesCircleWhite}
          />
          <div className='trim-bottom'>
            {getSubNavMenuOptions(
              getFilteredItemsForProspectCustomer(primaryMenuOptions, isCustomer, isNoncustomer),
              'primary'
            )}
            {getMenuOptions(
              getFilteredItemsForProspectCustomer(secondaryMenuOptions, isCustomer, isNoncustomer),
              'secondary'
            )}
          </div>
        </div>
      )}
    </div>
  ) : (
    <MainNavMobile {...subNavProps} />
  )
}

export default MainNavMobile
