import './SubNavListContainerDesktop.scss'
import { DropdownListItem } from './SubNavListContainerDesktop.styled'
import { replaceColon } from '../../../../../utils/helper-util'

const SubNavListContainerDesktop = (props: any): JSX.Element => {
  const {
    menu,
    menuOrder,
    isSelected,
    getPrimaryActiveSelection,
    getSecondaryActiveSelection,
    getTertiaryActiveSelection,
    arrowrightlargeblack,
    showPreselection = true,
    selectedLink = ''
  } = props

  const handleLinkSelection = (event: any, selection: string, containsSubNav: boolean) => {
    // keyCode = 9 for TAB key
    if (event.keyCode !== 9) {
      event.stopPropagation()
      if (!containsSubNav) {
        return true
      }
      if (menuOrder == 'primary') {
        getPrimaryActiveSelection(selection)
      } else if (menuOrder == 'secondary') {
        getSecondaryActiveSelection(selection)
      } else if (menuOrder == 'tertiary') {
        getTertiaryActiveSelection(selection)
      }
      event.preventDefault()
      return false
    }
  }

  return (
    <div className={`subnav-list-container-desktop`}>
      <ul className={`subnav-desktop-menu`}>
        {menu?.subItems?.map((menuItem: any) => {
          const { linkId, containsSubNav, linkUrl, linkText } = menuItem
          const url = containsSubNav ? '#' : replaceColon(linkUrl)
          let isActive = false
          if (showPreselection && (linkText === isSelected || linkText === selectedLink)) {
            isActive = true
          }
          return (
            <DropdownListItem
              key={linkId}
              containsSubNav={containsSubNav}
              arrowrightlargeblack={arrowrightlargeblack}
              className={`${containsSubNav && 'contains-subnav'} ${isActive && 'is-active'}`}
              onClick={(event) => handleLinkSelection(event, linkText, containsSubNav)}
              onKeyDown={(event) => handleLinkSelection(event, linkText, containsSubNav)}
            >
              <a href={url} role='button' title={linkText} aria-label={linkText} className='subnav-menu-item-link'>
                {linkText}
              </a>
            </DropdownListItem>
          )
        })}
      </ul>
    </div>
  )
}

export default SubNavListContainerDesktop
