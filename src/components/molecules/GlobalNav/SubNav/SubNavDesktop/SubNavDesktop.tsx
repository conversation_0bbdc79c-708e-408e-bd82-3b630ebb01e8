import { useState } from 'react'

import styled from 'styled-components'

import { ARROW_DOWN_ICON } from '../../../../../assets/images'
import { HREF } from '../../../../../constants'
import useClickAwayListener from '../../../../../hooks/useClickAwayListener'
import { getImageSrc } from '../../../../../utils/local-util'
import { NavbarLinkType, SubNavDesktopProps } from '../../types'
import './SubNavDesktop.scss'
import SubNavListContainerDesktop from '../SubNavListContainerDesktop'

interface MenuButtonProps {
  arrowDownLargeBlack?: string
}

const MenuButton = styled.button<MenuButtonProps>`
  ::after {
    background-image: url(${(props) => getImageSrc(props.arrowDownLargeBlack || '')});
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    content: '';
    height: 15px;
    margin: auto;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    transition: transform 175ms;
    width: 15px;
  }
`

const SubNavDesktop = (props: SubNavDesktopProps): JSX.Element => {
  const { menu, arrowrightlargeblack, arrowDownLargeBlack = ARROW_DOWN_ICON } = props
  const { subItems, linkUrl, iconpathUrl = '', menutext, linkText } = menu

  interface Subitem {
    linkText?: string
    subItems?: Array<Subitem>
    linkUrl?: string
    siblings?: number //used to analyze the depth of the parent
  }

  const [displaySubnavMenus, setDisplaySubnavMenus] = useState(false)
  const [activePrimarySelection, setActivePrimarySelection] = useState('')
  const [activeSecondarySelection, setActiveSecondarySelection] = useState('')
  const [activeTertiarySelection, setActiveTertiarySelection] = useState('')
  const [secondaryMenu, setSecondaryMenu] = useState<Subitem>({})
  const [tertiaryMenu, setTertiaryMenu] = useState<Subitem>({})
  const [quaternaryMenu, setQuaternaryMenu] = useState<Subitem>({})
  //pre-loading related states
  const [showPrimaryPreSelection, setShowPrimaryPreSelection] = useState(true)
  const [userSelectedMenuPath, setUserSelectedMenuPath] = useState('')
  const [selectedLink, setSelectedLink] = useState('')

  const LABEL_SEPARATOR = '||'

  /**
   * This is added as there are situations where duplicate links have to be selected on the first loading of the page
   * //see how selection of https://www.cox.com/aboutus/policies/customer-service-agreement.html works
   *
   * Once the user starts to interact with the menu, then remove the duplicate selection.
   * @see SubNavListContainerDesktop's selectedLink property
   * @param menuClicked
   */
  const removeDuplicateLinkSelection = (menuClicked: boolean) => {
    if (menuClicked) setSelectedLink('')
  }

  const getPrimaryActiveSelection = (selection: string, clicked = true) => {
    let seletedItem
    setUserSelectedMenuPath(clicked || userSelectedMenuPath ? activePrimarySelection : '')
    setSecondaryMenu({})
    setActiveSecondarySelection('')
    setTertiaryMenu({})
    setActiveTertiarySelection('')
    setQuaternaryMenu({})
    setShowPrimaryPreSelection(true)
    removeDuplicateLinkSelection(clicked)
    if (subItems) {
      for (const item of subItems) {
        if (item.linkText === selection && item.containsSubNav) {
          setActivePrimarySelection(selection)
          setSecondaryMenu(item)
          seletedItem = item
          break
        }
      }
    }

    return seletedItem
  }

  const getSecondaryActiveSelection = (selection: string, clicked = true, preSelectedMenu: NavbarLinkType = {}) => {
    setTertiaryMenu({})
    setActiveTertiarySelection('')
    setQuaternaryMenu({})
    let selectedItem
    setUserSelectedMenuPath(clicked || userSelectedMenuPath ? activePrimarySelection + LABEL_SEPARATOR + selection : '')
    removeDuplicateLinkSelection(clicked)

    const secondaryMenuSubItemsArray = secondaryMenu?.subItems || preSelectedMenu?.subItems || []
    for (const item of secondaryMenuSubItemsArray) {
      if (item.linkText == selection) {
        setActiveSecondarySelection(selection)
        setTertiaryMenu(item)
        selectedItem = item
        break
      }
    }

    return selectedItem
  }

  const getTertiaryActiveSelection = (selection: string, clicked = true, preSelectedMenu: NavbarLinkType = {}) => {
    let selectedSubItem
    setUserSelectedMenuPath(
      clicked || userSelectedMenuPath
        ? activePrimarySelection + LABEL_SEPARATOR + activeSecondarySelection + LABEL_SEPARATOR + selection
        : ''
    )
    const tertiaryMenuSubItemsArray = tertiaryMenu?.subItems || preSelectedMenu?.subItems || []
    for (const item of tertiaryMenuSubItemsArray) {
      if (item.linkText == selection) {
        setActiveTertiarySelection(selection)
        setQuaternaryMenu(item)
        selectedSubItem = item
        break
      }
    }
    removeDuplicateLinkSelection(clicked)

    return selectedSubItem
  }

  const resetMenu = (preselect = false) => {
    setActivePrimarySelection('')
    setSecondaryMenu({})
    setActiveSecondarySelection('')
    setTertiaryMenu({})
    setActiveTertiarySelection('')
    setQuaternaryMenu({})
    //load preselection only if preselect is passed or user hasn't changed local menu hasn't been loaded yet.
    if (preselect || userSelectedMenuPath.length > 0) {
      handlePreselect()
    }
  }

  const handleMenuButtonClick = () => {
    setDisplaySubnavMenus(!displaySubnavMenus)
    resetMenu(!userSelectedMenuPath)
  }

  const handleClickAway = (event: any) => {
    if (event.target.classList.contains('subnav-menu-item-link')) {
      return
    }
    //the useState is not picking the change right away and giving it some buffer time.
    setTimeout(() => {
      setUserSelectedMenuPath('')
    }, 100)
    setDisplaySubnavMenus(false)
    setDisplaySubnavMenus(false)
    resetMenu()
  }

  const subnavRef = useClickAwayListener(handleClickAway)

  //pre-selection and caching user selection related methods.
  const handlePreselect = () => {
    const currentPage = getCurrentPage()

    if (userSelectedMenuPath) {
      populatePreselection(userSelectedMenuPath.split(LABEL_SEPARATOR))
      return
    }

    const labelHolder: Array<Subitem> = []
    if (subItems) listMenuLabel(subItems, '', labelHolder)
    const currentItems = labelHolder.filter((item) => {
      if (item?.linkUrl) {
        return item.linkUrl.includes(currentPage)
      }
    })

    /*
     * when there are multiple links on the same item
     * 1. if the links appears on the fouth, then add that as secondary menu
     * 2. if the page apears other than fourth menu, take whichever has smaller size of menu and show that as parent.
     */
    let pickedItem = currentItems[0]
    for (const item of currentItems) {
      const paths = item.linkText?.split(LABEL_SEPARATOR)
      if (paths?.length == 4) {
        pickedItem = item
        break
      }
      if (item.siblings && pickedItem.siblings && item?.siblings < pickedItem?.siblings) {
        pickedItem = item
      }
    }

    if (pickedItem?.linkText) {
      const paths = pickedItem.linkText.split(LABEL_SEPARATOR)
      setSelectedLink(paths[paths.length - 1]) //take the last leaf item
      populatePreselection(paths)
    }
  }

  /**
   * method to populate the preselected menu on page load.
   * When the 'Menu' is clicked, the current page selection will be pre-selected.
   * @param path array containing primary, secondary, tertiary.. lables.
   */
  const populatePreselection = (path: Array<string>) => {
    //the paths size determines the link depth
    setActivePrimarySelection(path[0]) //primary label
    const secondaryItem = getPrimaryActiveSelection(path[0], false)
    const tertiaryItem = getSecondaryActiveSelection(path[1], false, secondaryItem)
    setActiveSecondarySelection(path[1]) //secondary label
    const quaternaryItem = getTertiaryActiveSelection(path[2], false, tertiaryItem)
    setActiveTertiarySelection(path[2]) //tertiary label
    setQuaternaryMenu(quaternaryItem || {})
    /*
     * The following section is to populate the fourth menu on the second menu when loading the page
     * and on the first click on the menu.
     * If the user hasn't changed the selection, the fourth level menu will be shown on the second menu. Once other selection happens
     * the new selection will be shown on the 'Menu' click
     *
     * to proceed..
     * check if the user has changed the pre-selected menu [!userSelectedMenuPath]
     * check if the item has linkText and subItems
     */
    if (!userSelectedMenuPath && quaternaryItem?.linkText && quaternaryItem?.subItems) {
      setSecondaryMenu({})
      setActiveSecondarySelection('')
      setTertiaryMenu({})
      setActiveTertiarySelection('')
      setQuaternaryMenu({})
      setSecondaryMenu(quaternaryItem)
      setActiveSecondarySelection(path[3])
      setShowPrimaryPreSelection(false)
    }
  }

  /**
   * Recursively populate the label.
   * @param subItem
   * @param path
   * @param labelHolder
   */
  const listMenuLabel = (subItem: NavbarLinkType[], path: string, labelHolder: Array<Subitem>, siblings = 0) => {
    for (const item of subItem) {
      const currentMenuPath = path ? `${path}||${item.linkText?.trim()}` : item.linkText?.trim()
      if (!item.subItems || item.subItems.length === 0) {
        labelHolder.push({ linkText: currentMenuPath, linkUrl: item.linkUrl?.trim(), siblings })
      }
      const sizeOfSiblings = item.subItems?.length

      if (item.subItems && item.subItems.length > 0) {
        listMenuLabel(item.subItems, currentMenuPath || '', labelHolder, sizeOfSiblings)
      }
    }
  }

  const getCurrentPage = () => {
    const currentUrl = new URL(HREF)
    return currentUrl.pathname
  }

  return (
    <div className='sub-nav-desktop-wrapper'>
      <div data-testid={`sub-nav-container`} className={`sub-nav-container`}>
        <div className='subnavigation'>
          <div className='site-section-marker'>
            <a href={linkUrl} role='button' title={linkText} aria-label={linkText}>
              <img src={getImageSrc(iconpathUrl)} alt={linkText} />
              <p className='subnav-menu-title' tabIndex={0}>
                {linkText}
              </p>
            </a>
          </div>
          <MenuButton
            ref={subnavRef}
            className={`subnav-menu-button ${displaySubnavMenus && 'blue-highlight expanded'}`}
            onClick={() => handleMenuButtonClick()}
            arrowDownLargeBlack={arrowDownLargeBlack}
          >
            {menutext || 'Menu'}
          </MenuButton>
        </div>
      </div>
      {displaySubnavMenus && (
        <div className='sub-nav-menus'>
          <div className='section-title hidden'>
            <img src={getImageSrc(iconpathUrl)} alt='subnav-icon' />
            <p>{linkText}</p>
          </div>
          <SubNavListContainerDesktop
            menu={menu}
            menuOrder='primary'
            showPreselection={showPrimaryPreSelection}
            isSelected={activePrimarySelection}
            getPrimaryActiveSelection={getPrimaryActiveSelection}
            arrowrightlargeblack={arrowrightlargeblack}
            selectedLink={selectedLink}
          />
          {activePrimarySelection && (
            <SubNavListContainerDesktop
              menu={secondaryMenu}
              menuOrder='secondary'
              isSelected={activeSecondarySelection}
              getSecondaryActiveSelection={getSecondaryActiveSelection}
              arrowrightlargeblack={arrowrightlargeblack}
              selectedLink={selectedLink}
            />
          )}
          {activeSecondarySelection && (
            <SubNavListContainerDesktop
              menu={tertiaryMenu}
              menuOrder='tertiary'
              isSelected={activeTertiarySelection}
              getTertiaryActiveSelection={getTertiaryActiveSelection}
              arrowrightlargeblack={arrowrightlargeblack}
              selectedLink={selectedLink}
            />
          )}
          {activeTertiarySelection && (
            <SubNavListContainerDesktop
              menu={quaternaryMenu}
              menuOrder='quaternary'
              arrowrightlargeblack={arrowrightlargeblack}
              selectedLink={selectedLink}
            />
          )}
        </div>
      )}
    </div>
  )
}

export default SubNavDesktop
