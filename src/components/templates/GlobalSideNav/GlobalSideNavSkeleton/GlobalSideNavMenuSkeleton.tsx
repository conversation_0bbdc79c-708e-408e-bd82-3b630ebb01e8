import '../GlobalSideNavMenu/GlobalSideNavMenu.scss'
import { Skeleton } from '../../../atoms/Skeleton'

const GlobalSideNavMenuSkeleton = (): JSX.Element => {
  return (
    <nav className='side-nav-menu-container'>
      <ul className='side-nav-menu'>
        <div className='scroll-wrapper'>
          {[1, 2, 3, 4, 5, 6].map((nav: any) => (
            <li key={nav} className='side-nav-item'>
              <a href={nav}>
                <Skeleton shape='circle' width={20} height={20} />
                <Skeleton shape='text' width={'100%'} />
              </a>
            </li>
          ))}
        </div>
      </ul>
    </nav>
  )
}

export default GlobalSideNavMenuSkeleton
