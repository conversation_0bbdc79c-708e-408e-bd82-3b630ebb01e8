import '../GlobalSideNav.scss'

import GlobalSideNavMenuSkeleton from './GlobalSideNavMenuSkeleton'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import { ScreenSizes } from '../../../../types'
import { Skeleton } from '../../../atoms/Skeleton'
import { RBDropdown } from '../../../shared/ReactBootstrap'

const GlobalSideNavSkeleton = (): JSX.Element => {
  const { width: windowWidth } = useWindowSize()
  const isLargeScreen = windowWidth >= ScreenSizes.LG
  return (
    <div className={`global-side-nav-container ${isLargeScreen ? 'container' : ''}`} data-testid={'side-nav-skeleton'}>
      <div className={`col-12 col-lg-4 col-xl-3 side-nav-wrapper-${!isLargeScreen ? 'mobile' : 'desktop'}`}>
        {!isLargeScreen ? (
          <RBDropdown>
            <RBDropdown.Toggle>
              <div className='side-nav-mobile'>
                <div className='progress-indicator'>
                  <Skeleton shape='circle' width={20} height={20} />
                  <Skeleton shape='text' width={180} />
                </div>
                <Skeleton shape='circle' width={15} height={15} />
              </div>
            </RBDropdown.Toggle>
          </RBDropdown>
        ) : (
          <GlobalSideNavMenuSkeleton />
        )}
      </div>
      <div className='col-12 col-lg-8 col-xl-9 side-nav-children-placeholder'>
        {[1, 2, 3, 4, 5, 6].map((item) => (
          <Skeleton shape='text' width={'100%'} key={item} />
        ))}
      </div>
    </div>
  )
}

export default GlobalSideNavSkeleton
