import { useEffect, useRef, useState } from 'react'

import './GlobalSideNav.scss'
import GlobalSideNavMenu from './GlobalSideNavMenu'
import GlobalSideNavSkeleton from './GlobalSideNavSkeleton'
import { GlobalSideNavProps, SideNavItem, SubNavItem } from './types'
import { SIDE_NAVIGATION_DETAILS_PATH } from '../../../constants'
import { useSideNavContext } from '../../../contexts/SideNavContext'
import { useAxios } from '../../../hooks/useAxios'
import { useWindowSize } from '../../../hooks/useWindowSize'
import { ScreenSizes } from '../../../types'
import { getImageSrc } from '../../../utils/local-util'
import { triggerNewRelicAction } from '../../../utils/newrelic-util'
import Banner, { BannerType, BannerVariaion } from '../../atoms/Banner'
import { ButtonStates } from '../../atoms/Button'
import { RBDropdown } from '../../shared/ReactBootstrap'

const GlobalSideNav = (props: GlobalSideNavProps): JSX.Element => {
  const { response, loading, error } = useAxios({ url: SIDE_NAVIGATION_DETAILS_PATH })
  const { children, handleNavigate, activeSideNavId } = props
  const [icons, setIcons] = useState({
    chevronUpIcon: '',
    chevronUpIconAlt: '',
    chevronDownIcon: '',
    chevronDownIconAlt: '',
    openInNewTabIcon: '',
    openInNewTabIconAlt: ''
  })
  const { sideNavContext, setSideNavContext } = useSideNavContext()
  const [showMobileMenu, setShowMobileMenu] = useState(false)
  const { width: windowWidth } = useWindowSize()
  const isLargeScreen = windowWidth >= ScreenSizes.LG
  const selectedItemRef = useRef<HTMLDivElement>(null)

  const handleNavigationDetails = (e: any, id: string, collapse: boolean, title: string, url: string, subId?: string) => {
    e?.preventDefault()
    let updatedNavDetails
    const onlyExpandItem = sideNavContext.some(
      (navDetail: SideNavItem) => navDetail.title === title && navDetail.subNavItems && navDetail.subNavItems?.length > 0
    )
    if (onlyExpandItem) {
      updatedNavDetails = sideNavContext.map((navDetail: SideNavItem) => ({
        ...navDetail,
        expanded: !collapse && navDetail.id === id
      }))
    } else {
      updatedNavDetails = sideNavContext.map((navDetail: SideNavItem) => ({
        ...navDetail,
        selected: navDetail.id === id,
        expanded: navDetail.id === id && navDetail.subNavItems && navDetail.subNavItems?.length > 0,
        subNavItems: navDetail.subNavItems?.map((subNavItem) => ({
          ...subNavItem,
          selected: navDetail.id === id && subNavItem.id === subId
        }))
      }))
    }
    setSideNavContext(updatedNavDetails)
    if (!onlyExpandItem) {
      setShowMobileMenu(false)
    }
    if (url && handleNavigate) {
      handleNavigate(url)
    }
  }

  useEffect(() => {
    if (response?.sideNavigationDetails?.length > 0) {
      const updatedNavDetails = response.sideNavigationDetails.map((navDetail: SideNavItem) => {
        const updatedSubNavItems = navDetail.subNavItems
          ?.filter((subNav) => {
            try {
              if (subNav?.ruleExpression) {
                return eval(subNav.ruleExpression)
              }
              return !subNav.ruleExpression
            } catch (err) {
              const errorDesc = `Check the rule expression: ${subNav?.ruleExpression}`
              console.error(errorDesc, err)
              triggerNewRelicAction({
                errorMessage: errorDesc,
                errorName: 'Global Side Nav: Rule Expression Error',
                actionName: 'Rule Expression Error'
              })
              return !subNav.ruleExpression
            }
          })
          .map((subNavItem: SubNavItem) => ({
            ...subNavItem,
            selected: activeSideNavId && activeSideNavId === subNavItem.id
          }))
        return {
          ...navDetail,
          selected: activeSideNavId && activeSideNavId === navDetail.id,
          subNavItems: updatedSubNavItems,
          expanded: updatedSubNavItems?.some((subNavItem) => subNavItem.selected) || false,
          hasRuleExpression: navDetail.subNavItems?.some((subNav) => subNav?.ruleExpression)
        }
      })

      const filteredNavDetails = updatedNavDetails.filter(
        (navDetail: SideNavItem) =>
          !(navDetail.hasRuleExpression === true && (!navDetail.subNavItems || navDetail?.subNavItems?.length === 0))
      )
      setSideNavContext(filteredNavDetails)

      const { openInNewTabIcon, openInNewTabIconAlt, chevronDownIcon, chevronDownIconAlt, chevronUpIcon, chevronUpIconAlt } =
        response
      setIcons({
        chevronUpIcon: chevronUpIcon,
        chevronUpIconAlt: chevronUpIconAlt,
        chevronDownIcon: chevronDownIcon,
        chevronDownIconAlt: chevronDownIconAlt,
        openInNewTabIcon: openInNewTabIcon,
        openInNewTabIconAlt: openInNewTabIconAlt
      })
    }
  }, [response])

  useEffect(() => {
    if (selectedItemRef.current) {
      selectedItemRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      })
    }
  }, [showMobileMenu])

  const selectedSideNavMobile: SideNavItem =
    sideNavContext.filter(
      (sideNavItem: SideNavItem) =>
        sideNavItem.selected || sideNavItem?.subNavItems?.some((subNavItem) => subNavItem.selected)
    )[0] || []

  if (loading) {
    return <GlobalSideNavSkeleton />
  }

  return (
    <div className={`global-side-nav-container ${isLargeScreen ? 'container' : ''}`}>
      {error ? (
        <div className={`col-12 col-lg-4 col-xl-3 side-nav-wrapper-${isLargeScreen ? 'desktop' : 'mobile'}`}>
          <Banner
            altIcon='Warning'
            iconPath='/content/dam/cox/common/icons/ui_components/circle-exclamation-pure-orange.svg'
            bannerType={BannerType.DYNAMIC}
            buttonStates={ButtonStates.ACTIVE}
            message="<p>We're not able to load this part of the page.</p>"
            variation={BannerVariaion.WARNING}
          />
        </div>
      ) : (
        <div className={`col-12 col-lg-4 col-xl-3 side-nav-wrapper-${isLargeScreen ? 'desktop' : 'mobile'}`}>
          {!isLargeScreen ? (
            <RBDropdown show={showMobileMenu} onToggle={() => setShowMobileMenu(!showMobileMenu)}>
              <RBDropdown.Toggle
                id='side-nav-dropdown-toggle'
                aria-label='Toggle side navigation menu'
                aria-expanded={showMobileMenu}
                aria-controls='side-nav-menu'
                tabIndex={0}
              >
                <div className='side-nav-mobile' data-testid={'side-nav-progress-indicator'}>
                  <div className='progress-indicator'>
                    <img
                      className='side-nav-item-img'
                      src={getImageSrc(selectedSideNavMobile?.iconUrl)}
                      alt={selectedSideNavMobile?.iconUrlAlt}
                    />
                    <span>{selectedSideNavMobile?.title}</span>
                  </div>
                  <img
                    className='chevron-img'
                    src={getImageSrc(!showMobileMenu ? icons.chevronDownIcon : icons.chevronUpIcon)}
                    alt={!showMobileMenu ? icons.chevronDownIconAlt : icons.chevronUpIconAlt}
                    aria-hidden='true'
                  />
                </div>
              </RBDropdown.Toggle>
              <RBDropdown.Menu data-testid={'side-nav-menu'} aria-labelledby='side-nav-dropdown-toggle'>
                <GlobalSideNavMenu
                  {...props}
                  handleNavigationDetails={handleNavigationDetails}
                  selectedItemRef={selectedItemRef}
                  icons={icons}
                />
              </RBDropdown.Menu>
            </RBDropdown>
          ) : (
            <GlobalSideNavMenu {...props} handleNavigationDetails={handleNavigationDetails} icons={icons} />
          )}
        </div>
      )}
      <div className='col-12 col-lg-8 col-xl-9'>{children}</div>
    </div>
  )
}

export default GlobalSideNav
