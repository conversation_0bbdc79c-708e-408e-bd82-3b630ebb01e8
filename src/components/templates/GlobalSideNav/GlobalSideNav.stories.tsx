import type { Meta, StoryObj } from '@storybook/react'

import GlobalSideNav from './GlobalSideNav'
import Layout from '../Layout'
import { childrenContent } from './mockData/globalSideNavStoriesData'
const meta: Meta<typeof GlobalSideNav> = {
  title: 'templates/GlobalSideNav',
  component: GlobalSideNav,
  parameters: {
    layout: 'fullscreen',
    backgrounds: {
      default: 'dark',
      values: [
        { name: 'light', value: '#ffffff' },
        { name: 'dark', value: '#6c7880' }
      ]
    }
  },
  tags: ['autodocs'],
  argTypes: {},
  decorators: [
    (Story, context) => {
      const utagData = context?.parameters?.utag_data || { categorySubscribed: ['Internet', 'TV', 'Phone'] }
      global.utag_data = utagData
      return (
        <Layout appName='storybook'>
          <Story />
        </Layout>
      )
    }
  ]
}

export default meta
type Story = StoryObj<typeof GlobalSideNav>

export const SideNavOnHomePage: Story = {
  args: {
    children: childrenContent,
    activeSideNavId: 'dashboard-item',
    appContext: 'myprofile'
  }
}
export const SideNavOnIbillPage: Story = {
  args: {
    children: childrenContent,
    activeSideNavId: 'paymybills-item',
    appContext: 'myprofile'
  }
}

export const SideNavWithL2Selected: Story = {
  args: {
    children: childrenContent,
    activeSideNavId: 'homephone-sub-item',
    appContext: 'resaccount'
  }
}

export const SideNavWithNoServices: Story = {
  args: {
    children: childrenContent,
    activeSideNavId: 'billingoptions-sub-item',
    appContext: 'myprofile'
  },
  parameters: {
    utag_data: { categorySubscribed: [] }
  }
}
