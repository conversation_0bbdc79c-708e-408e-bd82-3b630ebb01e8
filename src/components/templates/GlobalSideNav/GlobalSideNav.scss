@import '../../../assets/styles/v3/globalStyles.scss';

.global-side-nav-container {
  display: flex;
  flex-direction: column;
  .side-nav-wrapper-mobile {
    padding: 2.5rem 1.5rem 2.5rem 1.5rem;
    position: sticky;
    top: 0;
    .dropdown {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      .dropdown-toggle {
        width: 100%;
        background: transparent;
        border: none;
        padding: 0;
        .btn .btn-primary {
          display: none;
        }
        .side-nav-mobile {
          width: 100%;
          height: 3.375rem;
          display: flex;
          padding: 0rem 1.25rem;
          justify-content: space-between;
          align-items: center;
          border-radius: 3.75rem;
          background: var(--cox-neutral-neutral-100-white, #fff);
          box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.15);
          .progress-indicator {
            display: flex;
            align-items: center;
            gap: 1rem;
            .side-nav-item-img {
              width: 1.5rem;
              height: 1.5rem;
            }
            span {
              @include cox-text-paragraph3-bold;
              color: var(--color-blue-600);
            }
          }
          .chevron-img {
            width: 1rem;
            height: 1rem;
            flex-shrink: 0;
          }
        }
      }
      .dropdown-toggle::after {
        display: none !important;
      }
      .dropdown-menu {
        margin-top: 0.75rem;
        padding: 0;
        width: 100%;
        border-radius: 1.25rem;
        ul {
          margin: 0;
        }
      }
    }
  }

  @media (min-width: $md) {
    .side-nav-wrapper-mobile {
      .dropdown {
        .dropdown-toggle {
          .side-nav-mobile {
            padding: 0 1.75rem;
          }
        }
      }
    }
  }

  @media (min-width: $lg) {
    flex-direction: row;
    gap: 1.5rem;
    padding: 1.5rem 0;
    .side-nav-wrapper-desktop {
      display: flex;
      .side-nav-menu-container {
        .side-nav-menu {
          position: sticky;
          top: 0;
        }
      }
    }
  }
  .side-nav-children-placeholder {
    background-color: #fff;
    border-radius: 1rem;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}
