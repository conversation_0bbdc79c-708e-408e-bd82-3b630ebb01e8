import { ReactNode } from 'react'

import ContentRenderer from './ContentRenderer'
import { AddressProvider } from '../../../contexts/AddressContext'
import { AppDataProvider } from '../../../contexts/AppDataContext'
import { BBFLProvider } from '../../../contexts/BBFLContext'
import { FlexOffersProvider } from '../../../contexts/FlexOffersContext'
import { HeaderProfileProvider } from '../../../contexts/HeaderProfileContext'
import { LocationProvider } from '../../../contexts/LocationContext'
import { SideNavProvider } from '../../../contexts/SideNavContext'
import { UdoProvider } from '../../../contexts/UdoContext'
import './../../../assets/styles/v3/globalStyles.scss'

const Layout = ({ children, appName = '' }: { children: ReactNode; appName: string; configUrl?: string }) => {
  return (
    <AddressProvider>
      <AppDataProvider>
        <BBFLProvider>
          <FlexOffersProvider>
            <HeaderProfileProvider>
              <LocationProvider>
                <SideNavProvider>
                  <UdoProvider>
                    <ContentRenderer appName={appName}>{children}</ContentRenderer>
                  </UdoProvider>
                </SideNavProvider>
              </LocationProvider>
            </HeaderProfileProvider>
          </FlexOffersProvider>
        </BBFLProvider>
      </AppDataProvider>
    </AddressProvider>
  )
}

export default Layout
