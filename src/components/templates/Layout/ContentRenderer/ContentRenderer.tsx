import { ReactNode, Suspense } from 'react'

import { FOOTER_XF, HEADER_XF, PATHNAME } from '../../../../constants'
import { useAppDataContext } from '../../../../contexts/AppDataContext'
import withTemplateData from '../../../../hoc/withTemplateData'
import { usePageCommon } from '../../../../hooks/usePageCommon'
import { getThemeClass } from '../../../../utils/helper-util'
import Footer from '../../../organisms/Footer'
import Header from '../../../organisms/Header'
import MinimalFooter from '../../../organisms/MinimalFooter'
import MinimalHeader from '../../../organisms/MinimalHeader'
import ErrorBoundary from '../../ErrorBoundary'
import Fallback from '../../Fallback'
import PageLoader from '../../PageLoader'
import MetaTagRenderer from '../MetaTagRenderer'
import ScriptRenderer from '../ScriptRenderer'

const ContentRenderer = ({ appName, children }: { appName: string; children: ReactNode }) => {
  const HeaderXF = withTemplateData(Header, HEADER_XF)
  const FooterXF = withTemplateData(Footer, FOOTER_XF)
  const prefix = PATHNAME.includes('/ui/v8') ? '/ui/v8' : ''
  const { displayChat, chatContent, displayHeader, shouldRender, displayFooter, minimalHeader, minimalFooter } =
    usePageCommon(appName, prefix)
  const { appData } = useAppDataContext()
  const appDataAvailable = appData?.success
  const sections = appData?.page?.template?.sections || appData?.page?.sections
  const customerType = sections?.payment?.customerType

  return (
    <div className={`${getThemeClass()}`}>
      <MetaTagRenderer displayChat={displayChat} customerType={customerType} chatContent={chatContent} />
      {appDataAvailable && <ScriptRenderer />}
      {displayHeader && (minimalHeader ? <MinimalHeader /> : <HeaderXF />)}
      {shouldRender && (
        <Suspense fallback={<PageLoader />}>
          <ErrorBoundary fallback={<Fallback />}>{children}</ErrorBoundary>
        </Suspense>
      )}
      {displayFooter && (minimalFooter ? <MinimalFooter /> : <FooterXF />)}
    </div>
  )
}

export default ContentRenderer
