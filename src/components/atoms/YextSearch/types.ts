export type YextSearchProps = {
  id: string
  searchstyle: SearchStyleStrings
  searchRoot?: SearchRoots
  isInEditor?: boolean
  searchclosex?: string
  searchmagnifyingglassicon?: string
  placeholder?: string
  className?: string
  handleCloseSearchInput?: (e?: any) => void
}

export enum SearchStyleStrings {
  WHITE = 'white',
  WHITELILAC = 'whitelilac'
}

export enum SearchRoots {
  BUSINESS = 'business',
  RESIDENTIAL = 'residential'
}
export type YextSearchResultType = {
  matchedSubstrings: []
  queryIntents: []
  value: string
  verticalKeys: []
}

export interface InputProps {
  searchclosex?: string
}
