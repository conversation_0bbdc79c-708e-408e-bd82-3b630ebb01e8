import { KeyboardEvent, SyntheticEvent, useEffect, useRef, useState } from 'react'

import { SearchStyleStrings, YextSearchProps, YextSearchResultType } from './types'
import { CLOSE_X_ICON, MAGNIFYING_GLASS } from '../../../assets/images'
import { COX_DOMAINS, HASH, ORIGIN, PATHNAME, SEARCH } from '../../../constants'
import { useUdoContext } from '../../../contexts/UdoContext'
import useClickAwayListener from '../../../hooks/useClickAwayListener'
import useLobExperience from '../../../hooks/useLobExperience'
import { getAutoCompleteOptions } from '../../../services'
import { RBDropdown } from '../../shared/ReactBootstrap'
import RichText from '../RichText'
import './YextSearch.scss'

export const YEXT_KEY = process.env.YEXT_API_KEY || ''

const YextSearch = (props: YextSearchProps): React.JSX.Element => {
  const { lob, isBusiness } = useLobExperience()
  const {
    id,
    searchstyle = SearchStyleStrings.WHITE,
    searchclosex = CLOSE_X_ICON,
    searchmagnifyingglassicon = MAGNIFYING_GLASS,
    placeholder = isBusiness ? 'Search Cox Business' : 'Search Cox.com',
    className,
    handleCloseSearchInput
  } = props

  //&query=
  const [searchInput, setSearchInput] = useState<string>('')
  const [isOptionSelected, setIsOptionSelected] = useState(false)
  const [dropdownItems, setDropdownItems] = useState([])
  const [isOpen, setIsOpen] = useState(false)
  const [isUserTyping, setIsUserTyping] = useState(false)
  const { udo } = useUdoContext()
  const { localeName, visitorType, zip } = udo
  const searchInputRef = useRef<HTMLInputElement>(null)

  const experienceKey = `cox_${lob}_answers`
  // Yext api autocomplete and suggestions
  useEffect(() => {
    getAutocompleteOptions()
  }, [searchInput])

  const getAutocompleteOptions = async () => {
    const queryParams = getQueryParamsForAutocomplete(searchInput)
    try {
      const response = await getAutoCompleteOptions({ queryParams })
      if (response) {
        setDropdownItems(response)
      }
    } catch (err) {
      console.error(err)
    }
  }

  // open and close dropdown as expected
  useEffect(() => {
    if (dropdownItems.length === 0) {
      setIsOpen(false)
    }
    if (searchInput && dropdownItems.length !== 0 && !isOptionSelected) {
      setIsOpen(true)
    }
  }, [dropdownItems])

  const getQueryParamsForAutocomplete = (input: string) => {
    // parameters that are necessary for query to Yext
    const baseParameters = {
      api_key: YEXT_KEY,
      experienceKey: experienceKey,
      locale: 'en',
      v: 20190101,
      input: cleanQueryInput(input)
    }

    // parameters useful for Cox
    const coxParameters = {
      sessionTrackingEnabled: false,
      version: 'PRODUCTION',
      businessId: 866292
    }

    const parameters = {
      ...baseParameters,
      ...coxParameters
    }

    return parameters
  }

  // helper functions
  const cleanQueryInput = (input: string) => {
    return input.replace(/ /g, '+')
  }

  const redirectUrl = `${COX_DOMAINS.PROD}/search/${lob}/`
  function createRedirectUrlWithQueryParamsAndContext(params: string) {
    const context = {
      market: localeName ? localeName : 'national',
      visitorType: visitorType,
      zipcode: zip
    }
    const stringifiedContext = encodeURIComponent(JSON.stringify(context))
    const fullURL = ORIGIN + PATHNAME + SEARCH + HASH
    const referralPageUrl = isBusiness ? '&referrerPageUrl=' + fullURL : ''
    return `${redirectUrl}?query=${params}&context=${stringifiedContext}${referralPageUrl}`
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    const regex = /[<>\\'\\/=]/g
    const filteredInput = e.target.value.replace(regex, '')
    setSearchInput(filteredInput)
    setIsUserTyping(true)
  }

  const handleSubmit = (e?: SyntheticEvent, value?: string) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    e?.preventDefault && e.preventDefault()
    closeSuggestion()
    const searchUrl = createRedirectUrlWithQueryParamsAndContext(cleanQueryInput(value ?? searchInput))
    window.open(searchUrl, '_self')
  }

  const handleKeypress = (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit(e)
    }
  }

  const handleOnClick = (e: React.MouseEvent<HTMLElement>) => {
    setIsOptionSelected(true)
    setSearchInput(e.currentTarget?.innerText)
    handleSubmit(e, e.currentTarget?.innerText)
  }

  const handleOnBlurDropdown = (e: React.FocusEvent<HTMLInputElement>) => {
    const isShiftTabPressed = e.relatedTarget?.classList.contains('yext-dropdown-item')

    if (!e.target?.nextElementSibling && !isShiftTabPressed) {
      searchInputRef?.current?.focus()
    }
  }

  const handleOnKeyDownSearchInput = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      setSearchInput('')
      handleCloseSearchInput?.(e)
    }
  }

  const closeSuggestion = () => {
    setIsOpen(false)
    setIsUserTyping(false)
  }

  const clearInputField = () => {
    searchInputRef?.current?.focus()
    setSearchInput('')
    setIsUserTyping(false)
  }

  const formatSearchResults = (str: any, substr: string) => {
    const notMatchedText = `<span className='result-not-matched-text'>${substr}</span>`
    return `<span className='result-matched-text'>${str.replaceAll(substr, notMatchedText)}</span>`
  }

  const searchRef = useClickAwayListener(closeSuggestion)

  return (
    <div
      data-testid={`yext-search`}
      className={`yext-search ${searchstyle}style ${className}`}
      ref={searchRef}
      onFocus={() => {
        if (dropdownItems.length === 0) {
          setIsOpen(false)
        } else {
          setIsOpen(true)
        }
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        searchInput ? setIsUserTyping(true) : setIsUserTyping(false)
      }}
      onKeyDown={handleOnKeyDownSearchInput}
    >
      <RBDropdown show={isOpen} className='input-group'>
        <div className={`rounded-pill yext-search-input ${searchstyle}style responsive-search-input`}>
          <div className='input-group-prepend'>
            {/* TODO: read altText from AEM */}
            <img src={searchmagnifyingglassicon} className='cox-icon' alt={'search'} />
          </div>

          <input
            className={`search-input border-0 yext-focus w-100 ${searchstyle}style`}
            type='search'
            value={searchInput}
            //TODO : read place holder from AEM
            placeholder={placeholder}
            id={id}
            aria-label='search-input'
            onChange={handleChange}
            onKeyDown={handleKeypress}
            autoComplete='off'
            ref={searchInputRef}
          />

          <button
            type='reset'
            className={`${isUserTyping ? '' : 'd-none'} input-group-append`}
            onClick={() => clearInputField()}
          >
            {/* TODO: read altText from AEM */}
            <img src={searchclosex} className='cox-icon' alt={'reset'} />
          </button>

          <RBDropdown.Menu data-testid={'dropdown-menu'} className={'responsive-dropdown-menu border-0'}>
            {dropdownItems.map(({ value }: YextSearchResultType) => {
              /**value is auto complete options  */
              /**item value is search input match results  */
              const itemValue = formatSearchResults(value, searchInput)
              return (
                <RBDropdown.Item
                  key={cleanQueryInput(value)}
                  className={'yext-dropdown-item'}
                  onClick={(value) => handleOnClick(value)}
                  onBlur={handleOnBlurDropdown}
                >
                  {searchInput === '' ? value : <RichText text={itemValue} />}
                </RBDropdown.Item>
              )
            })}
          </RBDropdown.Menu>
        </div>
      </RBDropdown>
    </div>
  )
}

export default YextSearch
