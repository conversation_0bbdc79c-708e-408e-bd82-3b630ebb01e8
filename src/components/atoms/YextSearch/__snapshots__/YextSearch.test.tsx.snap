// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`YextSearch component Matches DOM Snapshot 1`] = `
<div
  className="yext-search whitestyle undefined"
  data-testid="yext-search"
  onFocus={[Function]}
  onKeyDown={[Function]}
>
  <div
    className="input-group dropdown"
  >
    <div
      className="rounded-pill yext-search-input whitestyle responsive-search-input"
    >
      <div
        className="input-group-prepend"
      >
        <img
          alt="search"
          className="cox-icon"
          src="/content/dam/cox/common/icons/ui_components/magnifying-glass.svg"
        />
      </div>
      <input
        aria-label="search-input"
        autoComplete="off"
        className="search-input border-0 yext-focus w-100 whitestyle"
        id="search"
        onChange={[Function]}
        onKeyDown={[Function]}
        placeholder="Search Cox.com"
        type="search"
        value=""
      />
      <button
        className="d-none input-group-append"
        onClick={[Function]}
        type="reset"
      >
        <img
          alt="reset"
          className="cox-icon"
          src="/content/dam/cox/common/icons/ui_components/x.svg"
        />
      </button>
    </div>
  </div>
</div>
`;
