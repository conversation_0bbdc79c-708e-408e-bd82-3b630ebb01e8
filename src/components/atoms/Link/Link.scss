@import './../../../assets/styles/v3/globalStyles.scss';

.link__container {
  display: flex;
  align-items: center;
  .link__anchor {
    text-decoration: none;
    display: flex;
    gap: var(--spacer-200);
    align-items: center;

    .link__icon {
      display: flex;
      align-items: center;
      justify-content: left;
    }
    @include link-text;
    color: var(--link-color-text-default);
    &:hover {
      color: var(--link-color-text-hover);
    }
    &.link__anchor--disabled {
      color: var(--link-color-text-disabled);
      cursor: not-allowed;
    }
  }

  .link__icon {
    height: var(--sizing-600);
    width: var(--sizing-600);
  }
}
