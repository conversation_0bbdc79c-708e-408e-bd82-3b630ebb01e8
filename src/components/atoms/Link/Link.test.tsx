import { render, screen } from '@testing-library/react'

import Link from './Link'

describe('Link component', () => {
  const baseProps = {
    linkText: 'Google',
    href: 'https://google.com',
    linkTextIconValue: 'icon.png',
    linkIconAltTextValue: 'link icon',
    target: '_blank'
  }

  it('renders link text and href', () => {
    render(<Link {...baseProps} />)
    const link = screen.getByRole('link', { name: 'Google' })
    expect(link).toHaveAttribute('href', 'https://google.com')
    expect(link).toHaveAttribute('target', '_blank')
  })

  it('renders icon as a clickable link', () => {
    render(<Link {...baseProps} />)
    const links = screen.getAllByRole('link')
    expect(links.length).toBe(1)
    const icon = screen.getByTestId('link-icon')
    expect(icon).toBeInTheDocument()
    expect(icon.tagName).toBe('IMG')
  })

  it('renders disabled link when disabledLink is true', () => {
    render(<Link {...baseProps} disabledLink={true} />)
    const link = screen.getByRole('link', { name: 'Google' })
    expect(link).toHaveClass('link__anchor--disabled')
  })

  it('applies correct test ID to container', () => {
    render(<Link {...baseProps} />)
    const container = screen.getByTestId('link-container')
    expect(container).toBeInTheDocument()
  })
})
