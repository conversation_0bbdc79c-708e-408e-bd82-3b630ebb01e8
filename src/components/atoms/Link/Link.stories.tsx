import type { <PERSON>a, StoryObj } from '@storybook/react'

import Link from './Link'

const meta: Meta<typeof Link> = {
  title: 'atoms/Link',
  component: Link,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
}

export default meta
type Story = StoryObj<typeof Link>

export const LinkWithIcon: Story = {
  args: {
    linkText: 'Active link with icon',
    href: 'https://google.com',
    linkTextIconValue: 'https://test.cox.com/content/dam/cox/common/icons/ui_components/cart.svg',
    target: '_blank'
  }
}

export const LinkWithoutIcon: Story = {
  args: {
    linkText: 'Active link without icon',
    href: 'https://google.com',
    target: '_blank'
  }
}

export const DisabledLink: Story = {
  args: {
    linkText: 'Disabled link',
    href: 'https://google.com',
    target: '_blank',
    disabledLink: true
  }
}
