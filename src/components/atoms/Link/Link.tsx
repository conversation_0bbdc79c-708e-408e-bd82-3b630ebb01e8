import { LinkProps } from './types'
import './Link.scss'
import { getImageSrc } from '../../../utils/local-util'

const Link = ({ linkText, href, linkTextIconValue, target = '_self', disabledLink = false }: LinkProps) => {
  if (!linkText) return null

  const anchorProps = disabledLink
    ? {
        role: 'link',
        'aria-disabled': true,
        tabIndex: -1,
        className: `link__anchor link__anchor--disabled`,
        onClick: (e: React.MouseEvent) => e.preventDefault()
      }
    : {
        href: href,
        target: target,
        className: `link__anchor`
      }
  return (
    <div className='link__container' data-testid='link-container'>
      <a {...anchorProps}>
        {linkText}
        {linkTextIconValue && (
          <img src={getImageSrc(linkTextIconValue)} alt='' className='link__icon' data-testid='link-icon' />
        )}
      </a>
    </div>
  )
}

export default Link
