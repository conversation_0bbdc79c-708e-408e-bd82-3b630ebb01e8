import './SectionHeader.scss'
import { SectionHeaderContent, SectionHeaderProps } from './types'
import RichText from '../RichText'
import { Skeleton } from '../Skeleton'

const SectionHeader = (props: SectionHeaderProps) => {
  const {
    eyebrow,
    title,
    description,
    rootClass = '',
    rootDataTestId = 'section-header',
    loading = false,
    tokenProperties = '',
    hideMCP = false,
    isInEditor = false,
    id,
    className = ''
  } = props
  const dataTestID = id || rootDataTestId
  const getWidthOfSkeleton = (content: string) => {
    if (content === 'eyebrow') {
      return 200
    } else if (content === 'title') {
      return 400
    } else {
      return 300
    }
  }
  const handleRendering = (content: SectionHeaderContent, element: string) => {
    const { isRTE = false } = content
    if (loading) {
      return <Skeleton shape='text' width={getWidthOfSkeleton(element)} />
    } else if (isRTE) {
      return (
        <RichText
          text={content?.value}
          isParsed={content?.isParsed}
          className={content?.className}
          tokenProperties={tokenProperties}
        />
      )
    } else {
      return content?.value
    }
  }
  const eyebrowClass = eyebrow?.containerClass ?? (eyebrow?.additionalClass ?? '') + ' main-eyebrow'
  const titleClass = title?.containerClass ?? (title?.additionalClass ?? '') + ' main-heading'
  const descriptionClass = description?.containerClass ?? (description?.additionalClass ?? '') + ' main-description'

  // Checks if hideMCP flag is true and that display is in dispatcher, not in author:
  const mcpClass = hideMCP && isInEditor == false ? 'd-none' : ''

  if (eyebrow?.value || title?.value || description?.value) {
    return (
      <div className={`${rootClass} section-header-container ${mcpClass} ${className}`} data-testid={dataTestID} id={id}>
        {eyebrow?.value && (
          <div className={eyebrowClass} data-testid={`${dataTestID}-eyebrow`}>
            {handleRendering(eyebrow, 'eyebrow')}{' '}
          </div>
        )}
        {title?.value && (
          <div className={titleClass} data-testid={`${dataTestID}-title`}>
            {handleRendering(title, 'title')}{' '}
          </div>
        )}
        {description?.value && (
          <div className={descriptionClass} data-testid={`${dataTestID}-description`}>
            {handleRendering(description, 'description')}{' '}
          </div>
        )}
      </div>
    )
  }
  return <></>
}

export default SectionHeader
