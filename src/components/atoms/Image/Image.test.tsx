import React from 'react'

import { fireEvent, render, screen } from '@testing-library/react'
import renderer from 'react-test-renderer'

import Image from './Image'
import { ImageLinkTypes } from './types'
import { GLOBAL_COX_LOGO } from '../../../assets/images'
//import { render } from 'react-dom'
jest.useFakeTimers()
describe('Image component', () => {
  test('renders Image component', () => {
    render(<Image id='img-test' src={GLOBAL_COX_LOGO} alt='alt-text' />)
    expect(screen.getByTestId('img-test')).toBeInTheDocument()
    expect(screen.getByAltText('alt-text')).toBeInTheDocument()
  })
  test('renders Image icon', () => {
    render(<Image id='img-test' src={GLOBAL_COX_LOGO} asIcon />)
    expect(screen.getByTestId('img-test')).toBeTruthy()
  })
  test('renders Image as Background', () => {
    render(<Image id='img-test' src={GLOBAL_COX_LOGO} asBG alt='title-text' />)
    expect(screen.getByTestId('img-test')).toBeInTheDocument()
    expect(screen.getByTestId('img-test')).toHaveStyle({
      backgroundImage: `url("${GLOBAL_COX_LOGO}")`
    })
    expect(screen.getByTitle('title-text')).toBeInTheDocument()
  })
  test('renders Image with Caption', () => {
    const caption = 'This is cox logo'
    render(<Image id='img-page' src={GLOBAL_COX_LOGO} caption={caption} />)
    expect(screen.getByTestId('img-page')).toBeInTheDocument()
    expect(screen.getByText(caption)).toBeInTheDocument()
  })
  test('renders Image with Link', () => {
    const linkUrl = 'https://www.cox.com/residential/home.html'
    render(<Image id='img-page' src={GLOBAL_COX_LOGO} linkId='img-link' linkUrl={linkUrl} linkType={ImageLinkTypes.PAGE} />)
    expect(screen.getByTestId('img-page')).toBeInTheDocument()
    expect(screen.getByTestId('image-anchor-img-link')).toBeInTheDocument()
    expect(screen.getByTestId('image-anchor-img-link')).toHaveAttribute('href', linkUrl)
  })
  test('renders Image on Load', () => {
    const mockLoad = jest.fn()
    render(<Image id='img-test' src={GLOBAL_COX_LOGO} onLoad={mockLoad} />)
    const img = screen.getByTestId('img-test')
    Object.defineProperty(img, 'clientWidth', { configurable: true, value: 100 })
    fireEvent.load(img)
    jest.advanceTimersByTime(200)
    expect(mockLoad).toHaveBeenCalled()
  })
  test('renders Image component with modal type', () => {
    //spy on useRef to cover  modal and chattype
    const useRefSpy = jest.spyOn(React, 'useRef').mockReturnValue({ current: { parentNode: { clientWidth: 100 } } })
    render(<Image id='img-test' src={GLOBAL_COX_LOGO} linkType='modal' />)
    expect(screen.getByTestId('image-anchor-img-test')).toHaveAttribute('data-link-type', 'modal')
    useRefSpy.mockRestore()
  })
  test('renders Image component with chat type', () => {
    //spy on useRef to cover  modal and chattype
    const useRefSpy = jest.spyOn(React, 'useRef').mockReturnValue({ current: { parentNode: { clientWidth: 100 } } })
    render(<Image id='img-test' src={GLOBAL_COX_LOGO} linkType='chat' />)
    expect(screen.getByTestId('img-test').classList.contains('chat')).toBeTruthy()
    useRefSpy.mockRestore()
  })
  test('renders Image component with accoridon type', () => {
    //spy on useRef to cover  modal and chattype
    const useRefSpy = jest.spyOn(React, 'useRef').mockReturnValue({ current: { parentNode: { clientWidth: 100 } } })
    render(<Image id='img-test' src={GLOBAL_COX_LOGO} linkType='accordion' linkId='img-test' />)
    expect(screen.getByTestId('image-anchor-img-test')).toHaveAttribute('aria-expanded', 'false')
    expect(screen.getByTestId('image-anchor-img-test')).toHaveAttribute('aria-controls', 'img-test')
    useRefSpy.mockRestore()
  })
  it('Matches DOM Snapshot', () => {
    const domTree = renderer
      .create(
        <Image
          id='img-page'
          src={GLOBAL_COX_LOGO}
          caption='This is cox logo'
          linkId='img-link'
          linkUrl='https://www.cox.com/residential/home.html'
        />
      )
      .toJSON()
    expect(domTree).toMatchSnapshot()
  })
})
afterAll(() => {
  jest.clearAllTimers()
})
