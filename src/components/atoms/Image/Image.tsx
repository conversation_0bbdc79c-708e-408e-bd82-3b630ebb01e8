import { useEffect, useRef } from 'react'

import { ImageAlignmentClasses, ImageLinkTypes, ImageProps } from './types'
import { handleChatInfo, useOliverChat } from '../../../hooks/useOliverChat'
import { handleModal } from '../../../utils/rte-util'
import RichText from '../RichText'
import './Image.scss'

const Image = (props: ImageProps): JSX.Element => {
  const {
    id = '',
    alt = '',
    src = '',
    lob = '',
    caption = undefined,
    displayPopupTitle = false,
    height = '',
    width = '',
    linkCss = '',
    removeRadius = false,
    linkId = '',
    linkType = '',
    linkUrl = '',
    linkAlt = '',
    openInNewTab = false,
    onLoad = false,
    className = '',
    imgCss = '',
    asIcon = false,
    asBG = false,
    ariaLabel = '',
    enableFocus = false,
    chatText = '',
    alignmentClass = ImageAlignmentClasses.DEFAULT
  } = props
  const target = openInNewTab ? '_blank' : '_self'
  const imageRef = useRef<any>(null)
  const { isChatLoaded, options } = useOliverChat()
  const borderRadius = removeRadius === true ? '' : 'bordered'
  const anchorAddlProps: any = {
    'data-link-type': linkType === ImageLinkTypes.ACCORDION ? ImageLinkTypes.COLLAPSE : linkType
  }
  if (linkType === ImageLinkTypes.ACCORDION || linkType === ImageLinkTypes.TAB) {
    anchorAddlProps['aria-expanded'] = 'false'
    anchorAddlProps['aria-controls'] = linkId
  }
  if (linkType === ImageLinkTypes.PAGE) {
    anchorAddlProps.target = target
  }

  useEffect(() => {
    const imgContainer = imageRef?.current
    if (linkType === ImageLinkTypes.MODAL) {
      if (imgContainer) {
        imgContainer?.parentNode?.addEventListener('click', handleModal, false)
      }
    } else if (linkType === ImageLinkTypes.CHAT) {
      handleChatInfo(imgContainer, options, isChatLoaded, chatText)
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    enableFocus && (imgContainer.tabIndex = 0)
  }, [linkType, isChatLoaded])

  const onLoadHandler = () => {
    if (!onLoad) return //see the definition of onLoad
    //need to wait till the image is fully loaded
    const waitForImageLoad = setInterval(() => {
      const width = imageRef?.current?.clientWidth
      if (width > 0) {
        onLoad(imageRef)
        clearInterval(waitForImageLoad)
      }
    }, 50)
  }

  const getImage = () => {
    return (
      <img
        src={src}
        alt={alt ?? ''}
        height={height}
        width={width}
        className={`${borderRadius} ${imgCss} ${linkType === ImageLinkTypes.CHAT ? 'chat' : ''}`}
        id={id}
        ref={imageRef}
        onLoad={onLoadHandler}
        aria-label={ariaLabel || alt}
        data-testid={id}
      />
    )
  }
  const getBgImage = () => {
    return (
      <div
        style={{
          backgroundImage: `url("${src}")`
        }}
        title={alt ?? ''}
        className={`bg-image ${imgCss}`}
        ref={imageRef}
        onLoad={onLoadHandler}
        aria-label={ariaLabel || alt}
        data-testid={id}
        role='img'
      ></div>
    )
  }
  if (asIcon) {
    return getImage()
  }
  if (asBG) {
    return getBgImage()
  }
  const getCaption = () => {
    return (
      caption &&
      !displayPopupTitle && (
        <RichText
          id={`image-caption-${id}`}
          className='image-caption cox-text-paragraph3-regular'
          text={caption}
          isParsed
        ></RichText>
      )
    )
  }

  return (
    <div className={`image-container ${className} ${lob}`} id={id} data-testid={`image-container-${id}`}>
      <div className={`wrap-caption ${alignmentClass}`}>
        {linkType && linkType !== ImageLinkTypes.CHAT ? (
          <a
            href={linkUrl}
            title={linkAlt as any}
            className={linkCss}
            id={`image-anchor-${linkId || id}`}
            data-testid={`image-anchor-${linkId || id}`}
            {...anchorAddlProps}
          >
            {getImage()}
          </a>
        ) : (
          getImage()
        )}
        {getCaption()}
      </div>
    </div>
  )
}

export default Image
