import './Accordion.scss'
import { ACCORDION_VARIATION, AccordionItemType, AccordionProps, ItemProp, ItemType } from './types'
import { CHEVRON_DOWN_RIVER_BLUE, SM_CHEVRON_DOWN_RIVER_BLUE } from '../../../assets/images'
import { getImageSrc } from '../../../utils/local-util'
import { RBAccordion } from '../../shared/ReactBootstrap'
import RichText from '../RichText'

const Accordion = ({
  accordionIcon,
  accordionItems = [],
  alwaysOpen = false,
  defaultActiveKey = [],
  variation = ACCORDION_VARIATION.BASE,
  renderTitle,
  renderBody,
  handleHeaderClick,
  id = '',
  as = 'h3',
  className = '',
  enableFAQs = false
}: AccordionProps): JSX.Element => {
  const getAccordionChevronIcon = () => {
    if (accordionIcon) return CHEVRON_DOWN_RIVER_BLUE
    else if (variation == ACCORDION_VARIATION.PSU) return SM_CHEVRON_DOWN_RIVER_BLUE
    else return CHEVRON_DOWN_RIVER_BLUE
  }

  const enableFAQsPageElement = enableFAQs && {
    itemScope: true,
    itemType: ItemType.PAGE
  }

  const enableFAQsQuestionElement = enableFAQs && {
    itemScope: true,
    itemProp: ItemProp.MAIN_ENTITY,
    itemType: ItemType.QUESTION
  }

  const enableFAQsNameElement = enableFAQs && {
    itemProp: ItemProp.NAME
  }

  const enableFAQsAnswerElement = enableFAQs && {
    itemScope: true,
    itemProp: ItemProp.ACCEPTED_ANSWER,
    itemType: ItemType.ANSWER
  }

  const enableFAQsTextElement = enableFAQs && {
    itemProp: ItemProp.TEXT
  }

  const getAccordionTitle = (accordionItem: AccordionItemType) => {
    if (variation == ACCORDION_VARIATION.CUSTOM) {
      const title = renderTitle && renderTitle(accordionItem)
      return <>{title}</>
    } else if (variation == ACCORDION_VARIATION.PSU) {
      return (
        <div className='accordion-title-section'>
          {accordionItem.accordionIcon && (
            <img
              className='accordion-title-icon'
              src={accordionItem.accordionIcon}
              alt={accordionItem.accordionIconAlttxt}
            />
          )}
          <p className={`psu-accordion-title`}>
            <span className='title1'>{accordionItem.title1}</span>
            {accordionItem.title2 && <RichText text={accordionItem.title2} />}
          </p>
        </div>
      )
    } else
      return (
        <div className='accordion-title' {...enableFAQsNameElement}>
          {accordionItem.title && <RichText text={accordionItem.title} />}
        </div>
      )
  }

  const getAccordionBody = (accordionItem: AccordionItemType) => {
    if (variation == ACCORDION_VARIATION.CUSTOM) {
      const body = renderBody && renderBody(accordionItem)
      return <>{body}</>
    } else if (variation == ACCORDION_VARIATION.PSU) {
      return (
        <div className='bullet-points-list'>
          {accordionItem.bulletPoints?.map((bulletPoint, index) => (
            <div className='bullet-point-item' key={`bullet-point-item-${index + 1}`}>
              <div className='bullet-point-icon-frame'>
                {accordionItem.bulletIcon && (
                  <img className='bullet-point-icon' src={accordionItem.bulletIcon} alt={`bullet-point-icon-${index + 1}`} />
                )}
              </div>
              <p className='bullet-point-text'>{bulletPoint}</p>
            </div>
          ))}
        </div>
      )
    } else {
      return (
        <div {...enableFAQsTextElement}>{accordionItem.description && <RichText text={accordionItem.description} />}</div>
      )
    }
  }

  return (
    <div
      className={`accordion-container accordion-variation-${variation} ${className}`}
      data-testid={`accordion-container`}
      id={id}
    >
      <RBAccordion
        alwaysOpen={alwaysOpen}
        className={`accordion-items`}
        defaultActiveKey={defaultActiveKey}
        flush
        {...enableFAQsPageElement}
      >
        {accordionItems.map((accordionItem: AccordionItemType, index) => (
          <RBAccordion.Item
            eventKey={`accordion-item-${index + 1}`}
            key={`accordion-item-${index + 1}`}
            {...enableFAQsQuestionElement}
          >
            <RBAccordion.Header
              as={as}
              key={`accordion-item-header-${index + 1}`}
              onClick={() => handleHeaderClick && handleHeaderClick()}
            >
              <a role='none'>{getAccordionTitle(accordionItem)}</a>
              <img
                className={`accordion-chevron ${variation}-chevron`}
                src={getImageSrc(getAccordionChevronIcon())}
                alt={accordionItem.accordionIconAlttxt}
              />
            </RBAccordion.Header>
            <div className='accordion-item-container'>
              <RBAccordion.Body {...enableFAQsAnswerElement}>{getAccordionBody(accordionItem)}</RBAccordion.Body>
              <div className='divider'></div>
            </div>
          </RBAccordion.Item>
        ))}
      </RBAccordion>
    </div>
  )
}

export default Accordion
