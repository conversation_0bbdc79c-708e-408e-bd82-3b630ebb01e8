export type AccordionProps = {
  /** Custom accordion icon can be specified */
  accordionIcon?: string
  /** An array of the accordion items */
  accordionItems: AccordionItemType[]
  /** Make accordion items stay open when another item is opened by setting to true*/
  alwaysOpen?: boolean
  /** Custom classes can be specified */
  className?: string
  /** The default active keys are expanded on start, it can be a string for one active accordion item or string[] for multiple */
  defaultActiveKey?: any
  /** Variation of accordion: base, psu, etc. */
  variation?: string
  /** Function specifying action on click of accordion header */
  handleHeaderClick?: () => void
  /** Id should be unique if specified  */
  id?: string
  /** Set a custom element for this component */
  as?: any /** Custom accordion title render function */
  renderTitle?: (obj: any) => any
  /** Custom accordion body render function */
  renderBody?: (obj: any) => any
  /** Enable FAQs - to add Google Pages-appropriate schema tagging */
  enableFAQs?: boolean
}

export type AccordionItemType = {
  /** Accordion title icon */
  accordionIcon?: string
  /** Alt text for accordion title icon */
  accordionIconAlttxt?: string
  /** Title text for the base accordion - this is shown at all times */
  title?: string
  /** Primary title for fragments accordion */
  title1?: string
  /** Secondary title for fragments accordion */
  title2?: string
  /** Description is the text shown on toggle */
  description?: string
  /** Icon for bullet points */
  bulletIcon?: string
  /** Bullet point array of texts */
  bulletPoints?: string[]
}

export enum ACCORDION_VARIATION {
  BASE = 'base',
  PSU = 'psu',
  CUSTOM = 'custom'
}

export enum ItemType {
  PAGE = 'https://schema.org/FAQPage',
  QUESTION = 'https://schema.org/Question',
  ANSWER = 'https://schema.org/Answer'
}

export enum ItemProp {
  MAIN_ENTITY = 'mainEntity',
  NAME = 'name',
  ACCEPTED_ANSWER = 'acceptedAnswer',
  TEXT = 'text'
}
