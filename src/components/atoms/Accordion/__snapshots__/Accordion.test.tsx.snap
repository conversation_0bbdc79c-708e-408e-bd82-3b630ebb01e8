// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Accordion component matches DOM Snapshot 1`] = `
<div
  className="accordion-container accordion-variation-base "
  data-testid="accordion-container"
  id=""
>
  <div
    className="accordion-items accordion accordion-flush"
  >
    <div
      className="accordion-item"
    >
      <h3
        className="accordion-header"
      >
        <button
          aria-expanded={false}
          className="accordion-button collapsed"
          onClick={[Function]}
          type="button"
        >
          <a
            role="none"
          >
            <div
              className="accordion-title"
            >
              <div
                className="word-wrap rte  "
                data-testid="rich-text-container"
                id="rich-text"
              >
                Can I get Cox Mobile if I don't have other Cox services?
              </div>
            </div>
          </a>
          <img
            className="accordion-chevron base-chevron"
            src="/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg"
          />
        </button>
      </h3>
      <div
        className="accordion-item-container"
      >
        <div
          aria-expanded={null}
          className="accordion-collapse collapse"
        >
          <div
            className="accordion-body"
          >
            <div>
              <div
                className="word-wrap rte  "
                data-testid="rich-text-container"
                id="rich-text"
              >
                You must have Cox Internet, enroll in EasyPay and enroll in paperless billing to be eligible for Cox Mobile.
              </div>
            </div>
          </div>
        </div>
        <div
          className="divider"
        />
      </div>
    </div>
    <div
      className="accordion-item"
    >
      <h3
        className="accordion-header"
      >
        <button
          aria-expanded={false}
          className="accordion-button collapsed"
          onClick={[Function]}
          type="button"
        >
          <a
            role="none"
          >
            <div
              className="accordion-title"
            >
              <div
                className="word-wrap rte  "
                data-testid="rich-text-container"
                id="rich-text"
              >
                What's the easiest way to place an order for a phone or Cox Mobile cell plan?
              </div>
            </div>
          </a>
          <img
            className="accordion-chevron base-chevron"
            src="/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg"
          />
        </button>
      </h3>
      <div
        className="accordion-item-container"
      >
        <div
          aria-expanded={null}
          className="accordion-collapse collapse"
        >
          <div
            className="accordion-body"
          >
            <div>
              <div
                className="word-wrap rte  "
                data-testid="rich-text-container"
                id="rich-text"
              >
                Placing an order online is the easiest way to get started with Cox Mobile. If you prefer an in-store experience, you can always use our store locator to find a Cox Store location near you.
              </div>
            </div>
          </div>
        </div>
        <div
          className="divider"
        />
      </div>
    </div>
  </div>
</div>
`;
