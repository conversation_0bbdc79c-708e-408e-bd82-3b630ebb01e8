import React from 'react'

import type { Meta, StoryObj } from '@storybook/react'

import Accordion from './Accordion'
import { ACCORDION_VARIATION } from './types'

const meta: Meta<typeof Accordion> = {
  title: 'atoms/Accordion',
  component: Accordion,
  parameters: {
    layout: 'fullscreen'
  },
  tags: ['autodocs'],
  argTypes: {
    accordionItems: [],
    alwaysOpen: {
      control: 'boolean'
    },
    defaultActiveKey: {
      control: 'text'
    }
  }
}

export default meta
type Story = StoryObj<typeof Accordion>

const sampleBaseAccordionItems = [
  {
    title: 'Does my Cox Mobile cell phone plan include unlimited calling and texting?',
    description:
      '<p>Yes, both our plans include unlimited SMS/MMS and calling. </p><br/ ><p><span class="cox-text-paragraph5-regular">Other restrictions apply.</span></p>'
  },
  {
    title: 'Can I get Cox wifi if I don’t have test other Cox services?',
    description:
      'You must have <a href="#">Cox Wifi</a>, enroll in EasyPay and enroll in paperless billing to be eligible for Cox Mobile.'
  },
  {
    title: 'What’s the easiest way to place an order for a phone or Cox Mobile cell plan?',
    description:
      'Placing an order online is the easiest way to get started with Cox Mobile. If you prefer an in-store experience, you can always use our store locator to find a Cox Store location near you.'
  },
  {
    title: 'Can I bring my own phone number when purchasing a new wireless phone plan through Cox Mobile?',
    description:
      "Yes. We accept phone numbers from all U.S. carriers, so it's easy to transfer your number when you sign up."
  },
  {
    title: "Yes. We accept phone numbers from all U.S. carriers, so it's easy to transfer your number when you sign up.",
    description: 'Yes, where available. Requires a 5G-capable device.'
  },
  {
    title: 'Does Cox Mobile have the best cell phone coverage in my area?',
    description:
      'Cox Mobile offers Nationwide 4G LTE and 5G on compatible phones, where available. See our coverage map for your area.'
  }
]

const samplePSUAccordionItems = [
  {
    title1: 'Primary Title',
    title2: '<span>Secondary Title</span>',
    accordionIcon: 'https://assets.cox.com/is/image/coxstage/leop1709?$carousel-product$',
    accordionIconAlttxt: 'leopard',
    bulletIcon: 'https://assets.cox.com/is/image/coxstage/scenery?$carousel-product$',
    bulletPoints: [
      'Character count 50. Lorem ipsum 1',
      'Character count 50. Lorem ipsum 2',
      'Character count 50. Lorem ipsum 3'
    ]
  },
  {
    title1: 'Primary Title',
    title2: '<span>Secondary Title</span>',
    accordionIcon: 'https://assets.cox.com/is/image/coxstage/lndscp?$carousel-product$',
    accordionIconAlttxt: 'landscape',
    bulletIcon: 'https://assets.cox.com/is/image/coxstage/scenery?$carousel-product$',
    bulletPoints: [
      'Character count 50. Lorem ipsum 1',
      'Character count 50. Lorem ipsum 2',
      'Character count 50. Lorem ipsum 3'
    ]
  },
  {
    title1: 'No image',
    title2: '<span>Secondary Title</span>',
    bulletIcon: '',
    bulletPoints: ['No icon 1', 'No icon 2', 'No icon 3']
  },
  {
    title1: 'Only Title1 ',
    accordionIcon: 'https://assets.cox.com/is/image/coxstage/mntns?$carousel-product$',
    accordionIconAlttxt: 'mountain',
    bulletPoints: ['No icon 1', 'No icon 2', 'No icon 3']
  }
]

const sampleCustomAccordionItems = [
  {
    title1: 'Primary Title 1',
    title2: 'Secondary Title 1',
    description: 'Character count 50. Lorem ipsum.'
  },
  {
    title1: 'Primary Title 2',
    title2: 'Secondary Title 2',
    description: 'Character count 50. Lorem ipsum.'
  },
  {
    title1: 'Primary Title 3',
    title2: 'Secondary Title 3',
    description: 'Character count 50. Lorem ipsum.'
  }
]

const sampleRenderTitle = (accordionItem: any) => {
  return (
    <p className='custom-accordion-title'>
      <span className='title1'>{accordionItem.title1}</span>
      {accordionItem.title2 && (
        <>
          {' '}
          - <span className='title2'>{accordionItem.title2}</span>
        </>
      )}
    </p>
  )
}

const sampleRenderBody = (accordionItem: any) => {
  return (
    <div className='custom-accordion-title'>
      <p>{accordionItem.description}</p>
    </div>
  )
}

export const BaseAccordion: Story = {
  args: {
    variation: ACCORDION_VARIATION.BASE,
    accordionItems: sampleBaseAccordionItems
  }
}

export const AlwaysOpen: Story = {
  args: {
    accordionItems: sampleBaseAccordionItems,
    alwaysOpen: true
  }
}

export const DefaultKeySelected: Story = {
  args: {
    accordionItems: sampleBaseAccordionItems,
    defaultActiveKey: [
      'Can I get Cox Mobile if I don’t have other Cox services?',
      'Can I bring my own phone number when purchasing a new wireless phone plan through Cox Mobile?'
    ]
  }
}

export const PSUAccordion: Story = {
  args: {
    variation: ACCORDION_VARIATION.PSU,
    defaultActiveKey: 'accordion-item-1',
    accordionItems: samplePSUAccordionItems
  }
}

export const CustomPSUAccordion: Story = {
  args: {
    variation: ACCORDION_VARIATION.CUSTOM,
    accordionItems: sampleCustomAccordionItems,
    renderTitle: sampleRenderTitle,
    renderBody: sampleRenderBody
  }
}

export const EnableFAQsAccordion: Story = {
  args: {
    variation: ACCORDION_VARIATION.BASE,
    accordionItems: sampleBaseAccordionItems,
    enableFAQs: true
  }
}
