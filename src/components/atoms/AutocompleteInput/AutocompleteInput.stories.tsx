import type { Meta, StoryObj } from '@storybook/react'

import AutocompleteInput from './AutocompleteInput'

const meta: Meta<typeof AutocompleteInput> = {
  title: 'atoms/AutocompleteInput',
  component: AutocompleteInput,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
}

export default meta
type Story = StoryObj<typeof AutocompleteInput>

export const Special: Story = {
  args: {
    //variation: 'special',
    value: ''
  }
}
