import { ModalBodyProps } from './types'
import { RBModal } from '../../../shared/ReactBootstrap'
import RichText from '../../RichText'
import { ModalTypes } from '../types'

const ModalBody = (props: ModalBodyProps) => {
  const {
    headline = '',
    subhead = '',
    description = '',
    src = '',
    modalType = ModalTypes.custom,
    children,
    showFooter = true,
    isParsed = true,
    modalId = '',
    altImage = 'basic-modal-media'
  } = props
  const hasImage = modalType === ModalTypes.withImage

  if (modalType === ModalTypes.custom) {
    return (
      <RBModal.Body className={!showFooter ? 'bottom-corner' : ''}>
        <div className='basic-modal-body-wrapper custom-modal' data-testid={`${modalId}-${modalType}-modal`}>
          {children}
        </div>
      </RBModal.Body>
    )
  } else if (modalType === ModalTypes.video) {
    return (
      <RBModal.Body>
        <div className='video-modal-body' data-testid={`${modalId}-${modalType}-modal`}>
          {children}
        </div>
      </RBModal.Body>
    )
  } else {
    return (
      <RBModal.Body className={!showFooter ? 'bottom-corner' : ''}>
        <div className={`basic-modal-body-wrapper ${modalType}`} data-testid={`${modalId}-${modalType}-modal-wrapper`}>
          {hasImage && (
            <div className={`basic-modal-body-image ${modalType}`}>
              <img
                src={src}
                alt={altImage}
                className={'modal-bg-image'}
                data-testid={`${modalId}-${modalType}-modal-body-image`}
              />
            </div>
          )}
          {(headline || subhead || description) && (
            <div className={`basic-modal-body-content ${modalType}`}>
              {headline && (
                <div className='headline' data-testid={`${modalId}-${modalType}-headline`}>
                  {headline}
                </div>
              )}
              {subhead && (
                <p className='subhead' data-testid={`${modalId}-${modalType}-subhead`}>
                  {subhead}
                </p>
              )}
              {description && (
                <RichText
                  text={description as any}
                  isParsed={isParsed}
                  data-testid={`${modalId}-${modalType}-description`}
                />
              )}
            </div>
          )}
        </div>
      </RBModal.Body>
    )
  }

  // eslint-disable-next-line no-unreachable
  return <></>
}

export default ModalBody
