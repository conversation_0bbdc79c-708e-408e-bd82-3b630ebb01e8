import { ReactNode } from 'react'

import { ModalBodyProps } from './ModalBody/types'
import { ModalFooterProps } from './ModalFooter/types'
import { ModalHeaderProps } from './ModalHeader/types'

export interface ModalProps extends ModalBodyProps, ModalHeaderProps, ModalFooterProps {
  /**Specifies the Id of the modal */
  modalId: string
  /**Specifies the state of the modal whether it is open or close */
  show: boolean
  /**Specifies the component name from where the modal is triggered */
  componentName?: string
  /**Specifies the custom callback to handle close on backdrop click or escape key press */
  handleClose?: () => void
  /**Specifies the children (custom content pass from different components) of the modal */
  children?: ReactNode
  /**Specifies the custom callback to handle open on backdrop click or escape key press */
  handleOpen?: (e: any) => void
  /**handle modal responsive based on the content */
  responsive?: boolean
  /** When backdrop is set to "static", the modal will not close when clicking outside it*/
  backdrop?: boolean | 'static' | undefined
  /** Custom classes can be specified */
  customClassName?: string
}

export enum ModalTypes {
  withImage = 'withImage',
  withoutImage = 'withoutImage',
  custom = 'custom',
  video = 'video'
}
