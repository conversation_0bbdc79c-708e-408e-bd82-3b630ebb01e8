export type ModalFooterProps = {
  /**Specifies the state of the modal footer whether it will be shown or hidden */
  showFooter?: boolean
  /**Specifies the primary button text of the modal footer */
  primaryBtnText?: string
  /**Specifies the primary button link of the modal footer */
  primaryBtnLink?: string
  /**Specifies the custom callback function for primary button of the modal footer */
  primaryBtnClick?: () => void
  /**Specifies the secondary button text of the modal footer */
  secondaryBtnText?: string
  /**Specifies the secondary button link of the modal footer */
  secondaryBtnLink?: string
  /**Specifies the secondary button custom callback of the modal footer */
  secondaryBtnClick?: () => void
  /**modalId is required for automation testing */
  modalId: string
  /**Specifies modal type */
  modalType?: string
  /** Specifies if the primary button is meant to close the modal */
  primaryBtnisCloseBtn?: boolean
  /** Specifies if the secondary button is meant to close the modal */
  secondaryBtnisCloseBtn?: boolean
  /**Specifies the custom callback to handle the text on close button */
  handleClose?: () => void
}
