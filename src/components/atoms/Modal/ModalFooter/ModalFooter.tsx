import { RBModal } from '../../../shared/ReactBootstrap'
import Button, { ButtonTypes } from '../../Button'
import { ModalFooterProps } from '../ModalFooter/types'
import { ModalTypes } from '../types'

const ModalFooter = (props: ModalFooterProps): JSX.Element => {
  const {
    showFooter = true,
    primaryBtnText = '',
    primaryBtnLink = '',
    primaryBtnisCloseBtn = false,
    primaryBtnClick,
    secondaryBtnText = '',
    secondaryBtnLink = '',
    secondaryBtnisCloseBtn = false,
    secondaryBtnClick,
    modalId = '',
    modalType,
    handleClose
  } = props
  if (modalType === ModalTypes.video) {
    return <></>
  } else if (showFooter) {
    return (
      <>
        {(primaryBtnText || secondaryBtnText) && (
          <RBModal.Footer>
            {primaryBtnText && (
              <Button
                id={`${modalId}-primary-button`}
                text={primaryBtnText}
                buttonTypes={ButtonTypes.PRIMARY}
                linkUrl={primaryBtnLink}
                customClickEvent={primaryBtnisCloseBtn ? handleClose : primaryBtnClick}
                data-testid={`${modalId}-primary-button`}
              />
            )}
            {secondaryBtnText && (
              <Button
                id={`${modalId}-secondary-button`}
                text={secondaryBtnText}
                buttonTypes={ButtonTypes.SECONDARY}
                linkUrl={secondaryBtnLink}
                customClickEvent={secondaryBtnisCloseBtn ? handleClose : secondaryBtnClick}
                data-testid={`${modalId}-secondary-button`}
              />
            )}
          </RBModal.Footer>
        )}
      </>
    )
  }
  return <></>
}

export default ModalFooter
