import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'

import Modal from './Modal'
import { ModalTypes } from './types'

const meta: Meta<typeof Modal> = {
  title: 'atoms/Modal',
  component: Modal,
  tags: ['autodocs'],
  argTypes: {
    modalType: {
      control: 'select',
      options: ['withImage', 'withoutImage', 'custom']
    }
  }
}

export default meta
type Story = StoryObj<typeof Modal>

export const withImage: Story = {
  args: {
    show: true,
    modalType: ModalTypes.withImage,
    title: 'Offer details',
    showFooter: true,
    primaryBtnText: 'Primary Button',
    src: 'https://assets.cox.com/is/image/coxstage/google-pixel-8a-obsidian-lockup?$modal-basic$',
    headline: 'Get even more out of your high-speed home internet connection',
    subhead: 'Unlimited Data',
    description:
      '<p>Connect without fear of overage charges. Perfect for large families, working &amp; learning remotely, movie streamers, gamers, and controlling your smart home.</p><p>click <a href="/residential/internet.html" target="_blank" rel="noopener noreferrer">here</a> for more offers</p>',
    isParsed: false
  }
}

export const withoutImage: Story = {
  args: {
    show: true,
    modalType: ModalTypes.withoutImage,
    title: 'Offer details',
    showFooter: true,
    secondaryBtnText: 'Secondary Button',
    headline: 'Get even more out of your high-speed home internet connection',
    subhead: 'Unlimited Data',
    description:
      '<p>Connect without fear of overage charges. Perfect for large families, working &amp; learning remotely, movie streamers, gamers, and controlling your smart home.</p><p>click <a href="/residential/internet.html" target="_blank" rel="noopener noreferrer">here</a> for more offers</p>',
    isParsed: false
  }
}

const mockTemplate = `A Cox internet plan is required to qualify for Cox Mobile. No annual contract. No cancellation fees.`

export const Default: Story = {
  args: {
    show: true,
    modalType: ModalTypes.custom,
    title: 'Offer details',
    headline: 'Get even more out of your high-speed home internet connection',
    subhead: 'Unlimited Data',
    isParsed: false,
    description:
      '<p>Connect without fear of overage charges. Perfect for large families, working &amp; learning remotely, movie streamers, gamers, and controlling your smart home.</p><p>click <a href="/residential/internet.html" target="_blank" rel="noopener noreferrer">here</a> for more offers</p>',
    children: mockTemplate
  }
}
export const withFooterOnly: Story = {
  args: {
    show: true,
    modalType: ModalTypes.custom,
    title: 'Offer details',
    showFooter: true,
    showHeader: false,
    headline: 'Get even more out of your high-speed home internet connection',
    subhead: 'Unlimited Data',
    description:
      '<p>Connect without fear of overage charges. Perfect for large families, working &amp; learning remotely, movie streamers, gamers, and controlling your smart home.</p><p>click <a href="/residential/internet.html" target="_blank" rel="noopener noreferrer">here</a> for more offers</p>',
    children: mockTemplate,
    primaryBtnText: 'Submit',
    secondaryBtnText: 'Cancel',
    secondaryBtnisCloseBtn: true,
    isParsed: false,
    customClassName: 'footer-custom-class'
  }
}

export const WithHeaderOnly: Story = {
  args: {
    show: true,
    modalType: ModalTypes.custom,
    title: 'Offer details',
    showFooter: false,
    showHeader: true,
    headline: 'Get even more out of your high-speed home internet connection',
    subhead: 'Unlimited Data',
    description:
      '<p>Connect without fear of overage charges. Perfect for large families, working &amp; learning remotely, movie streamers, gamers, and controlling your smart home.</p><p>click <a href="/residential/internet.html" target="_blank" rel="noopener noreferrer">here</a> for more offers</p>',
    children: mockTemplate,
    isParsed: false
  }
}

export const WithNoHeaderAndFooter: Story = {
  args: {
    show: true,
    modalType: ModalTypes.custom,
    title: 'Offer details',
    showHeader: false,
    showFooter: false,
    headline: 'Get even more out of your high-speed home internet connection',
    subhead: 'Unlimited Data',
    isParsed: false,
    primaryBtnText: 'Submit',
    secondaryBtnText: 'Cancel',
    description:
      '<p>Connect without fear of overage charges. Perfect for large families, working &amp; learning remotely, movie streamers, gamers, and controlling your smart home.</p><p>click <a href="/residential/internet.html" target="_blank" rel="noopener noreferrer">here</a> for more offers</p>',
    children: mockTemplate
  }
}

export const WithStaticBackdrop: Story = {
  args: {
    show: true,
    modalType: ModalTypes.custom,
    title: 'Offer details',
    showHeader: true,
    showFooter: true,
    headline: 'Get even more out of your high-speed home internet connection',
    subhead: 'Unlimited Data',
    isParsed: false,
    primaryBtnText: 'Submit',
    secondaryBtnText: 'Cancel',
    secondaryBtnisCloseBtn: true,
    description:
      '<p>Connect without fear of overage charges. Perfect for large families, working &amp; learning remotely, movie streamers, gamers, and controlling your smart home.</p><p>click <a href="/residential/internet.html" target="_blank" rel="noopener noreferrer">here</a> for more offers</p>',
    children: mockTemplate,
    backdrop: 'static'
  }
}
