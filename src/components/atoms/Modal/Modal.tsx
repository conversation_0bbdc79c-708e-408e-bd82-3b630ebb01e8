import { useEffect, useState } from 'react'

import ModalBody from './ModalBody'
import Mo<PERSON>Footer from './ModalFooter'
import ModalHeader from './ModalHeader'
import { ModalProps } from './types'
import useLobExperience from '../../../hooks/useLobExperience'
import { getCacheItem, setCacheItem } from '../../../utils/local-storage-util'
import { RBModal } from '../../shared/ReactBootstrap'
import './Modal.scss'

const Modal = (props: ModalProps): JSX.Element => {
  const {
    modalId = '',
    show = false,
    componentName = '',
    handleClose,
    children,
    handleOpen = () => { },
    responsive = false,
    backdrop,
    customClassName = ''
  } = props
  const [showModal, setShowModal] = useState(show)
  const { themeClass } = useLobExperience()

  useEffect(() => {
    window.addEventListener('showModal', handleShowModal)
    return () => {
      window.removeEventListener('showModal', handleShowModal)
    }
  }, [])

  useEffect(() => {
    window.addEventListener('hideModal', handleHideModal)
    return () => {
      window.removeEventListener('hideModal', handleHideModal)
    }
  }, [])

  useEffect(() => {
    setShowModal(show)
    setCacheItem('activeModal', show !== undefined ? (show ? 'true' : 'false') : 'false')
  }, [show, setShowModal])

  const handleShowModal = (e: any) => {
    const isActiveModal = getCacheItem('activeModal') ?? false
    if (e?.detail?.modalId === modalId && isActiveModal === 'false') {
      setCacheItem('activeModal', 'true')
      setShowModal(e?.detail?.showModal)
      if (handleOpen) {
        handleOpen(e)
      }
    }
  }
  const closeModal = () => {
    if (handleClose) {
      handleClose()
    }
    setShowModal(false)
  }
  const handleOnExit = () => {
    setCacheItem('activeModal', 'false')
    setShowModal(false)
  }
  const handleHideModal = (e: any) => {
    if (e?.detail?.modalId === modalId && getCacheItem('activeModal')) {
      setCacheItem('activeModal', e?.detail?.showModal)
      setShowModal(e?.detail?.showModal)
    }
  }

  return (
    <RBModal
      id={modalId}
      show={showModal}
      onHide={closeModal}
      onExit={handleOnExit}
      dialogClassName={
        'basic-modal ' + componentName + ' ' + themeClass + (responsive ? ' responsive' : '') + ' ' + customClassName
      }
      data-testid={modalId}
      centered={true}
      onEscapeKeyDown={closeModal}
      backdrop={backdrop}
      animation={false}
    >
      <ModalHeader {...props} handleClose={closeModal} />
      <ModalBody {...props}>{children}</ModalBody>
      <ModalFooter {...props} handleClose={closeModal} />
    </RBModal>
  )
}

export default Modal
