import React from 'react'

import type { Meta, StoryObj } from '@storybook/react'

import { Skeleton } from './Skeleton'

const meta: Meta<typeof Skeleton> = {
  title: 'atoms/Skeleton',
  component: Skeleton,
  parameters: {
    docs: {
      description: {
        component: `
The **Skeleton** component provides a visual placeholder while content is loading.
        `.trim()
      }
    }
  },
  argTypes: {
    shape: {
      control: { type: 'select' },
      options: ['text', 'circle', 'rectangle']
    },
    width: {
      control: 'text'
    },
    height: {
      control: 'text'
    },
    onDark: {
      control: 'boolean'
    }
  }
}

export default meta

type Story = StoryObj<typeof Skeleton>

export const Text: Story = {
  args: {
    shape: 'text',
    width: '100%'
  },
  parameters: {
    docs: {
      description: {
        story: 'A simple rectangular skeleton, used for loading text. It has a default height of 1em.'
      }
    }
  }
}

export const Circle: Story = {
  args: {
    shape: 'circle',
    width: '56px',
    height: '56px'
  },
  parameters: {
    docs: {
      description: {
        story: 'Use the circle variant for loading avatars or round icons.'
      }
    }
  }
}

export const Rectangle: Story = {
  args: {
    shape: 'rectangle',
    width: '100%',
    height: '128px'
  },
  parameters: {
    docs: {
      description: {
        story:
          'A rectangular skeleton with custom width and height, which can be used to load images or other block elements.'
      }
    }
  }
}

export const OnDark: Story = {
  args: {
    shape: 'text',
    width: '100%',
    onDark: true
  },
  render: (args) => (
    <div style={{ background: '#222', padding: '2rem' }}>
      <Skeleton {...args} />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Skeletons can be used on dark colored backgrounds by using the `onDark` prop.'
      }
    }
  }
}

export const LoadingExample: Story = {
  render: () => (
    <div style={{ width: '300px' }}>
      <Skeleton shape='rectangle' width='100%' height='100px' />
      <Skeleton shape='text' width='100%' />
      <Skeleton shape='text' width='100%' />
      <Skeleton shape='text' width='80%' />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Use a combination of skeleton shapes to create a loading state for your components.'
      }
    }
  }
}
