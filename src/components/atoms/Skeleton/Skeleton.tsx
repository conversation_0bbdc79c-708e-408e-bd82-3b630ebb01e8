import React from 'react'

import './Skeleton.scss'
import { SkeletonProps } from './types'

export const Skeleton: React.FC<SkeletonProps> = ({ shape = 'text', width, height, onDark = false, className }) => {
  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height
  }

  const themeClass = onDark === true ? 'skeleton--on-dark' : ''
  const combinedClassName = `skeleton skeleton--${shape} ${themeClass} ${className}`.trim()

  return (
    <div
      className={combinedClassName}
      style={style}
      role='status'
      aria-label='Loading content'
      aria-busy='true'
      data-testid='skeleton'
    />
  )
}
