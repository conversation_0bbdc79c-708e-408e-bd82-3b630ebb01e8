@import '././../../../assets/styles/v3/globalStyles.scss';

.skeleton {
  display: inline-block;
  background: var(--skeleton-color-background-default);
  opacity: 0.1;
  border-radius: var(--skeleton-border-radius-default);
  animation: glow 1.4s ease infinite;

  &.skeleton--text {
    height: 1em;
    width: 100%;
    border-radius: var(--skeleton-border-radius-default);
  }

  &.skeleton--circle {
    border-radius: var(--skeleton-border-radius-circle);
    aspect-ratio: 1 / 1;
  }

  &.skeleton--rectangle {
    border-radius: var(--skeleton-border-radius-default);
  }

  &.skeleton--on-dark {
    background: var(--skeleton-color-background-on-dark);
    opacity: 0.15;
    animation-name: glow-on-dark;
  }

  @keyframes glow {
    50% {
      opacity: 0.05;
    }
  }

  @keyframes glow-on-dark {
    50% {
      opacity: 0.1;
    }
  }
}
