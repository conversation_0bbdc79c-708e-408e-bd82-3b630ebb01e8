import React from 'react'

import { render, screen } from '@testing-library/react'

import { Skeleton } from './Skeleton'

describe('Skeleton', () => {
  it('renders with default props', () => {
    render(<Skeleton />)
    const skeleton = screen.getByRole('status')

    expect(skeleton).toBeInTheDocument()
    expect(skeleton).toHaveClass('skeleton')
    expect(skeleton).not.toHaveClass('skeleton--circle')
    expect(skeleton).toHaveAttribute('role', 'status')
    expect(skeleton).toHaveAttribute('aria-busy', 'true')
    expect(skeleton).toHaveAttribute('aria-label', 'Loading content')
  })

  it('renders with circle variant', () => {
    render(<Skeleton shape='circle' />)
    const skeleton = screen.getByRole('status')

    expect(skeleton).toHaveClass('skeleton--circle')
  })

  it('applies width and height correctly (number)', () => {
    render(<Skeleton width={100} height={50} />)
    const skeleton = screen.getByRole('status')

    expect(skeleton.style.width).toBe('100px')
    expect(skeleton.style.height).toBe('50px')
  })

  it('applies width and height correctly (string)', () => {
    render(<Skeleton width='75%' height='2rem' />)
    const skeleton = screen.getByRole('status')

    expect(skeleton.style.width).toBe('75%')
    expect(skeleton.style.height).toBe('2rem')
  })

  it('renders with onDark variant', () => {
    render(<Skeleton onDark />)
    const skeleton = screen.getByRole('status')

    expect(skeleton).toHaveClass('skeleton--on-dark')
  })

  it('accepts and applies additional className', () => {
    render(<Skeleton className='skeleton--text' />)
    const skeleton = screen.getByRole('status')

    expect(skeleton.className).toContain('skeleton--text')
  })
})
