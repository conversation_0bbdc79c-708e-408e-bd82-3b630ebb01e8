import { useState } from 'react'

import { CHEVRON_RIGHT_WHITE } from '../../../../assets/images'
import { getImageSrc } from '../../../../utils/local-util'
import { Spinner } from '../../Spinner'
import BaseButton from '../BaseButton'
import { AlignmentProps, ButtonIconProps, ButtonProps, ButtonStates } from '../types'

const IconButton = (props: ButtonProps): JSX.Element => {
  const {
    activeIcon = CHEVRON_RIGHT_WHITE,
    hoverIcon = CHEVRON_RIGHT_WHITE,
    disabledIcon,
    buttonStates,
    simplified,
    showLoader = false
  } = props
  const ButtonIconComponent = (
    <ButtonIcon
      icon={simplified}
      buttonStates={buttonStates}
      activeIcon={activeIcon}
      disabledIcon={disabledIcon}
      hoverIcon={hoverIcon}
      showLoader={showLoader}
    />
  )

  const alignment = props.linkUrl ? (props.alignment === AlignmentProps.CENTER ? 'text-center' : 'text-start') : ''
  return (
    <div className={`simplified-button ${alignment}`}>
      <BaseButton {...props}>{ButtonIconComponent}</BaseButton>
    </div>
  )
}

const ButtonIcon = ({ icon, buttonStates, activeIcon, disabledIcon, hoverIcon, showLoader }: ButtonIconProps) => {
  const activeSrc = buttonStates === ButtonStates.DISABLED ? disabledIcon : activeIcon
  const [imageSrc, setImageSrc] = useState(activeSrc)
  const renderIcon = () => {
    if (!imageSrc) return null
    const iconVal = imageSrc && (
      <div
        className='button-icon-wrapper'
        onMouseEnter={() => setImageSrc(hoverIcon)}
        onMouseLeave={() => setImageSrc(activeSrc)}
        style={{ width: '16px', height: '16px', backgroundImage: `url(${getImageSrc(imageSrc)})`, backgroundSize: 'cover' }}
        aria-label={icon}
      ></div>
    )
    return iconVal
  }

  return showLoader ? (
    <>
      <span className='hide-display-icon'>
        {renderIcon()}
        <Spinner color='solid' size='md' />
      </span>
    </>
  ) : (
    renderIcon()
  )
}

export default IconButton
