import { render, screen } from '@testing-library/react'
import renderer from 'react-test-renderer'
import '@testing-library/jest-dom'

import { NotificationBadgeProps } from './types'

import { NotificationBadge } from './index'

describe('NotificationBadge component', () => {
  const defaultProps: NotificationBadgeProps = {
    count: 0,
    showCount: true
  }
  test('matches snapshot', () => {
    const tree = renderer.create(<NotificationBadge {...defaultProps} />).toJSON()
    expect(tree).toMatchSnapshot()
  })
  it('renders NotificationBadge component correctly', () => {
    render(<NotificationBadge {...defaultProps} />)

    expect(screen.getByTestId('notification-badge-container')).toBeInTheDocument()
    expect(screen.getByTestId('notification-badge-text')).toHaveTextContent('0')
  })
  test('should not display count if numerical is false', () => {
    render(<NotificationBadge {...defaultProps} showCount={false} />)
    expect(screen.queryByTestId('notification-badge-text')).not.toBeInTheDocument()
  })
  test('should display correct count', () => {
    render(<NotificationBadge {...defaultProps} count={1} />)
    expect(screen.getByTestId('notification-badge-text')).toHaveTextContent('1')
  })
  test('should have postion absolute if standalone is false', () => {
    render(<NotificationBadge {...defaultProps} standalone={false} />)
    expect(screen.getByTestId('notification-badge-container')).toHaveClass('notification-badge__container--placement')
  })
  test('should not have postion absolute if standalone is true', () => {
    render(<NotificationBadge {...defaultProps} standalone={true} />)
    expect(screen.getByTestId('notification-badge-container')).not.toHaveClass('notification-badge__container--placement')
  })
})
