import './NotificationBadge.scss'
import { NotificationBadgeProps, placementClassMap } from './types'
const NotificationBadge = ({
  id = 'notification-badge',
  count = 0,
  showCount = false,
  standalone = true,
  placement = 'top end'
}: NotificationBadgeProps) => {
  const placementClass = standalone ? '' : `notification-badge__container--${placementClassMap[placement]}`
  const positionClass = standalone ? '' : `notification-badge__container--placement`
  return (
    <div
      className={`notification-badge__container 
      ${placementClass}
      ${positionClass}`}
      data-testid={`${id}-container`}
    >
      {showCount && (
        <span className={`notification-badge__text`} data-testid={`${id}-text`}>
          {count}
        </span>
      )}
    </div>
  )
}

export default NotificationBadge
