import { Meta, StoryObj } from '@storybook/react'

import NotificationBadge from './NotificationBadge'

const meta: Meta<typeof NotificationBadge> = {
  title: 'atoms/Notification Badge',
  component: NotificationBadge,
  parameters: {
    docs: {
      description: {
        component: `
The *Notification Badge* is a small visual indicator used to signal new activity, updates, or items that require attention.
        `.trim()
      }
    }
  },
  tags: ['autodocs']
}

export default meta

type Story = StoryObj<typeof NotificationBadge>

export const Default: Story = {
  args: {
    showCount: false,
    standalone: true
  },
  parameters: {
    docs: {
      description: {
        story: `A singular red dot is used to mark content that needs to be noticed, such as an unread notification.`
      }
    }
  }
}

export const Numerical: Story = {
  args: {
    count: 0,
    showCount: true,
    standalone: true
  },
  parameters: {
    docs: {
      description: {
        story: `The numerical form of a notification badge is used in cases where a count is displayed, which prevents from having to click thru or count items.`
      }
    }
  }
}

export const Standalone: Story = {
  args: {},
  render: () => {
    return (
      <div style={{ display: 'flex', gap: '2rem' }}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '0.25rem 1rem',
            background: 'var(--color-neutral-200)',
            border: '1px solid var(--color-neutral-300)',
            width: '200px'
          }}
        >
          <span>Messages</span>
          <NotificationBadge standalone showCount count={5} />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          'The `standalone` prop is used to indicate that the notification badge should not be positioned relative to any other element. This is useful when you want the notification badge to be positioned independently, such as in a notification list or a button.'
      }
    }
  }
}

export const Placement: Story = {
  render: () => {
    return (
      <div style={{ display: 'flex', gap: '2rem' }}>
        <div style={{ position: 'relative', background: 'var(--color-neutral-300)', width: '24px', height: '24px' }}>
          <NotificationBadge standalone={false} placement='top start' />
        </div>
        <div style={{ position: 'relative', background: 'var(--color-neutral-300)', width: '24px', height: '24px' }}>
          <NotificationBadge standalone={false} placement='top end' />
        </div>
        <div style={{ position: 'relative', background: 'var(--color-neutral-300)', width: '24px', height: '24px' }}>
          <NotificationBadge standalone={false} placement='bottom start' />
        </div>
        <div style={{ position: 'relative', background: 'var(--color-neutral-300)', width: '24px', height: '24px' }}>
          <NotificationBadge standalone={false} placement='bottom end' />
        </div>
        <div style={{ position: 'relative', background: 'var(--color-neutral-300)', width: '24px', height: '24px' }}>
          <NotificationBadge standalone={false} placement='left center' />
        </div>
        <div style={{ position: 'relative', background: 'var(--color-neutral-300)', width: '24px', height: '24px' }}>
          <NotificationBadge standalone={false} placement='right center' />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          'Use the `placement` prop to position the notification badge relative to an element. The placement prop can be one of the following values: `top start`, `top end`, `bottom start`, `bottom end`, `left center`, or `right center`.'
      }
    }
  }
}
