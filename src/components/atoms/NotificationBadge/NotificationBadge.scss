@import './../../../assets/styles/v3/globalStyles.scss';

.notification-badge__container {
  display: inline-flex;
  width: auto;
  height: 16px;
  min-width: 16px;
  min-height: 16px;
  padding: 0px 4px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-round);
  background-color: var(--color-red-500);
}
.notification-badge__text {
  @include getFontStyles(
    var(--cox-fontweights-cera-pro-medium),
    var(--cox-fontsize-100),
    1,
    normal,
    none,
    var(--color-neutral-100)
  );
  text-align: center;
  font-style: normal;
}

.notification-badge__container--placement {
  position: absolute;
}

.notification-badge__container--placement-top-start {
  top: 0;
  left: 0;
  transform: translate(-50%, -50%);
}

.notification-badge__container--placement-top-end {
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
}

.notification-badge__container--placement-bottom-start {
  bottom: 0;
  left: 0;
  transform: translate(-50%, 50%);
}

.notification-badge__container--placement-bottom-end {
  bottom: 0;
  right: 0;
  transform: translate(50%, 50%);
}

.notification-badge__container--placement-left-center {
  left: 0;
  top: 50%;
  transform: translate(-50%, -50%);
}

.notification-badge__container--placement-right-center {
  right: 0;
  top: 50%;
  transform: translate(50%, -50%);
}
