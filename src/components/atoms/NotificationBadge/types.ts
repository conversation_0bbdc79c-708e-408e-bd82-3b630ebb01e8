export type NotificationBadgeProps = {
  /**Used as a unique string, serving as a hook for automated tests.*/
  id?: string
  /** Display numerical count in the badge */
  showCount?: boolean
  /** Count to be displayed in the badge */
  count?: number
  /** Used for badges not positioned relative to a specific element.*/
  standalone?: boolean
  /** Used for positioning badges relative to a specific element. */
  placement?: 'top start' | 'top end' | 'bottom start' | 'bottom end' | 'left center' | 'right center'
}

export const placementClassMap: Record<string, string> = {
  'top start': 'placement-top-start',
  'top end': 'placement-top-end',
  'bottom start': 'placement-bottom-start',
  'bottom end': 'placement-bottom-end',
  'left center': 'placement-left-center',
  'right center': 'placement-right-center'
}
