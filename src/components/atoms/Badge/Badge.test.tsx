import React from 'react'

import { cleanup, screen } from '@testing-library/react'
import renderer from 'react-test-renderer'

import Badge from './Badge'
import { Alignment, BadgeVariation } from './types'
import { renderWithProviders } from '../../../test-utils'

describe('Badge component', () => {
  // 🧹 State Coverage: Cleanup
  // 🔄 Integration Coverage: Test isolation
  afterEach(cleanup)

  // 🌐 Integration Coverage: External services setup
  const preloadedState = {
    geolocation: {
      selectedState: null,
      selectedCity: null,
      selectedZipCode: null,
      geoLocationStates: [],
      geoLocationCities: [],
      geoLocationZipCodes: []
    }
  }

  describe('rendering', () => {
    // 🎯 Props Coverage: Default props
    // 🎨 Render Coverage: Initial render
    // 🔄 Integration Coverage: Hook interactions - renderWithProviders
    it('renders with default props', () => {
      const { container } = renderWithProviders(<Badge />, preloadedState)

      const badge = screen.getByTestId('badge-container')
      expect(badge).toBeInTheDocument()
      expect(badge).toHaveClass('align-left')
      expect(screen.getByTestId('badge')).toHaveClass('badge', 'cox-text-eyebrow2', 'badge-spacing')
      expect(container).toMatchSnapshot()
    })

    // 📝 Props Coverage: Optional props - text
    // 🎨 Render Coverage: Text content
    it('renders with custom text', () => {
      const text = 'Test Badge'
      renderWithProviders(<Badge text={text} />, preloadedState)
      expect(screen.getByText(text)).toBeInTheDocument()
    })

    // 📐 Props Coverage: Optional props - alignment
    // 🎨 Render Coverage: Style variations
    it('renders with custom alignment', () => {
      const alignments = Object.values(Alignment)
      alignments.forEach((align) => {
        cleanup()
        renderWithProviders(<Badge align={align} />, preloadedState)
        expect(screen.getByTestId('badge-container')).toHaveClass(`align-${align}`)
      })
    })
  })

  describe('variations', () => {
    // 🎨 Props Coverage: Optional props - variations
    // 🎯 Render Coverage: Style variations
    it('renders with each badge variation', () => {
      Object.keys(BadgeVariation).forEach((variation) => {
        cleanup()
        renderWithProviders(<Badge variation={variation} />, preloadedState)
        const badge = screen.getByTestId('badge')
        expect(badge).toHaveClass(BadgeVariation[variation])
      })
    })

    // 🎨 Props Coverage: Optional props - bgColor
    // 🔍 Props Coverage: Edge cases - bgColor without variation
    it('applies custom background color when no variation is set', () => {
      const bgColor = '#FF0000'
      renderWithProviders(<Badge bgColor={bgColor} />, preloadedState)
      const badge = screen.getByTestId('badge') as HTMLElement
      expect(badge.style.backgroundColor).toBe('rgb(255, 0, 0)')
    })

    // 🔍 Props Coverage: Edge cases - bgColor with variation
    // 🎨 Render Coverage: Style precedence
    it('does not apply background color when variation is set', () => {
      const bgColor = '#FF0000'
      renderWithProviders(<Badge variation='special' bgColor={bgColor} />, preloadedState)
      const badge = screen.getByTestId('badge') as HTMLElement
      expect(badge.style.backgroundColor).toBe('')
    })
  })

  describe('spacing', () => {
    // 📏 Props Coverage: Default behavior
    // 🎨 Render Coverage: Style classes
    it('applies badge-spacing class by default', () => {
      renderWithProviders(<Badge />, preloadedState)
      const badge = screen.getByTestId('badge')
      expect(badge).toHaveClass('badge-spacing')
    })

    // 📐 Props Coverage: Optional props - isSpacing
    // 🎨 Render Coverage: Conditional classes
    it('removes badge-spacing class when isSpacing is true', () => {
      renderWithProviders(<Badge isSpacing={true} />, preloadedState)
      const badge = screen.getByTestId('badge')
      expect(badge).not.toHaveClass('badge-spacing')
    })
  })

  describe('accessibility', () => {
    // 🏷️ Props Coverage: Optional props - id
    // ♿ Integration Coverage: Accessibility attributes
    it('applies custom id when provided', () => {
      const testId = 'test-badge'
      renderWithProviders(<Badge id={testId} />, preloadedState)
      expect(screen.getByTestId('badge-container')).toHaveAttribute('id', testId)
    })
  })

  // 📸 Render Coverage: Snapshot testing
  it('matches snapshot', () => {
    const tree = renderer.create(<Badge text='Test Badge' variation='special' align='center' />).toJSON()
    expect(tree).toMatchSnapshot()
  })
})
