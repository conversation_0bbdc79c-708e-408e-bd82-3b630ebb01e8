import { Alignment, BadgeProps, BadgeVariation } from './types'

import './Badge.scss'

const Badge = ({
  align = Alignment.left,
  text = '',
  bgColor = '',
  id = '',
  variation,
  isSpacing = false
}: BadgeProps): JSX.Element => {
  const hasVariation = variation && Object.values(BadgeVariation).includes(variation as BadgeVariation)
  return (
    <div data-testid={`badge-container`} className={`badge-container align-${align}`} id={id}>
      <div
        data-testid={`${id ? `${id}-` : ''}badge`}
        className={`badge  cox-text-eyebrow2 ${variation && (BadgeVariation[variation] ?? '')} ${
          !isSpacing ? 'badge-spacing' : ''
        }`}
        style={!hasVariation ? { backgroundColor: bgColor } : {}}
      >
        {text}
      </div>
    </div>
  )
}

export default Badge
