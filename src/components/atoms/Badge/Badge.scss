@import './../../../assets/styles/v3/globalStyles.scss';

.badge-container {
  display: flex;

  &.align-left {
    justify-content: left;
  }
  &.align-center {
    justify-content: center;
  }
  &.align-right {
    justify-content: right;
  }
  .badge {
    display: inline-flex;
    padding: 8px 20px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 30px;

    > * {
      @include cox-text-eyebrow2;
    }

    &.badge-spacing {
      margin: 8px 0px;
    }

    &.special {
      background: var(--badge-color-background-special);
    }

    &.standard {
      background: var(--badge-color-background-standard);
    }

    &.community {
      background: var(--badge-color-background-community);
    }

    &.mobile {
      background: var(--badge-color-background-mobile);
    }

    &.fiber {
      background: var(--badge-color-background-fiber);
    }
  }
}
