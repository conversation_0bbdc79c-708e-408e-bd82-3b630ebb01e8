import type { Meta, StoryObj } from '@storybook/react'

import Badge from './Badge'

const meta: Meta<typeof Badge> = {
  title: 'atoms/Badge',
  component: Badge,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {
    variation: {
      control: 'select',
      options: ['special', 'standard', 'community', 'mobile', 'fiber']
    }
  }
}

export default meta
type Story = StoryObj<typeof Badge>

export const Special: Story = {
  args: {
    variation: 'special',
    text: 'Special'
  }
}

export const Standard: Story = {
  args: {
    variation: 'standard',
    text: 'Standard'
  }
}

export const Community: Story = {
  args: {
    variation: 'community',
    text: 'Community'
  }
}

export const Mobile: Story = {
  args: {
    variation: 'mobile',
    text: 'Mobile'
  }
}

export const Fiber: Story = {
  args: {
    variation: 'fiber',
    text: 'Fiber'
  }
}
