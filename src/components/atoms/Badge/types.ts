import { CoxThemeClass } from '../../../types'

export enum Alignment {
  left = 'left',
  center = 'center',
  right = 'right'
}

/**
 * TODO: This enum contains duplicate values because they are deeply integrated with the theming system
 * across multiple versions (v2/v3) and products (Fiber/Mobile/Residential/Business).
 * These values are used in SCSS files, theme tokens, and component styles.
 *
 * This should be revisited as part of a larger design system refactor to:
 * 1. Separate color variants from product variants
 * 2. Create a more maintainable theming structure
 * 3. Remove duplicate values while maintaining backwards compatibility
 */
/* eslint-disable @typescript-eslint/no-duplicate-enum-values */
export enum BadgeVariation {
  special = 'special',
  standard = 'standard',
  mobile = 'mobile',
  fiber = 'fiber',
  community = 'community',
  green = 'mobile',
  aqua = 'standard',
  teal = 'fiber',
  merigold = 'special'
}
/* eslint-enable @typescript-eslint/no-duplicate-enum-values */

export type BadgeProps = {
  /** Specifies the alignment of badge and value has to be one of the Alignment prop  */
  align?: keyof typeof Alignment
  /**Specifies the text of the badge that should have a max of 15 characters */
  text?: string
  /**Specifies the background color of the badge */
  bgColor?: string
  /**Specifies the Id of the badge */
  id?: string
  /**Specifies the theme class of the badge */
  lob?: keyof typeof CoxThemeClass
  /**Specifies the variation of the badge and value has to be one of BadgeVariation prop */
  variation?: keyof typeof BadgeVariation
  /**Specifies the margin spacing (top & bottom) of the button */
  isSpacing?: boolean
}
