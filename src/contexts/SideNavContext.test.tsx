import React, { useRef } from 'react'

import { act, render, screen } from '@testing-library/react'

import { SideNavProvider, useSideNavContext } from './SideNavContext'

describe('SideNavContext', () => {
  test('renders snapshot correctly', () => {
    const { container } = render(
      <SideNavProvider>
        <div>Test Child</div>
      </SideNavProvider>
    )
    expect(container).toMatchSnapshot()
  })

  test('should not cause unnecessary re-renders when context value is accessed', () => {
    let renderCount = 0
    const TestComponent = () => {
      renderCount++
      const { sideNavContext, setSideNavContext, selectedSideNav } = useSideNavContext()
      const lastValueRef = useRef()

      // Store the current context value to compare on next render
      lastValueRef.current = { sideNavContext, setSideNavContext, selectedSideNav }

      return (
        <div data-testid='test-component'>
          <span data-testid='render-count'>{renderCount}</span>
          <span data-testid='nav-length'>{sideNavContext.length}</span>
        </div>
      )
    }

    render(
      <SideNavProvider>
        <TestComponent />
      </SideNavProvider>
    )

    // Initial render
    expect(screen.getByTestId('render-count')).toHaveTextContent('1')
    expect(screen.getByTestId('nav-length')).toHaveTextContent('0')

    // Multiple accesses to context should not cause additional renders
    // Simulate multiple context accesses (like what happens during AJAX calls)
    const component = screen.getByTestId('test-component')
    // Force a few more render cycles by accessing the DOM
    component.getAttribute('data-testid')
    component.getAttribute('data-testid')
    component.getAttribute('data-testid')

    // Should still only have 1 render
    expect(screen.getByTestId('render-count')).toHaveTextContent('1')
  })

  test('should maintain stable references for memoized values', () => {
    const capturedValues: any[] = []

    const TestComponent = () => {
      const contextValue = useSideNavContext()
      capturedValues.push({
        setter: contextValue.setSideNavContext,
        selectedNav: contextValue.selectedSideNav
      })

      return <div data-testid='test-component' />
    }

    const { rerender } = render(
      <SideNavProvider>
        <TestComponent />
      </SideNavProvider>
    )

    // Force a re-render by re-rendering the component
    rerender(
      <SideNavProvider>
        <TestComponent />
      </SideNavProvider>
    )

    // The setter function should be stable (same reference)
    expect(capturedValues[0].setter).toBe(capturedValues[1].setter)

    // The selectedNav should be stable when nav context is empty
    expect(capturedValues[0].selectedNav).toBe(capturedValues[1].selectedNav)
  })

  test('should only re-render when sideNavContext actually changes', () => {
    let renderCount = 0

    const TestComponent = () => {
      renderCount++
      const { sideNavContext, setSideNavContext } = useSideNavContext()

      // Expose setter for testing
      ;(window as any).testSetSideNavContext = setSideNavContext

      return (
        <div data-testid='test-component'>
          <span data-testid='render-count'>{renderCount}</span>
          <span data-testid='nav-length'>{sideNavContext.length}</span>
        </div>
      )
    }

    render(
      <SideNavProvider>
        <TestComponent />
      </SideNavProvider>
    )

    expect(screen.getByTestId('render-count')).toHaveTextContent('1')

    // Update with actual new navigation data
    act(() => {
      ;(window as any).testSetSideNavContext([{ id: '1', title: 'Test Nav', selected: false }])
    })

    // Should re-render because nav context changed
    expect(screen.getByTestId('render-count')).toHaveTextContent('2')
    expect(screen.getByTestId('nav-length')).toHaveTextContent('1')

    // Update with the same data (should not cause re-render due to React's state optimization)
    act(() => {
      ;(window as any).testSetSideNavContext([{ id: '1', title: 'Test Nav', selected: false }])
    })

    // May re-render due to new array reference, but that's expected behavior
    // The important thing is our context value remains stable
    expect(screen.getByTestId('nav-length')).toHaveTextContent('1')

    // Clean up
    delete (window as any).testSetSideNavContext
  })

  test('should correctly compute selectedSideNav with memoization', () => {
    let selectedNavComputeCount = 0

    const TestComponent = () => {
      const { selectedSideNav } = useSideNavContext()

      // Track how many times selectedSideNav changes
      React.useEffect(() => {
        selectedNavComputeCount++
      }, [selectedSideNav])

      return (
        <div data-testid='test-component'>
          <span data-testid='compute-count'>{selectedNavComputeCount}</span>
          <span data-testid='selected-id'>{selectedSideNav?.id || 'none'}</span>
        </div>
      )
    }

    const WrapperComponent = () => {
      const { setSideNavContext } = useSideNavContext()

      React.useEffect(() => {
        // Set up navigation with a selected item
        setSideNavContext([
          { id: '1', title: 'Item 1', selected: false },
          {
            id: '2',
            title: 'Item 2',
            selected: false,
            subNavItems: [
              { id: '2-1', title: 'Sub Item 1', selected: true },
              { id: '2-2', title: 'Sub Item 2', selected: false }
            ]
          }
        ])
      }, [setSideNavContext])

      return <TestComponent />
    }

    render(
      <SideNavProvider>
        <WrapperComponent />
      </SideNavProvider>
    )

    // Wait for the effect to run and verify selectedSideNav is computed correctly
    expect(screen.getByTestId('selected-id')).toHaveTextContent('2-1')

    // selectedSideNav should only compute when necessary
    expect(selectedNavComputeCount).toBeGreaterThan(0)
  })

  /**
   * Future Test Cases:
   *
   * 1. Context Initialization (lines 1-8)
   * - Should create context with empty array
   * - Should expose navigation and setter
   * - Should initialize selectedSideNav as empty object
   * - Should have correct displayName
   *
   * 2. Provider Setup (lines 10-36)
   * - Should initialize with empty array
   * - Should properly wrap children
   * - Should memoize context value
   * - Should maintain state consistency
   *
   * 3. Navigation State Management (lines 11-14)
   * - Should update navigation array
   * - Should replace entire navigation state
   * - Should maintain array immutability
   * - Should handle empty updates
   *
   * 4. Selected Navigation (lines 15-27)
   * - Should find selected main nav item
   * - Should find selected sub nav item
   * - Should handle nested navigation
   * - Should return null for no selection
   * - Should handle multiple selections
   *
   * 5. State Updates (lines 28-34)
   * - Should update context value
   * - Should recalculate selected nav
   * - Should trigger appropriate re-renders
   * - Should maintain reference equality
   *
   * 6. Hook Usage (lines 38-42)
   * - Should throw error when used outside provider
   * - Should provide access to navigation state
   * - Should update components on changes
   * - Should handle multiple consumers
   *
   * 7. Navigation Structure
   * - Should handle flat navigation
   * - Should handle nested navigation
   * - Should track selection state
   * - Should manage sub-navigation
   *
   * 8. Selection Logic
   * - Should handle main item selection
   * - Should handle sub-item selection
   * - Should maintain single selection
   * - Should clear previous selections
   *
   * 9. Error Handling
   * - Should handle malformed navigation data
   * - Should handle missing properties
   * - Should validate navigation structure
   * - Should preserve state on failures
   *
   * 10. Performance
   * - Should optimize re-renders
   * - Should handle large navigation trees
   * - Should manage memory efficiently
   * - Should clean up on unmount
   *
   * 11. Integration
   * - Should work with router
   * - Should persist through navigation
   * - Should sync with URL state
   * - Should work with animation systems
   */
})
