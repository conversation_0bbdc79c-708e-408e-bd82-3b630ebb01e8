import { create<PERSON>ontext, ReactNode, useCallback, useContext, useMemo, useState } from 'react'

import cloneDeep from 'lodash/cloneDeep'
import isEqual from 'lodash/isEqual'
import merge from 'lodash/merge'

export type Page = {
  custom: object
  template: {
    displayHeader: boolean
    displayFooter: boolean
    displayChat: boolean
    chatContent: string
    sections: {
      payment: {
        customerType: string
      }
    }
    templateName: string
    pageName: string
  }
  reserved: {
    csrfToken: string
    pageversion: string
  }
  displayBBFL?: string
  bbflPath?: string
  sections?: {
    payment: {
      customerType: string
    }
  }
}

export type Udo = {
  visitorType: string
  cidm: string
  uid: string
  visitorServiceability: string
  cBillPay3Days: boolean
  cCHLEligible: boolean
  cEasyPayEnroll: boolean
  cPaperlessEnrolled: boolean
  cMobilePhone: boolean
  cMobilePhoneVerified: boolean
  cTcPendOrder: boolean
  cSrPendOrder: boolean
  cInsPendOrder: boolean
  cPendShipment: boolean
  cPendPickup: boolean
  moveAddressCapture: boolean
  zip: string
  pAddressActive: boolean
  pAddressCapture: boolean
  pAddressStreet: string
  pAddressZip: string
  pAddressApartment: string
  pHouseNumber: string
  moveAddressActive: boolean
  moveToApt: string
  moveToStreet: string
  moveToZip: string
  moveToHouseNumber: string
  moveToSiteID: string
  mfaEnrolled: boolean
  pCoxMdmRequest: boolean
  zipGeo: string
  error: string
  serviceableCodes: string
  siteID: string
  franchiseId: string
  localeName: string
}
export type AppData = {
  page: Page
  udo: Udo
  displayBBFL?: string
  success: boolean
}
export type AppDataContextValue = {
  appData: AppData
  setAppData: (data: any) => void
}

export const AppDataContext = createContext<AppDataContextValue | null>(null)

AppDataContext.displayName = 'AppDataContext'

const initialState = {
  page: {
    custom: {},
    template: {
      displayHeader: false,
      displayFooter: false,
      displayChat: false,
      chatContent: '',
      sections: {
        payment: {
          customerType: ''
        }
      },
      templateName: '',
      pageName: ''
    },
    reserved: {
      csrfToken: '',
      pageversion: ''
    },
    displayBBFL: 'false',
    bbflPath: '',
    //TODO: static json files should match with java response on custom apps
    sections: {
      payment: {
        customerType: ''
      }
    }
  },
  udo: {
    visitorType: '',
    cidm: '',
    uid: '',
    visitorServiceability: '',
    cBillPay3Days: false,
    cCHLEligible: false,
    cEasyPayEnroll: false,
    cPaperlessEnrolled: false,
    cMobilePhone: false,
    cMobilePhoneVerified: false,
    cTcPendOrder: false,
    cSrPendOrder: false,
    cInsPendOrder: false,
    cPendShipment: false,
    cPendPickup: false,
    moveAddressCapture: false,
    zip: '',
    pAddressActive: false,
    pAddressCapture: false,
    pAddressStreet: '',
    pAddressZip: '',
    pAddressApartment: '',
    pHouseNumber: '',
    moveAddressActive: false,
    moveToApt: '',
    moveToStreet: '',
    moveToZip: '',
    moveToHouseNumber: '',
    moveToSiteID: '',
    mfaEnrolled: false,
    pCoxMdmRequest: false,
    zipGeo: '',
    error: '',
    serviceableCodes: '',
    siteID: '',
    franchiseId: '',
    localeName: ''
  },
  displayBBFL: 'false',
  success: false
}

export const AppDataProvider = ({ children }: { children: ReactNode }) => {
  const [appData, setAppData] = useState<AppData>(initialState)

  // Memoize the setter function with useCallback to prevent recreation on every render
  const setter = useCallback((newValue: any) => {
    setAppData((prev: any) => {
      // Proper deep merge - clone prev to avoid mutation, then merge newValue
      const updated = merge(cloneDeep(prev), newValue)

      // Only update state if data has actually changed
      // Using lodash isEqual for deep comparison
      if (!isEqual(updated, prev)) {
        return updated
      }
      return prev
    })
  }, [])

  // Memoize the context value to prevent unnecessary re-renders
  const valueObject = useMemo(() => {
    return { appData, setAppData: setter }
  }, [appData, setter])
  return <AppDataContext.Provider value={valueObject}>{children}</AppDataContext.Provider>
}

export function useAppDataContext() {
  const context = useContext(AppDataContext)
  if (!context) {
    throw new Error('AppDataContext must be used within a AppDataContextProvider')
  }
  return context
}
