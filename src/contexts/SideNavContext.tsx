import { createContext, useCallback, useContext, useMemo, useState } from 'react'
export const SideNavContext = createContext({
  sideNavContext: [],
  setSideNavContext: (navigationDetails: any) => {
    console.info(navigationDetails)
  },
  selectedSideNav: {}
})
SideNavContext.displayName = 'SideNavContext'

export const SideNavProvider = ({ children }: any) => {
  const [sideNavContext, setSideNavContext] = useState<any>([])

  // Performance optimization: Stabilize setter function reference to prevent unnecessary re-renders
  const setter = useCallback((newValue: any) => {
    setSideNavContext(newValue)
  }, [])

  // Performance optimization: Cache selectedSideNav computation to avoid recalculation on every render
  const selectedSideNav = useMemo(() => {
    return sideNavContext
      .map((item: any) => {
        if (item.subNavItems) {
          const selectedSubNavItem = item.subNavItems.find((subItem: any) => subItem.selected === true)
          if (selectedSubNavItem) {
            return selectedSubNavItem // Return the selected subNavItem
          }
        }
        return item.selected === true ? item : null
      })
      .filter((item: any) => item !== null)[0]
  }, [sideNavContext])

  // Performance optimization: Stabilize context value object to prevent cascading re-renders
  const valueObject = useMemo(() => {
    return {
      sideNavContext,
      setSideNavContext: setter,
      selectedSideNav
    }
  }, [sideNavContext, setter, selectedSideNav])
  return <SideNavContext.Provider value={valueObject}>{children}</SideNavContext.Provider>
}

export function useSideNavContext() {
  const context = useContext(SideNavContext)
  if (!context) {
    throw new Error('SideNavContext must be used within a SideNavContextProvider')
  }
  return context
}
