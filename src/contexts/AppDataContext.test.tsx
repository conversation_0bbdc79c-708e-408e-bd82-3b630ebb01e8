import React from 'react'

import { act, render, renderHook } from '@testing-library/react'

import '@testing-library/jest-dom'
import { AppData, AppDataProvider, Page, useAppDataContext } from './AppDataContext'

// Add a declaration file to extend the types for the tests
declare module './AppDataContext' {
  interface Page {
    template: {
      sections: {
        payment: { customerType: string }
        custom?: { testValue: string }
        section1?: { value: string }
        section2?: { value: string }
      }
    }
  }
}

describe('AppDataContext', () => {
  test('renders snapshot correctly', () => {
    const { container } = render(
      <AppDataProvider>
        <div>Test Child</div>
      </AppDataProvider>
    )
    expect(container).toMatchSnapshot()
  })

  // Wrapper for renderHook to provide the context
  const wrapper = ({ children }: { children: React.ReactNode }) => <AppDataProvider>{children}</AppDataProvider>

  // Test for our state comparison optimization
  test('should not update state when new data is identical', () => {
    // Use renderHook to test our custom hook
    const { result } = renderHook(() => useAppDataContext(), { wrapper })

    // First update - should cause a state change
    act(() => {
      // Create a valid AppData object with test sections
      const mockData = {
        page: {
          custom: {},
          template: {
            displayHeader: false,
            displayFooter: false,
            displayChat: false,
            chatContent: '',
            sections: {
              payment: { customerType: 'test' },
              custom: { testValue: 'test' }
            } as Page['template']['sections'],
            templateName: '',
            pageName: ''
          },
          reserved: {
            csrfToken: '',
            pageversion: ''
          }
        },
        udo: {
          visitorType: '',
          cidm: '',
          uid: '',
          visitorServiceability: '',
          // Add minimal required fields
          cBillPay3Days: false,
          cCHLEligible: false,
          cEasyPayEnroll: false,
          cPaperlessEnrolled: false,
          cMobilePhone: false,
          cMobilePhoneVerified: false,
          cTcPendOrder: false,
          cSrPendOrder: false,
          cInsPendOrder: false,
          cPendShipment: false,
          cPendPickup: false,
          moveAddressCapture: false,
          zip: '',
          pAddressActive: false,
          pAddressCapture: false,
          pAddressStreet: '',
          pAddressZip: '',
          pAddressApartment: '',
          pHouseNumber: '',
          moveAddressActive: false,
          moveToApt: '',
          moveToStreet: '',
          moveToZip: '',
          moveToHouseNumber: '',
          moveToSiteID: '',
          mfaEnrolled: false,
          pCoxMdmRequest: false,
          zipGeo: '',
          error: '',
          serviceableCodes: '',
          siteID: '',
          franchiseId: '',
          localeName: ''
        },
        success: true
      }

      result.current.setAppData(mockData)
    })

    // Get reference to current state
    const firstState = result.current.appData

    // Second update with identical data - should NOT cause a state change
    act(() => {
      // Use the same structure to ensure deep equality check works
      const mockData: Partial<AppData> = {
        page: {
          custom: {},
          template: {
            displayHeader: false,
            displayFooter: false,
            displayChat: false,
            chatContent: '',
            sections: {
              payment: { customerType: 'test' },
              custom: { testValue: 'test' }
            },
            templateName: '',
            pageName: ''
          },
          reserved: {
            csrfToken: '',
            pageversion: ''
          }
        },
        udo: {
          visitorType: '',
          cidm: '',
          uid: '',
          visitorServiceability: '',
          cBillPay3Days: false,
          cCHLEligible: false,
          cEasyPayEnroll: false,
          cPaperlessEnrolled: false,
          cMobilePhone: false,
          cMobilePhoneVerified: false,
          cTcPendOrder: false,
          cSrPendOrder: false,
          cInsPendOrder: false,
          cPendShipment: false,
          cPendPickup: false,
          moveAddressCapture: false,
          zip: '',
          pAddressActive: false,
          pAddressCapture: false,
          pAddressStreet: '',
          pAddressZip: '',
          pAddressApartment: '',
          pHouseNumber: '',
          moveAddressActive: false,
          moveToApt: '',
          moveToStreet: '',
          moveToZip: '',
          moveToHouseNumber: '',
          moveToSiteID: '',
          mfaEnrolled: false,
          pCoxMdmRequest: false,
          zipGeo: '',
          error: '',
          serviceableCodes: '',
          siteID: '',
          franchiseId: '',
          localeName: ''
        },
        success: true
      }

      result.current.setAppData(mockData)
    })

    // Verify that state reference hasn't changed (no re-render due to optimization)
    expect(result.current.appData).toBe(firstState)
  })

  // Test that different data properly updates the state
  test('should update state when new data is different', () => {
    // Use renderHook to test our custom hook
    const { result } = renderHook(() => useAppDataContext(), { wrapper })

    // First update with initial data
    act(() => {
      const mockData = {
        page: {
          custom: {},
          template: {
            displayHeader: false,
            displayFooter: false,
            displayChat: false,
            chatContent: '',
            sections: {
              payment: { customerType: 'test' },
              custom: { testValue: 'initial' } as unknown as any
            },
            templateName: '',
            pageName: ''
          },
          reserved: {
            csrfToken: '',
            pageversion: ''
          }
        },
        udo: {
          visitorType: '',
          cidm: '',
          uid: '',
          visitorServiceability: '',
          cBillPay3Days: false,
          cCHLEligible: false,
          cEasyPayEnroll: false,
          cPaperlessEnrolled: false,
          cMobilePhone: false,
          cMobilePhoneVerified: false,
          cTcPendOrder: false,
          cSrPendOrder: false,
          cInsPendOrder: false,
          cPendShipment: false,
          cPendPickup: false,
          moveAddressCapture: false,
          zip: '',
          pAddressActive: false,
          pAddressCapture: false,
          pAddressStreet: '',
          pAddressZip: '',
          pAddressApartment: '',
          pHouseNumber: '',
          moveAddressActive: false,
          moveToApt: '',
          moveToStreet: '',
          moveToZip: '',
          moveToHouseNumber: '',
          moveToSiteID: '',
          mfaEnrolled: false,
          pCoxMdmRequest: false,
          zipGeo: '',
          error: '',
          serviceableCodes: '',
          siteID: '',
          franchiseId: '',
          localeName: ''
        },
        success: true
      }

      result.current.setAppData(mockData)
    })

    // Get reference to current state
    const firstState = result.current.appData

    // Update with different data
    act(() => {
      const mockData = {
        page: {
          custom: {},
          template: {
            displayHeader: false,
            displayFooter: false,
            displayChat: false,
            chatContent: '',
            sections: {
              payment: { customerType: 'test' },
              custom: { testValue: 'updated' } as unknown as any // Changed value here
            },
            templateName: '',
            pageName: ''
          },
          reserved: {
            csrfToken: '',
            pageversion: ''
          }
        },
        udo: {
          visitorType: '',
          cidm: '',
          uid: '',
          visitorServiceability: '',
          cBillPay3Days: false,
          cCHLEligible: false,
          cEasyPayEnroll: false,
          cPaperlessEnrolled: false,
          cMobilePhone: false,
          cMobilePhoneVerified: false,
          cTcPendOrder: false,
          cSrPendOrder: false,
          cInsPendOrder: false,
          cPendShipment: false,
          cPendPickup: false,
          moveAddressCapture: false,
          zip: '',
          pAddressActive: false,
          pAddressCapture: false,
          pAddressStreet: '',
          pAddressZip: '',
          pAddressApartment: '',
          pHouseNumber: '',
          moveAddressActive: false,
          moveToApt: '',
          moveToStreet: '',
          moveToZip: '',
          moveToHouseNumber: '',
          moveToSiteID: '',
          mfaEnrolled: false,
          pCoxMdmRequest: false,
          zipGeo: '',
          error: '',
          serviceableCodes: '',
          siteID: '',
          franchiseId: '',
          localeName: ''
        },
        success: true
      }

      result.current.setAppData(mockData)
    })

    // Verify that state reference has changed (proper state update)
    expect(result.current.appData).not.toBe(firstState)

    // Verify the new state has the updated value
    // @ts-expect-error - accessing custom property for testing
    expect(result.current.appData.page.template.sections.custom.testValue).toBe('updated')
  })

  // Test for partial updates
  test('should properly merge partial updates', () => {
    // Use renderHook to test our custom hook
    const { result } = renderHook(() => useAppDataContext(), { wrapper })

    // Initial state with multiple properties
    act(() => {
      const mockData = {
        page: {
          custom: {},
          template: {
            displayHeader: false,
            displayFooter: false,
            displayChat: false,
            chatContent: '',
            sections: {
              payment: { customerType: 'test' },
              section1: { value: 'one' } as unknown as any,
              section2: { value: 'two' } as unknown as any
            },
            templateName: '',
            pageName: ''
          },
          reserved: {
            csrfToken: '',
            pageversion: ''
          }
        },
        udo: {
          visitorType: 'original',
          cidm: '',
          uid: '',
          visitorServiceability: '',
          cBillPay3Days: false,
          cCHLEligible: false,
          cEasyPayEnroll: false,
          cPaperlessEnrolled: false,
          cMobilePhone: false,
          cMobilePhoneVerified: false,
          cTcPendOrder: false,
          cSrPendOrder: false,
          cInsPendOrder: false,
          cPendShipment: false,
          cPendPickup: false,
          moveAddressCapture: false,
          zip: '',
          pAddressActive: false,
          pAddressCapture: false,
          pAddressStreet: '',
          pAddressZip: '',
          pAddressApartment: '',
          pHouseNumber: '',
          moveAddressActive: false,
          moveToApt: '',
          moveToStreet: '',
          moveToZip: '',
          moveToHouseNumber: '',
          moveToSiteID: '',
          mfaEnrolled: false,
          pCoxMdmRequest: false,
          zipGeo: '',
          error: '',
          serviceableCodes: '',
          siteID: '',
          franchiseId: '',
          localeName: ''
        },
        success: true
      }

      result.current.setAppData(mockData)
    })

    // Update only one part of the state
    act(() => {
      // Just update the visitorType field in udo
      result.current.setAppData({
        udo: {
          ...result.current.appData.udo,
          visitorType: 'updated'
        }
      })
    })

    // Verify that the updated part changed
    expect(result.current.appData.udo.visitorType).toBe('updated')

    // Verify that non-updated parts are preserved
    // @ts-expect-error - accessing custom sections for testing
    expect(result.current.appData.page.template.sections.section1.value).toBe('one')
    // @ts-expect-error - accessing custom sections for testing
    expect(result.current.appData.page.template.sections.section2.value).toBe('two')
    expect(result.current.appData.success).toBe(true)
  })

  // Test for memoized setter function
  test('setter function should be stable across renders', () => {
    // Setup a component that captures the setter function on each render
    const setterFunctions: Array<(data: Partial<AppData>) => void> = []

    const TestComponent = () => {
      const { setAppData } = useAppDataContext()
      setterFunctions.push(setAppData)
      return null
    }

    // Render the component multiple times
    const { rerender } = render(
      <AppDataProvider>
        <TestComponent />
      </AppDataProvider>
    )

    // Force re-renders
    rerender(
      <AppDataProvider>
        <TestComponent />
      </AppDataProvider>
    )

    rerender(
      <AppDataProvider>
        <TestComponent />
      </AppDataProvider>
    )

    // Verify that all setter references are the same (function was memoized)
    expect(setterFunctions[0]).toBe(setterFunctions[1])
    expect(setterFunctions[1]).toBe(setterFunctions[2])
  })

  /**
   * Additional Test Cases to Consider:
   *
   * 1. Context Creation
   * - Should create context with default values
   * - Should expose appData and setAppData
   * - Should have correct displayName
   *
   * 2. Provider Behavior
   * - Should provide initial empty state
   * - Should wrap children correctly
   * - Should memoize value object correctly
   * - Should maintain state between renders
   *
   * 3. State Management
   * - Should update appData when setter is called
   * - Should merge new data with existing state
   * - Should handle nested object updates
   * - Should handle multiple sequential updates
   *
   * 4. Hook Usage
   * - Should throw error when used outside provider
   * - Should provide access to current context value
   * - Should update components when context changes
   * - Should handle multiple consumers
   *
   * 5. Error Handling
   * - Should handle invalid data in setter
   * - Should handle undefined/null values
   * - Should preserve existing data on failed updates
   *
   * 6. Performance
   * - Should prevent unnecessary re-renders
   * - Should properly memoize setter function
   * - Should handle large state objects
   *
   * 7. Type Safety
   * - Should enforce correct types for appData
   * - Should enforce correct types for setter
   * - Should handle generic type parameters
   *
   * 8. Integration
   * - Should work with nested providers
   * - Should work with concurrent mode
   * - Should work with suspense boundaries
   */
})
