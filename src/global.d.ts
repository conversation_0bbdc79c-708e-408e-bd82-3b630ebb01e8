export {}
declare module 'react-helmet'
declare global {
  interface Window {
    // Below just informs IDE and/or TS-compiler (it's set in `.js` file).
    RequestJson: {
      udo: object
      page: {
        template: {
          udo: object
          page: object
        }
        reserved: {
          csrfToken: string
          pageversion: string
        }
      }
    }
    adobe: {
      target: {
        event: any
        triggerView: (key: string, { page: boolean }) => void
      }
    }
    utag: {
      link: (params: { [key: string]: any }) => void
      view: (params: { [key: string]: any }) => void
    }
    utag_data: {
      resposiveDisplayType: string
      serviceableCodes: string
      visitorType: string
      cidm: any
      pageName: any
      uid?: string // Universal ID set by WebAPI from cfuid cookie
      categorySubscribed: any
    }
    newrelic: {
      addPageAction: (key: string, value: any) => void
      setCustomAttribute: (key: string, value: any) => void
    }
    app_config: any
    Visitor: any
  }
  interface Navigator {
    connection: { downlink: any; rtt: any }
  }
}
