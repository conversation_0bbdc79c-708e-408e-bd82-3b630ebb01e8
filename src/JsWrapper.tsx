import { createRoot } from 'react-dom/client'

import './assets/styles/v3/globalStyles.scss'
/** Atoms */
// import Accordion from './components/atoms/Accordion'
// import Badge from './components/atoms/Badge'
// import Banner, { BannerType, BannerVariaion } from './components/atoms/Banner'
// import Button, { ButtonStates, LinkTypes, SimplifiedIcons } from './components/atoms/Button'
// import Modal from './components/atoms/Modal'
// import { ModalBody } from './components/atoms/Modal/ModalBody'
// import { ModalFooter } from './components/atoms/Modal/ModalFooter'
// import { ModalHeader } from './components/atoms/Modal/ModalHeader'
// import Pagination from './components/atoms/Pagination'
// import RichText from './components/atoms/RichText'
// import Image from './components/atoms/Image'
// import SectionHeader from './components/atoms/SectionHeader'
import Separator from './components/atoms/Separator'
import Typography from './components/atoms/Typography'
// import YextSearch, { SearchStyleStrings } from './components/atoms/YextSearch'
// /** Molecules */
// import BBFL from './components/molecules/BBFL'
// import SolutionList, { Solution } from './components/molecules/SolutionList'
// import Footer from './components/organisms/Footer'
// /** Organisms */
// import Header from './components/organisms/Header'
// /** Tempaltes */
// import Layout from './components/templates/Layout'
// import PageLoader from './components/templates/PageLoader'

/** Atoms */
export const renderSeparator = (element: any, weight: any) => {
  createRoot(element).render(<Separator weight={weight} />)
}
export const renderTypography = (element: any, style: any, text: any) => {
  createRoot(element).render(<Typography style={style} text={text} />)
}

// export const renderRichText = (element: any, text: any, className: any) => {
//   createRoot(element).render(<RichText text={text} className={className} />)
// }

// export const renderImage = (element: any, src: string, containerClassname: string) => {
//   createRoot(element).render(<Image src={src} className={containerClassname} />)
// }

// export const renderAccordion = (
//   element: any,
//   accordionIcon: any,
//   accordionItems: any,
//   alwaysOpen: any,
//   defaultActiveKey: any,
//   handleHeaderClick: any
// ) => {
//   createRoot(element).render(
//     <Accordion
//       accordionIcon={accordionIcon}
//       accordionItems={accordionItems}
//       alwaysOpen={alwaysOpen}
//       defaultActiveKey={defaultActiveKey}
//       handleHeaderClick={handleHeaderClick}
//     />
//   )
// }

// export const renderSectionHeader = (element: any) => {
//   createRoot(element).render(<SectionHeader eyebrow={{ value: 'text' }} />)
// }
// export const renderBage = (element: any) => {
//   createRoot(element).render(<Badge text={'text'} />)
// }
// export const renderBanner = (
//   element: any,
//   anchor: string,
//   altIcon: string,
//   bannerType: BannerType,
//   buttonIcon: string,
//   buttonStates: ButtonStates,
//   className: string,
//   iconPath: string,
//   id: string,
//   linkId: string,
//   linkText: string,
//   linkType: LinkTypes,
//   linkUrl: string,
//   message: string,
//   openInNewTab: boolean,
//   simplified: SimplifiedIcons,
//   variation: BannerVariaion
// ) => {
//   createRoot(element).render(
//     <Banner
//       anchor={anchor}
//       altIcon={altIcon}
//       bannerType={bannerType}
//       buttonIcon={buttonIcon}
//       buttonStates={buttonStates}
//       className={className}
//       iconPath={iconPath}
//       id={id}
//       linkId={linkId}
//       linkText={linkText}
//       linkType={linkType}
//       linkUrl={linkUrl}
//       message={message}
//       openInNewTab={openInNewTab}
//       simplified={simplified}
//       variation={variation}
//     />
//   )
// }

// export const renderButton = (element: any, text: any, buttonStates: any, buttonTypes: any) => {
//   createRoot(element).render(<Button text={text} buttonStates={buttonStates} buttonTypes={buttonTypes} />)
// }
// export const renderModalHeader = (element: any) => {
//   createRoot(element).render(<ModalHeader title={'Modal Header Title'} modalId='test' />)
// }
// export const renderModalBody = (element: any) => {
//   createRoot(element).render(
//     <ModalBody headline={'This is Modal Headline'} subhead='This is Modal Sub Headline' modalId='test' />
//   )
// }
// export const renderModalFooter = (element: any) => {
//   createRoot(element).render(<ModalFooter showFooter={false} primaryBtnText={'Primary Button'} modalId={''} />)
// }
// export const renderModal = (element: any) => {
//   createRoot(element).render(
//     <Modal show={true} handleClose={() => {}} modalId='test'>
//       <ModalHeader title={'Modal Header Title'} modalId={''}></ModalHeader>
//       <ModalBody headline={'This is Modal Headline'} subhead='This is Modal Sub Headline' modalId={'test'}></ModalBody>
//       <ModalFooter showFooter={false} primaryBtnText={'Primary Button'} modalId={'test'}></ModalFooter>
//     </Modal>
//   )
// }
// export const renderPagination = (element: any) => {
//   createRoot(element).render(<Pagination pageNumbers={[1, 2, 3]} />)
// }
// export const renderYextSearch = (element: any, id: string, searchstyle: SearchStyleStrings) => {
//   createRoot(element).render(<YextSearch id={id} searchstyle={searchstyle} />)
// }

// /** Molecules */

// export const renderBBFL = (element: any, display: any, className: any) => {
//   createRoot(element).render(<BBFL display={display} className={className} />)
// }
// export const renderSolutionList = (
//   element: any,
//   className: string,
//   componentTitle: string,
//   componentDescription: string,
//   pagination: boolean,
//   pagesList: string[],
//   solutionList: Solution[]
// ) => {
//   createRoot(element).render(
//     <SolutionList
//       className={className}
//       componentTitle={componentTitle}
//       componentDescription={componentDescription}
//       pagination={pagination}
//       pagesList={pagesList}
//       solutionsList={solutionList}
//     />
//   )
// }
// export const renderFormText = (element: any) => {
//   createRoot(element).render(<FormText id={''} title={'Mock Input'} />)
// }
// export const renderFormContainer = (element: any) => {
//   createRoot(element).render(
//     <FormContainer
//       formState={{
//         text: {
//           value: '',
//           isRequired: false,
//           status: MessageStatus.PENDING,
//           inputType: 'text'
//         }
//       }}
//     >
//       <FormText id={''} title={'Mock Input'}></FormText>
//     </FormContainer>
//   )
// }
// /** Organisms */

// export const renderFooter = (
//   element: any,
//   navigationdetails: string,
//   disclaimerText: string,
//   chevron: string,
//   baseCssClass: string,
//   lob: string
// ) => {
//   createRoot(element).render(
//     <Footer
//       navigationdetails={navigationdetails}
//       disclaimerText={disclaimerText}
//       chevron={chevron}
//       baseCssClass={baseCssClass}
//       lob={lob}
//     />
//   )
// }

// export const renderHeader = (
//   element: any,
//   lob: string,
//   logo: string,
//   navigationdetails: string,
//   timesCircle: string,
//   timesCircleWhite: string,
//   globalNavMenu: string,
//   arrowDownLargeBlack: string,
//   chevronUpBlack: string,
//   geoLocationDetails: string,
//   displayContactUs: string,
//   locationPin: string,
//   geoError: string,
//   search: string,
//   displaySignInOut: string,
//   signInUrl: string,
//   signIn: string,
//   bellnotification: string,
//   notificationViewAllUrl: string,
//   shoppingCart: string
// ) => {
//   createRoot(element).render(
//     <Header
//       lob={lob}
//       logo={logo}
//       navigationdetails={navigationdetails}
//       timesCircle={timesCircle}
//       timesCircleWhite={timesCircleWhite}
//       globalNavMenu={globalNavMenu}
//       arrowDownLargeBlack={arrowDownLargeBlack}
//       chevronUpBlack={chevronUpBlack}
//       geoLocationDetails={geoLocationDetails}
//       displayContactUs={displayContactUs}
//       locationPin={locationPin}
//       geoError={geoError}
//       search={search}
//       displaySignInOut={displaySignInOut}
//       signInUrl={signInUrl}
//       signIn={signIn}
//       bellnotification={bellnotification}
//       notificationViewAllUrl={notificationViewAllUrl}
//       shoppingCart={shoppingCart}
//     />
//   )
// }

// /**Templates */
// export const renderLayout = (element: any, children: any) => {
//   createRoot(element).render(<Layout>{children}</Layout>)
// }

// export const renderPageLoader = (element: any) => {
//   createRoot(element).render(<PageLoader />)
// }
