import parse, { attributesToProps, Element, HTMLReactParserOptions, Text } from 'html-react-parser'
import { Helmet } from 'react-helmet'

import { COX_DOMAINS, HREF, ORIGIN, SEARCH, SPANISH_COX_DOMAINS } from '../constants'
import { CoxThemeClass, LobExperience } from '../types'

export const getThemeClass = function () {
  if (HREF.indexOf(LobExperience.BUSINESS) > -1) {
    return CoxThemeClass.BUSINESS
  } else {
    return CoxThemeClass.RESIDENTIAL
  }
}

export const convertStringToArray = (string: any) => {
  try {
    if (string) {
      return JSON.parse(
        // prettier-ignore
        string?.replace(/'/g, '\'')?.replace(/([^"]+)|("[^"]+")/g, function ($0: any, $1: string, $2: any) {
          if ($1) {
            return $1.replace(/([a-zA-Z0-9]+?):/g, '"$1":')
          } else {
            return $2
          }
        })
      )
    } else return []
  } catch (error) {
    console.error(error)
    return []
  }
}

export const getPrimaryAndSecondaryMenuItems = (menuItems: any) => {
  let primaryMenuOptions = menuItems || []
  let secondaryMenuOptions = []

  const getIndexOfSecondarySectionStart = (menuItem: any) => menuItem.startSecondarySection === true
  const secondaryMenuStartIndex = (menuItems.length > 0 && menuItems?.findIndex(getIndexOfSecondarySectionStart)) || -1

  if (secondaryMenuStartIndex > 0) {
    primaryMenuOptions = menuItems?.slice(0, secondaryMenuStartIndex) || []
    secondaryMenuOptions = menuItems?.slice(secondaryMenuStartIndex) || []
  }

  return { primaryMenuOptions, secondaryMenuOptions }
}

export const getFilteredItemsForProspectCustomer = (menuItems: any, isCustomer: any, isNoncustomer: any) => {
  const filteredItems = menuItems.filter((menuItem: any) => {
    const { showForProspect, showForCustomer } = menuItem
    if ((showForProspect && showForCustomer) || (!showForProspect && !showForCustomer)) {
      return menuItem
    } else {
      if (isCustomer && showForCustomer && !showForProspect) {
        return menuItem
      } else if (isNoncustomer && !showForCustomer && showForProspect) {
        return menuItem
      }
    }
  })
  return filteredItems
}

export const replaceColon = (link: any) => {
  if (link) {
    return link?.replace('&colon', ':')
  }
  return link
}

export const Config = (label: any) => {
  return { emptyLabel: label, isEmpty: (props: any) => !props }
}

export const getObjectWithEmptyItemsOrder = (obj: any): any => {
  if (Array.isArray(obj[':itemsOrder']) && obj[':itemsOrder'].length === 0) {
    return obj
  }
  for (const key in obj) {
    if (typeof obj[key] === 'object') {
      const result = getObjectWithEmptyItemsOrder(obj[key])

      if (result) {
        return result
      }
    }
  }
  return null
}

export const getSortByKey = (sourceList: any[], key: string, type?: string) => {
  return sourceList.sort(function (a, b) {
    let valueOne = a[key]
    let valueTwo = b[key]

    switch (type) {
      case 'number':
        valueOne = parseInt(valueOne)
        valueTwo = parseInt(valueTwo)
        break
      default:
        valueOne = valueOne.toLowerCase()
        valueTwo = valueTwo.toLowerCase()
        break
    }

    if (valueOne < valueTwo) return -1
    if (valueOne > valueTwo) return 1
    return 0 // No sorting
  })
}

export const appendHtml = (url: string) => {
  if (!url.includes('.html')) {
    return url + '.html'
  }
  return url
}

export const getFormattedZipcode = (val: any) => {
  const numberedInput = val.replace(/\D/g, '')
  let formattedZipcode = ''
  Array.from(numberedInput).forEach((elem, index) => {
    if (index <= 8) {
      formattedZipcode += numberedInput.charAt(index)
      if (index === 4 && numberedInput.length > 5) {
        formattedZipcode += '-'
      }
    }
  })
  return formattedZipcode
}

export const getFormattedPhone = (value: string, previousValue: string) => {
  if (!value) return value
  // Remove any non-digit characters
  let currentValue = value.replace(/\D/g, '')
  if (!currentValue) return ''
  // Ensure it's a valid 10-digit number (for US phone numbers)

  if (currentValue.length <= 10) {
    // Format the phone number as (XXX) XXX-XXXX

    if (currentValue.length > 3 && currentValue.length <= 6) {
      currentValue = `(${currentValue.substring(0, 3)}) ${currentValue.substring(3)}`
    } else if (currentValue.length > 6) {
      currentValue = `(${currentValue.substring(0, 3)}) ${currentValue.substring(3, 6)}-${currentValue.substring(6, 10)}`
    } else {
      currentValue = `(${currentValue}`
    }
    return currentValue
  }

  return previousValue
}

export const extractNumericNumber = (value: string) => {
  return value.replace(/\D/g, '')
}

export const appendHash = (url = '', anchorId: string) => {
  const isHashUrl = url === '/#' || url === '#'
  const isSameUrl = isHashUrl || HREF.includes(url)
  const searchUrl = isSameUrl ? SEARCH : ''
  let anchorUrl = url + searchUrl + `#${anchorId}`
  if (isHashUrl) {
    anchorUrl = searchUrl + `#${anchorId}`
  }
  if (anchorId) {
    return anchorUrl
  }
  return url
}

export const getSpacing = (top?: boolean, right?: boolean, bottom?: boolean, left?: boolean, atomSpacing?: boolean) => {
  const classes: string[] = []

  if (left && right) {
    classes.push('container')
  } else {
    if (right) classes.push('right-spacing')
    if (left) classes.push('left-spacing')
  }

  if (atomSpacing) {
    if (top) classes.push('atom-top-spacing')
    if (bottom) classes.push('atom-bottom-spacing')
  } else {
    if (top) classes.push('top-spacing')
    if (bottom) classes.push('bottom-spacing')
  }

  return classes.join(' ')
}

const parseScriptOptions = {
  replace: (domNode: Element) => {
    if (domNode.type === 'script') {
      return (
        <Helmet>
          <script {...attributesToProps(domNode.attribs)}>{(domNode?.children[0] as Text)?.data}</script>
        </Helmet>
      )
    }
  }
}

export const customParse = (text: string, parseScript = false) => {
  if (text) {
    if (parseScript) {
      return parse(text, parseScriptOptions as HTMLReactParserOptions)
    } else {
      return parse(text)
    }
  }
  return ''
}

export const scrollToElementWithOffset = (e: any, linkUrl: string, tokenAnchor: string, openInNewTab?: boolean) => {
  e.preventDefault()
  const elementId = tokenAnchor || e?.target?.getAttribute('href')?.split('#')[1]
  const stickyNav = document?.querySelector('.react-sticky-container.is-sticky')

  if (openInNewTab) {
    // Open the URL in a new tab and scroll to the target element
    window.open(appendHash(linkUrl, elementId), '_blank')
  } else {
    if (linkUrl !== '/#' && linkUrl !== '#') {
      // Navigate to new URL and scroll to the element
      window.location.href = appendHash(linkUrl, elementId)
    }
    const targetElement = document?.querySelector(`[id="${elementId}"]`)
    if (targetElement) {
      const scrollPosition =
        targetElement.getBoundingClientRect()?.top + window.scrollY - (stickyNav?.getBoundingClientRect()?.height ?? 120)
      // Append hash and scroll on the same page
      if (linkUrl === '/#' || linkUrl === '#') {
        window.location.hash = appendHash(linkUrl as string, elementId)
      }
      window.scrollTo({ top: scrollPosition - 50, behavior: 'smooth' })
    }
  }
}

export const getEspanolLink = () => {
  if (SPANISH_COX_DOMAINS.STAGE.includes(ORIGIN)) {
    return COX_DOMAINS.STAGE
  } else if (SPANISH_COX_DOMAINS.PROD.includes(ORIGIN)) {
    return COX_DOMAINS.PROD
  } else if (COX_DOMAINS.STAGE.includes(ORIGIN)) {
    return SPANISH_COX_DOMAINS.STAGE
  } else {
    return SPANISH_COX_DOMAINS.PROD
  }
}
