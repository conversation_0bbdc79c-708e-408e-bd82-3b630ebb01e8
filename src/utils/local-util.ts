import { COX_DOMAINS, NODE_ENV } from '../constants'

export const getLocalToEnv = () => {
  if (NODE_ENV === 'development') {
    // use below line for access from LOCAL-DEV
    // return COX_DOMAINS.DEV
    // use below line for access from LOCAL-QA1
    // return COX_DOMAINS.QA1
    // use below line for access from LOCAL-QA2
    // return COX_DOMAINS.QA2
    // use below line for access from LOCAL-QALOAD
    //return COX_DOMAINS.QALOAD
    // use below line for access from LOCAL-STAGE
    return COX_DOMAINS.STAGE
    // use below line for access from LOCAL-PROD
    // return COX_DOMAINS.PROD
  }
  return ''
}

export const getImageSrc = (imageSrc = '') => {
  return getLocalToEnv() + imageSrc
}
