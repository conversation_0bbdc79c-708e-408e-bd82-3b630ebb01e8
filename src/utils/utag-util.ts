import Cookie from 'js-cookie'

import { HREF, PATHNAME } from '../constants'
import { AddressServiceabilityStatus, ReactComponents, UDOTypes } from '../types'

function urlParam(name: string) {
  const url = new URL(HREF)
  return url.searchParams.get(name) || ''
}
// Check the given cookie for a value and return it.
const getUDOCookie = (cookieName: string) => {
  return Cookie.get(cookieName)
}

enum ChannelNames {
  RESIDENTIAL = 'cox:res',
  BUSINESS = 'cox:bus',
  ABOUTUS = 'cox:aboutus',
  DEV = 'cox:dev'
}

enum BusinessUnits {
  RESIDENTIAL = 'res',
  BUSINESS = 'bus',
  ABOUTUS = 'aboutus',
  DEV = 'dev'
}

enum UrlSegments {
  RESIDENTIAL = 'residential',
  BUSINESS = 'business',
  ABOUTUS = 'aboutus',
  SUPPORT = 'support',
  MYCONNECTION = 'myconnection',
  DEV = 'dev'
}

const getChannelNameAndBusinessUnit = (urlSegment: string) => {
  if (urlSegment === UrlSegments.RESIDENTIAL) {
    return {
      channelName: ChannelNames.RESIDENTIAL,
      businessUnit: BusinessUnits.RESIDENTIAL
    }
  } else if (urlSegment === UrlSegments.BUSINESS) {
    return {
      channelName: ChannelNames.BUSINESS,
      businessUnit: BusinessUnits.BUSINESS
    }
  } else if (urlSegment === UrlSegments.ABOUTUS) {
    return {
      channelName: ChannelNames.ABOUTUS,
      businessUnit: BusinessUnits.ABOUTUS
    }
  } else {
    return { channelName: ChannelNames.DEV, businessUnit: BusinessUnits.DEV }
  }
}

enum PurchaseSteps {
  SUPPORT = 'support',
  MYCONNECTION = 'myconnection',
  MKTG = 'mktg'
}

const getPurchaseStep = (urlSegment: string, businessUnit: BusinessUnits) => {
  if (urlSegment == UrlSegments.SUPPORT) {
    return {
      purchaseStep: PurchaseSteps.SUPPORT,
      newBusinessUnit: businessUnit + ':' + urlSegment
    }
  } else if (urlSegment == UrlSegments.MYCONNECTION) {
    return { purchaseStep: PurchaseSteps.MYCONNECTION }
  } else {
    return { purchaseStep: PurchaseSteps.MKTG }
  }
}

enum Devices {
  DESKTOP = 'desktop',
  TABLET = 'tablet',
  MOBILE = 'mobile'
}

const getDevice = () => {
  const userAgent = navigator.userAgent
  if (/Mobi|Android/i.test(userAgent)) {
    return Devices.MOBILE
  } else if (/Tablet|iPad/i.test(userAgent)) {
    return Devices.TABLET
  } else {
    return Devices.DESKTOP
  }
}

enum URLParams {
  CAMPAIGNSESSION = 'sc_id',
  PROMOCODE = 'pc',
  ZIP = 'zip',
  CAMPAIGNCODE = 'campcode'
}

enum UDOCookies {
  CAMPAIGNSESSION = 'udo_s_sc_id',
  CAMPAIGNCODE = 'udo_s_campcode',
  ZIP = 'cox-current-zipcode'
}

const setUtagUrlParam = (utag_data: UDOTypes) => {
  if (urlParam(URLParams.CAMPAIGNSESSION) !== '') {
    utag_data.campaign = utag_data.campaignSession = urlParam(URLParams.CAMPAIGNSESSION)
  }

  if (urlParam(URLParams.CAMPAIGNCODE) !== '') {
    utag_data.campaignCodeInternal = utag_data.campaignCodeInternalSession = urlParam(URLParams.CAMPAIGNCODE)
  }

  if (urlParam(URLParams.PROMOCODE) !== '') {
    utag_data.promoCode = urlParam(URLParams.PROMOCODE)
  }
}

const setUtagUdoCookies = (utag_data: UDOTypes) => {
  if (getUDOCookie(UDOCookies.CAMPAIGNSESSION)) {
    utag_data.campaignSession = getUDOCookie(UDOCookies.CAMPAIGNSESSION)
  }
  if (getUDOCookie(UDOCookies.CAMPAIGNCODE)) {
    utag_data.campaignCodeInternalSession = getUDOCookie(UDOCookies.CAMPAIGNCODE)
  }
  if (getUDOCookie(UDOCookies.ZIP)) {
    utag_data.zip = getUDOCookie(UDOCookies.ZIP)
  }
}

const setCookies = (campaignSession: string, campaignCodeInternalSession: string) => {
  if (urlParam(URLParams.CAMPAIGNSESSION) !== '') {
    document.cookie = UDOCookies.CAMPAIGNSESSION + '=' + campaignSession + '; path=/'
  }
  if (urlParam(URLParams.CAMPAIGNCODE) !== '') {
    document.cookie = UDOCookies.CAMPAIGNCODE + '=' + campaignCodeInternalSession + '; path=/'
  }
}

const getPageType = () => {
  const metaPageType = ''
  if (metaPageType != '') {
    return metaPageType
  } else {
    return 'mktg'
  }
}

const getCategoryViewed = () => {
  if (document.querySelectorAll("meta[name='categoryViewed']").length > 0) {
    //@ts-expect-error Element implicitly has 'any' type
    return document.querySelectorAll("meta[name='categoryViewed']")[0]['content'].split(',')
  }
}

const getArticleId = () => {
  if (document.querySelectorAll("meta[name='articleID']").length > 0) {
    return (document.querySelectorAll('meta[name=articleID]')?.[0] as any).content
  }
}

const getLanguage = () => {
  if (PATHNAME.includes('espanol')) {
    return 'es'
  } else {
    return 'en'
  }
}

export const getUtagData = (udo: { [x: string]: any }, pageData?: any) => {
  // TODO: strongly type this with expected fields
  const utag_data: any = window?.utag_data || {}

  // Get data from URL
  const segments = PATHNAME.split('/')
  const { channelName, businessUnit } = getChannelNameAndBusinessUnit(segments[1])
  const { purchaseStep, newBusinessUnit } = getPurchaseStep(segments[2], businessUnit)

  let pageName, subSection, newChannelName
  pageName = subSection = channelName
  // Use URL segments to build up pageName, subSection, and channelName.
  for (let i = 2; i < segments.length; i++) {
    pageName = pageName + ':' + segments[i]
  }
  for (let i = 2; i < segments.length - 1; i++) {
    subSection = subSection + ':' + segments[i]
  }
  if (segments.length > 3) {
    newChannelName = channelName + ':' + segments[2]
  }
  pageName = pageName?.substring(0, pageName.length - 5)

  utag_data.channel = newChannelName ?? channelName
  utag_data.businessUnit = newBusinessUnit ?? businessUnit
  utag_data.purchaseStep = purchaseStep
  utag_data.pageName = pageName
  utag_data.subSection = subSection
  utag_data.responsiveDisplayType = getDevice()

  setUtagUrlParam(utag_data)
  setUtagUdoCookies(utag_data)

  // data attr that get data from elements on the page
  utag_data.categoryViewed = getCategoryViewed()
  utag_data.articleID = getArticleId()

  utag_data.pageType = getPageType()
  utag_data.language = getLanguage()
  utag_data.dateStamp = new Date().getTime()

  setCookies(utag_data.campaignSession, utag_data.campaignCodeInternalSession)

  if (pageData?.rulesEvaluated) {
    utag_data.rulesEvaluated = pageData?.rulesEvaluated
  }

  // Get UDO data from Context
  // Loop through the data and set the utag_data
  if (udo) {
    Object.keys(udo).forEach(function (key) {
      utag_data[key] = udo[key]
    })
  }

  return utag_data
}

export const loadScript = ({ url = '', id = '', isAsync = true, onload = null, isDefer = false, reload = false }: any) => {
  try {
    if (!reload || !document.getElementById(id)) {
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.id = id
      script.async = isAsync
      script.defer = isDefer
      script.src = url
      if (onload) {
        script.onload = onload
      }

      document.getElementsByTagName('head')[0].appendChild(script)
      console.info('Script loaded', id)
    }
  } catch (error) {
    console.error(error)
  }
}

export const updateTealiumStatus = (compName: string, status: string) => {
  if (typeof window?.utag != 'undefined') {
    if (ReactComponents.REACT_ADDRESSCAPTURE === compName) {
      switch (status) {
        case 'SERVICEABLE-COMING-SOON':
          triggerUtagLink('addressComingSoon')
          break
        case AddressServiceabilityStatus.serviceable:
          if (window?.utag_data?.serviceableCodes) {
            if (
              window?.utag_data.serviceableCodes.match(/ME0122/gi) != null &&
              window?.utag_data.serviceableCodes.match(/INTERNET_DE/gi) != null
            ) {
              triggerUtagLink('addressComingSoon')
            } else {
              triggerUtagLink('addressServiceableNotActive')
            }
          } else {
            triggerUtagLink('addressServiceableNotActive')
          }
          break
        case AddressServiceabilityStatus.serviceableActive:
          triggerUtagLink('addressServiceableActive')
          break
        case AddressServiceabilityStatus.exceedsThreshold:
        case AddressServiceabilityStatus.multiple:
          triggerUtagLink('addressMatchMultiple')
          break
        case AddressServiceabilityStatus.nonServiceable:
        case AddressServiceabilityStatus.unknown:
          triggerUtagLink('addressNotServiceable')
          break
        default:
          break
      }
    }
  }
}

export const triggerUtagLink = (eventName: string, eventInfo?: { [x: string]: any }) => {
  const udoEvent: any = { eventNames: eventName }
  if (eventInfo) {
    Object.keys(eventInfo).forEach(function (key) {
      udoEvent[key] = eventInfo[key]
    })
  }
  if (window?.utag?.link) {
    window?.utag?.link(udoEvent)
  }
}

export const setUtagData = (udo: any, page: any) => {
  window.utag_data = getUtagData(udo, page)
}
