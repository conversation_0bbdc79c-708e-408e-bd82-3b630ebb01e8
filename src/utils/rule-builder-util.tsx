import React from 'react'

import parse from 'html-react-parser'
import Cookies from 'js-cookie'

import { Flexoffers, RuleBuilderPayload } from '../types'
// import RuleBuilderConditional from '../components/RuleBuilderConditional'

export const getPayloadByRuleExpression = (ruleExpression: string) => {
  if (!ruleExpression) return {}
  return ruleExpression?.split(' && ')?.reduce((acc: { [key: string]: string | number | boolean }, item: string) => {
    const [key, value] = item.split(':')
    acc[key] = isNaN(+value) ? value?.trim() : parseInt(value)
    acc[key] = typeof acc[key] === 'string' && (acc[key] as string)?.toLowerCase() === 'true' ? true : acc[key]
    acc[key] = typeof acc[key] === 'string' && (acc[key] as string)?.toLowerCase() === 'false' ? false : acc[key]
    return acc
  }, {})
}

export const getPayLoadByOfferType = (payload: RuleBuilderPayload) => {
  const {
    customerType,
    appcontext,
    includeExactServices,
    maxOffers,
    intent,
    products,
    includeSubscribedServiceCategories,
    offerIds,
    lob
  } = payload
  let payLoadObj = {}
  switch (customerType) {
    case 'pinpoint':
      payLoadObj = {
        appcontext: appcontext,
        includeExactServices: includeExactServices,
        maxOffers: maxOffers,
        intent: intent,
        products: (products as string)?.split(','),
        includeSubscribedServiceCategories: includeSubscribedServiceCategories
      }
      break
    case 'customer':
      payLoadObj = {
        eligibility: {
          unChecked: {
            offerIds: offerIds
          }
        },
        lob: lob
      }
      break
    case 'prospect':
      payLoadObj = {
        offerIds: offerIds,
        lob: lob,
        zipcode: Cookies.get('cox-current-zipcode')
      }
      break
    default:
      payLoadObj = {}
  }
  return payLoadObj
}

export const getFlexOffersLength = (flexOffers: any) => {
  return flexOffers?.offers?.length || flexOffers?.cinfos?.length || 0
}

export const removeEmptyCF = (contentFragments: any[]) => {
  const refinedCF = []
  for (let i = 0; i < contentFragments?.length; i++) {
    if (contentFragments[i]?.elements && Object.keys(contentFragments[i].elements)?.length > 0) {
      refinedCF.push(contentFragments[i])
    }
  }
  return refinedCF
}

export const getOffersLength = (maxSlides: any, flexOffers: any) => {
  let flexOffersLength: number = getFlexOffersLength(flexOffers)

  // Note: When authors leave maxSlides blank, it is being passed as 0
  const numOfSlides = maxSlides && maxSlides != 0 ? maxSlides : null

  if (numOfSlides) {
    // Validation check here: If authors pass a maxSlides number that exceeds the actual flexOffersLength:
    flexOffersLength = numOfSlides > flexOffersLength ? flexOffersLength : numOfSlides
  }

  return flexOffersLength
}

export const escapeJsonString = (jsonString: string) => {
  if (!jsonString) return ''
  if (typeof jsonString === 'number') return jsonString
  // Regular expression to find unescaped double quotes and backslashes
  const unescapedQuoteRegex = /(?<!\\)"/g

  let escapedJsonString = jsonString.replace(unescapedQuoteRegex, '\\"')

  escapedJsonString = escapedJsonString.replace(/(?:\r\n|\r|\n)/g, '<br>')

  return escapedJsonString
}

export const getNestedValue = (
  inputStringWithTokens: string,
  response?: Flexoffers,
  index = 0,
  parsingRequired = true,
  getObjectAsString = false,
  isJsonString = false
) => {
  if (!response || response?.loading || !inputStringWithTokens) return ''
  const tokenRegex = /\[\[(.*?)\]\]/g

  const tokenReplacedString = inputStringWithTokens?.replace(tokenRegex, (match, token) => {
    const value = getNestedValueForKey(token, response, index, getObjectAsString)

    if (isJsonString) {
      return escapeJsonString(value) ?? ''
    }

    return value ?? ''
  })

  if (!parsingRequired) {
    return tokenReplacedString
  }
  if (countTextChars(tokenReplacedString) === 0) {
    return ''
  }
  return tokenReplacedString ? parse(tokenReplacedString) : ''
}
export const getNestedValueForKey = (key: string, response: Flexoffers, index = 0, getObjectAsString = false) => {
  try {
    let result = ''
    const keyArr = key?.split('.')
    const propertyToRead = (keyArr[0] + 's') as 'discounts' | 'offers' | 'cinfos'
    const valueObject = response?.[propertyToRead][index]
    const keyTofind = keyArr?.[keyArr.length - 1]
    // const checkIfObjectKey = !keyArr.some((c) => /^\d+$/.test(c))
    const checkIfObjectKey = keyArr?.some((currentKey) => !isNaN(parseInt(currentKey)))

    if (checkIfObjectKey || propertyToRead === 'cinfos') {
      result = getPropertyValueByKey(valueObject, keyArr?.slice(1))
    } else {
      result = getObject(valueObject, keyTofind, getObjectAsString)
    }

    return result
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return ''
  }
}

export const getPropertyValueByKey = (valueObject: any, keyArr: string[]) => {
  let value = valueObject
  for (let i = 0; i < keyArr.length; i++) {
    const prop = keyArr[i]

    if (/\[(\d+)\]/.test(prop)) {
      const index = prop?.match(/\[(\d+)\]/)?.[1]
      value = index ? value[prop.replace(/\[\d+\]/, '')]?.[index] : ''
    } else {
      value = value[prop]
    }
  }

  return value
}

export const getOrderByFlexOffers = (offers: any, offerListIds: any) => {
  if (offers && offerListIds) {
    const offerListIdCollection = offerListIds.toString().split(',')
    if (offerListIdCollection.length > 0 && offers && offers.length > 0) {
      const newOfferList = []
      for (let i = 0; i < offerListIdCollection.length; i++) {
        const currentOffer = offers.filter((offer: any) => offer.id.includes(offerListIdCollection[i]))
        if (currentOffer && currentOffer.length > 0) {
          newOfferList.push(currentOffer[0])
        }
      }
      if (newOfferList && newOfferList.length > 0) {
        return newOfferList
      }
    }
  }
  return offers
}

export function getObject(obj: any, key: any, getObjectAsString: boolean) {
  const queue = [obj]

  while (queue.length > 0) {
    const currObj = queue.shift()
    if (key in currObj) {
      if (typeof currObj[key] === 'object') {
        if (getObjectAsString) return JSON.stringify(currObj[key]).replace(/"/g, '\\"')
        return JSON.stringify(currObj[key])
      }
      return currObj[key]
    }
    if (currObj?.name === key) {
      return currObj['value']
    }

    for (const childKey in currObj) {
      const child = currObj[childKey]

      if (Array.isArray(child)) {
        for (const element of child) {
          if (typeof element === 'object' && element !== null) {
            queue.push(element)
          }
        }
      } else if (typeof child === 'object' && child !== null) {
        queue.push(child)
      }
    }
  }

  return ''
}

export const isDynamicValue = (key: string) => {
  const pattern = /\[\[|\]\]/g
  return pattern.test(key)
}

export const hasCFConditionals = (attribute: any) => {
  return attribute?.dataType === 'content-fragment' || !!attribute?.multiValue
}

export const getConditionResult = (condition: string | number | boolean, data: Flexoffers, index = 0) => {
  let conditionResult = false
  const conditionString = getNestedValue(condition as string, data, index) as string
  if (conditionString) {
    try {
      conditionResult = eval(conditionString)
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      conditionResult = false
    }
  }
  return conditionResult
}

export function convertToObject(str: string) {
  const unescapedStr = str.replace(/\\r/g, '')
  const obj = unescapedStr && JSON.parse(unescapedStr)
  const outObject: any = {}
  for (const [key, value] of Object.entries(obj)) {
    const values = (value as string).trim().split('\n')
    const result = values.reduce((acc: any, item) => {
      const [key, value] = splitByFirstEqualToArray(item)
      acc[key.trim()] = value.trim()
      return acc
    }, {})

    outObject[key] = result
  }
  return outObject
}

export const splitByFirstEqualToArray = (str: any) => {
  const indexOfFirstEqual = str && str.indexOf('=')
  if (indexOfFirstEqual === -1) {
    return [str, ''] // Handle case with no '=' in string
  }
  const key = str.substring(0, indexOfFirstEqual)
  const value = str.substring(indexOfFirstEqual + 1)
  return [key, value]
}

export function getTokenValues(text = '', tokenProperties = '', location = '') {
  try {
    if (isDynamicValue(text) && tokenProperties && tokenProperties?.length > 0) {
      const tokenResponse: any = { token: convertToObject(tokenProperties) }
      const tokenRegex = /\[\[(.*?)\]\]/g
      const tokenLocation = location || Cookies.get('cox-current-site') || 'global'
      const tokenReplacedString = text?.replace(tokenRegex, (match: any, token: any) => {
        const locationValue = getNestedValueForNestedKey(tokenResponse, `${token}.${tokenLocation}`)
        const globalValue = getNestedValueForNestedKey(tokenResponse, `${token}.${'global'}`)
        return locationValue || globalValue || match
      })
      return tokenReplacedString && parse(tokenReplacedString)
    }
    return text?.length > 0 ? parse(text) : ''
  } catch (err) {
    console.error(err)
  }
}

export const extractText = (node: any): any => {
  if (typeof node === 'string') {
    return node
  }
  if (Array.isArray(node)) {
    return node.map((element: any) => extractText(element)).join('')
  }

  if (React.isValidElement(node)) {
    const children = React.Children.toArray((node as any).props.children)

    return children.map((child: any) => extractText(child)).join('')
  }

  return ''
}

export function getNestedValueForNestedKey(obj: any, keys: string) {
  const keyArray = keys.split('.')
  let value = obj

  for (const key of keyArray) {
    if (key in value) {
      value = value[key]
    } else {
      return undefined // Key does not exist in the object
    }
  }

  return value
}

export const getAttributeValue = (
  attribute: any,
  flexOffers: any,
  index: any,
  tokenProperties: any,
  propertyName?: string,
  isArray?: boolean,
  propertyKey = 'displaytext'
  // isComponent = false
) => {
  let attributeValue = attribute?.value || ''
  if (attribute?.dataType === 'boolean' || typeof attributeValue === 'boolean') return attributeValue
  else if (
    typeof attribute?.dataType === 'string' &&
    isArray &&
    (!attribute?.value?.includes('ruleExpression') || (Array.isArray(attribute?.value) && attribute?.value?.length > 0))
  ) {
    return attribute?.value && JSON.parse(attribute?.value)
  } else if (typeof attribute === 'string') {
    attributeValue = attribute
  } else if (
    typeof attribute?.dataType === 'string' &&
    (attribute?.value?.includes('ruleExpression') || (Array.isArray(attribute?.value) && attribute?.value?.length > 0))
  ) {
    try {
      let conditionals = attributeValue
      if (typeof attributeValue === 'string') {
        conditionals = attributeValue && JSON.parse(attributeValue)
      }
      // if (isComponent) {
      //   return (
      //     <RuleBuilderConditional
      //       conditionals={conditionals}
      //       flexOffers={flexOffers}
      //       index={index}
      //       title={attribute?.title}
      //     />
      //   )
      // }
      const conditionalDynamicValue = getTextByConditionals(
        conditionals,
        flexOffers,
        index,
        propertyKey
        // DEBUG: Below line is used for debugging only
        // attribute?.title,
        // propertyName
      )
      return getDynamicValue(conditionalDynamicValue, flexOffers, index, tokenProperties) || ''
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (err) {
      const errorMessage = `Check ${propertyName}`
      console.error(errorMessage)
      return <div style={{ color: 'red' }}>. . . . .</div>
    }
  }
  return getDynamicValue(attributeValue, flexOffers, index, tokenProperties)
}

export const countTextChars = (htmlString: string) => {
  const parser = new DOMParser()
  const doc = parser.parseFromString(htmlString, 'text/html')
  const textContent = doc.body.textContent?.replace(/\n/g, '')
  return textContent?.length
}

export const getDynamicValue = (attributeValue: any, flexOffers: any, index: any, tokenProperties: any) => {
  if (attributeValue && isDynamicValue(attributeValue)) {
    if (attributeValue.includes('token.')) {
      return getTokenValues(attributeValue, tokenProperties)
    }
    return getNestedValue(attributeValue, flexOffers, index)
  }
  if (countTextChars(attributeValue) === 0) return ''
  return attributeValue && typeof attributeValue === 'string' && parse(attributeValue)
}

export const getTextByConditionals = (
  conditionals: any[],
  flexOffers: Flexoffers,
  index: number,
  propertyKey = 'displaytext'
  // DEBUG: Below line is used for debugging only
  // title = '',
  // propertyName?: string
) => {
  const elseBlock = conditionals?.find((condition: any) => condition.else === 'yes')

  let conditionResult = elseBlock?.displaytext
  conditionals?.some((condition: any) => {
    const { elseBlock, ruleExpression } = condition
    if (elseBlock === true) return null
    let result
    if (ruleExpression.includes('udo')) {
      result = true
    } else {
      result = getConditionResult(ruleExpression, flexOffers, index)
    }
    if (result) {
      // DEBUG: Below line is used for debugging only
      // if (!isProd.includes(origin)) {
      //   console.info('🚀', index, '-->', title, '-->', propertyName, '-->', ruleExpression)
      // }
      conditionResult = condition?.[propertyKey]
      /* Need to return the accordionIcon and alttext here */
      return true
    }
    return false
  })
  return conditionResult
}
