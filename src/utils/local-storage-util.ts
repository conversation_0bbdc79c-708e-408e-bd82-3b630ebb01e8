export const getCacheItem = function (key: any) {
  return typeof window !== 'undefined' ? window.localStorage.getItem(key) : null
}

export const setCacheItem = function (key: any, value: any) {
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  typeof window !== 'undefined' && window.localStorage.setItem(key, value)
}
export const removeCacheItem = function (key: any) {
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  typeof window !== 'undefined' && window.localStorage.removeItem(key)
}

export const setLocalStorage = (key: string, value: string) => {
  window.localStorage.setItem(key, value)
}

export const getLocalStorage = (key: string) => {
  return window.localStorage.getItem(key)
}

export const clearLocalStorage = (key: string) => {
  window.localStorage.removeItem(key)
}
