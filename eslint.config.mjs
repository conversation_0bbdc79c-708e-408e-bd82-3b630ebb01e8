import { defineConfig, globalIgnores } from 'eslint/config'
import tsPlugin from '@typescript-eslint/eslint-plugin'
import tsParser from '@typescript-eslint/parser'
import reactPlugin from 'eslint-plugin-react'
import reactHooksPlugin from 'eslint-plugin-react-hooks'
import testingLibraryPlugin from 'eslint-plugin-testing-library'
import prettierPlugin from 'eslint-plugin-prettier'
import importPlugin from 'eslint-plugin-import'
import prettierConfig from 'eslint-config-prettier'
import globals from 'globals'
import js from '@eslint/js'

// Construct the flat config using pure ESLint v9 approach
export default defineConfig([
  // Ignore files (formerly .eslintignore)
  globalIgnores(['**/dist/**', '**/node_modules/**', '**/.git/**', '**/coverage/**', '**/storybook-static/**']),

  // Base JS configuration from ESLint
  js.configs.recommended,

  // Global rules to apply to all files
  {
    rules: {
      'no-unreachable': 'warn' // Downgrade unreachable code to warnings
    }
  },

  // Add TypeScript configuration
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        ecmaVersion: 2021,
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true
        }
        // Removed project reference to avoid tsconfig path issues
      },
      globals: {
        NodeListOf: 'readonly' // Add NodeListOf to globals
      }
    },
    plugins: {
      '@typescript-eslint': tsPlugin
    },
    rules: {
      ...tsPlugin.configs.recommended.rules,
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-empty-function': 'off',
      '@typescript-eslint/no-unused-expressions': 'warn', // Downgrade to warning
      '@typescript-eslint/no-require-imports': 'warn', // Downgrade to warning
      'no-undef': 'off', // TypeScript handles this better
      'no-redeclare': 'off', // TypeScript handles this better
      '@typescript-eslint/no-redeclare': 'warn' // Use TypeScript's version but warn only
    }
  },

  // React configuration
  {
    files: ['**/*.jsx', '**/*.tsx'],
    plugins: {
      react: reactPlugin
    },
    languageOptions: {
      parserOptions: {
        ecmaFeatures: {
          jsx: true
        }
      },
      globals: {
        React: 'readonly', // Make React globally available
        JSX: 'readonly' // Make JSX globally available
      }
    },
    settings: {
      react: {
        version: 'detect'
      }
    },
    rules: {
      ...reactPlugin.configs.recommended.rules,
      'react/jsx-uses-react': 'error',
      'react/jsx-uses-vars': 'error',
      'react/react-in-jsx-scope': 'off', // Disable the rule requiring React import with JSX
      'react/prop-types': 'off', // Disable prop-types validation (TypeScript handles this)
      'react/display-name': 'off', // Disable display name requirement
      'react/no-unescaped-entities': 'off', // Allow unescaped entities in JSX
      'react/no-unknown-property': 'off', // Disable unknown properties check
      'react/jsx-key': 'warn' // Downgrade missing key props to warnings
    }
  },

  // React Hooks configuration
  {
    files: ['**/*.jsx', '**/*.tsx'],
    plugins: {
      'react-hooks': reactHooksPlugin
    },
    rules: {
      'react-hooks/rules-of-hooks': 'warn', // Downgrade to warning during migration
      'react-hooks/exhaustive-deps': 'off' // Temporarily disable to reduce noise during migration
    }
  },

  // Testing Library configuration
  {
    files: ['**/__tests__/**/*.[jt]s?(x)', '**/?(*.)+(spec|test).[jt]s?(x)'],
    plugins: {
      'testing-library': testingLibraryPlugin
    },
    rules: {
      // Use simplified testing-library rules to avoid conflicts
      'testing-library/no-debugging-utils': 'warn'
    }
  },

  // Import plugin configuration
  {
    plugins: {
      import: importPlugin
    },
    rules: {
      'sort-imports': [
        'warn',
        {
          ignoreCase: true,
          ignoreDeclarationSort: true
        }
      ],
      'import/order': [
        'warn',
        {
          groups: [['external', 'builtin'], 'internal', ['sibling', 'parent'], 'index'],
          pathGroups: [
            {
              pattern: '@(react|react-native)',
              group: 'external',
              position: 'before'
            },
            {
              pattern: '@src/**',
              group: 'internal'
            }
          ],
          pathGroupsExcludedImportTypes: ['internal', 'react'],
          'newlines-between': 'always',
          alphabetize: {
            order: 'asc',
            caseInsensitive: true
          }
        }
      ]
    }
  },

  // Prettier configuration
  {
    plugins: {
      prettier: prettierPlugin
    },
    rules: {
      'prettier/prettier': [
        'warn',
        {
          singleQuote: true,
          jsxSingleQuote: true,
          printWidth: 125,
          semi: false,
          tabWidth: 2,
          trailingComma: 'none',
          endOfLine: 'auto'
        }
      ]
    }
  },

  // Apply prettier config at the end to avoid conflicts
  prettierConfig,

  // Add global environment configuration
  {
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.browser,
        ...globals.commonjs,
        ...globals.jest,
        process: true,
        google: 'readonly'
      }
    }
  }
])
